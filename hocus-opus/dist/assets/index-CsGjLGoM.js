var Iy=Object.defineProperty;var tg=(f,i,c)=>i in f?Iy(f,i,{enumerable:!0,configurable:!0,writable:!0,value:c}):f[i]=c;var Ah=(f,i,c)=>tg(f,typeof i!="symbol"?i+"":i,c);(function(){const i=document.createElement("link").relList;if(i&&i.supports&&i.supports("modulepreload"))return;for(const o of document.querySelectorAll('link[rel="modulepreload"]'))s(o);new MutationObserver(o=>{for(const m of o)if(m.type==="childList")for(const E of m.addedNodes)E.tagName==="LINK"&&E.rel==="modulepreload"&&s(E)}).observe(document,{childList:!0,subtree:!0});function c(o){const m={};return o.integrity&&(m.integrity=o.integrity),o.referrerPolicy&&(m.referrerPolicy=o.referrerPolicy),o.crossOrigin==="use-credentials"?m.credentials="include":o.crossOrigin==="anonymous"?m.credentials="omit":m.credentials="same-origin",m}function s(o){if(o.ep)return;o.ep=!0;const m=c(o);fetch(o.href,m)}})();var cf={exports:{}},Zn={};/**
 * @license React
 * react-jsx-runtime.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Rh;function eg(){if(Rh)return Zn;Rh=1;var f=Symbol.for("react.transitional.element"),i=Symbol.for("react.fragment");function c(s,o,m){var E=null;if(m!==void 0&&(E=""+m),o.key!==void 0&&(E=""+o.key),"key"in o){m={};for(var O in o)O!=="key"&&(m[O]=o[O])}else m=o;return o=m.ref,{$$typeof:f,type:s,key:E,ref:o!==void 0?o:null,props:m}}return Zn.Fragment=i,Zn.jsx=c,Zn.jsxs=c,Zn}var Oh;function ag(){return Oh||(Oh=1,cf.exports=eg()),cf.exports}var T=ag(),ff={exports:{}},W={};/**
 * @license React
 * react.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Nh;function lg(){if(Nh)return W;Nh=1;var f=Symbol.for("react.transitional.element"),i=Symbol.for("react.portal"),c=Symbol.for("react.fragment"),s=Symbol.for("react.strict_mode"),o=Symbol.for("react.profiler"),m=Symbol.for("react.consumer"),E=Symbol.for("react.context"),O=Symbol.for("react.forward_ref"),w=Symbol.for("react.suspense"),x=Symbol.for("react.memo"),U=Symbol.for("react.lazy"),K=Symbol.iterator;function k(y){return y===null||typeof y!="object"?null:(y=K&&y[K]||y["@@iterator"],typeof y=="function"?y:null)}var H={isMounted:function(){return!1},enqueueForceUpdate:function(){},enqueueReplaceState:function(){},enqueueSetState:function(){}},L=Object.assign,bt={};function lt(y,D,B){this.props=y,this.context=D,this.refs=bt,this.updater=B||H}lt.prototype.isReactComponent={},lt.prototype.setState=function(y,D){if(typeof y!="object"&&typeof y!="function"&&y!=null)throw Error("takes an object of state variables to update or a function which returns an object of state variables.");this.updater.enqueueSetState(this,y,D,"setState")},lt.prototype.forceUpdate=function(y){this.updater.enqueueForceUpdate(this,y,"forceUpdate")};function St(){}St.prototype=lt.prototype;function gt(y,D,B){this.props=y,this.context=D,this.refs=bt,this.updater=B||H}var ut=gt.prototype=new St;ut.constructor=gt,L(ut,lt.prototype),ut.isPureReactComponent=!0;var pt=Array.isArray,F={H:null,A:null,T:null,S:null,V:null},zt=Object.prototype.hasOwnProperty;function Ut(y,D,B,j,X,it){return B=it.ref,{$$typeof:f,type:y,key:D,ref:B!==void 0?B:null,props:it}}function Pt(y,D){return Ut(y.type,D,void 0,void 0,void 0,y.props)}function Wt(y){return typeof y=="object"&&y!==null&&y.$$typeof===f}function el(y){var D={"=":"=0",":":"=2"};return"$"+y.replace(/[=:]/g,function(B){return D[B]})}var Xe=/\/+/g;function Lt(y,D){return typeof y=="object"&&y!==null&&y.key!=null?el(""+y.key):D.toString(36)}function za(){}function Ua(y){switch(y.status){case"fulfilled":return y.value;case"rejected":throw y.reason;default:switch(typeof y.status=="string"?y.then(za,za):(y.status="pending",y.then(function(D){y.status==="pending"&&(y.status="fulfilled",y.value=D)},function(D){y.status==="pending"&&(y.status="rejected",y.reason=D)})),y.status){case"fulfilled":return y.value;case"rejected":throw y.reason}}throw y}function Zt(y,D,B,j,X){var it=typeof y;(it==="undefined"||it==="boolean")&&(y=null);var J=!1;if(y===null)J=!0;else switch(it){case"bigint":case"string":case"number":J=!0;break;case"object":switch(y.$$typeof){case f:case i:J=!0;break;case U:return J=y._init,Zt(J(y._payload),D,B,j,X)}}if(J)return X=X(y),J=j===""?"."+Lt(y,0):j,pt(X)?(B="",J!=null&&(B=J.replace(Xe,"$&/")+"/"),Zt(X,D,B,"",function(ua){return ua})):X!=null&&(Wt(X)&&(X=Pt(X,B+(X.key==null||y&&y.key===X.key?"":(""+X.key).replace(Xe,"$&/")+"/")+J)),D.push(X)),1;J=0;var ue=j===""?".":j+":";if(pt(y))for(var _t=0;_t<y.length;_t++)j=y[_t],it=ue+Lt(j,_t),J+=Zt(j,D,B,it,X);else if(_t=k(y),typeof _t=="function")for(y=_t.call(y),_t=0;!(j=y.next()).done;)j=j.value,it=ue+Lt(j,_t++),J+=Zt(j,D,B,it,X);else if(it==="object"){if(typeof y.then=="function")return Zt(Ua(y),D,B,j,X);throw D=String(y),Error("Objects are not valid as a React child (found: "+(D==="[object Object]"?"object with keys {"+Object.keys(y).join(", ")+"}":D)+"). If you meant to render a collection of children, use an array instead.")}return J}function A(y,D,B){if(y==null)return y;var j=[],X=0;return Zt(y,j,"","",function(it){return D.call(B,it,X++)}),j}function q(y){if(y._status===-1){var D=y._result;D=D(),D.then(function(B){(y._status===0||y._status===-1)&&(y._status=1,y._result=B)},function(B){(y._status===0||y._status===-1)&&(y._status=2,y._result=B)}),y._status===-1&&(y._status=0,y._result=D)}if(y._status===1)return y._result.default;throw y._result}var Z=typeof reportError=="function"?reportError:function(y){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var D=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof y=="object"&&y!==null&&typeof y.message=="string"?String(y.message):String(y),error:y});if(!window.dispatchEvent(D))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",y);return}console.error(y)};function mt(){}return W.Children={map:A,forEach:function(y,D,B){A(y,function(){D.apply(this,arguments)},B)},count:function(y){var D=0;return A(y,function(){D++}),D},toArray:function(y){return A(y,function(D){return D})||[]},only:function(y){if(!Wt(y))throw Error("React.Children.only expected to receive a single React element child.");return y}},W.Component=lt,W.Fragment=c,W.Profiler=o,W.PureComponent=gt,W.StrictMode=s,W.Suspense=w,W.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=F,W.__COMPILER_RUNTIME={__proto__:null,c:function(y){return F.H.useMemoCache(y)}},W.cache=function(y){return function(){return y.apply(null,arguments)}},W.cloneElement=function(y,D,B){if(y==null)throw Error("The argument must be a React element, but you passed "+y+".");var j=L({},y.props),X=y.key,it=void 0;if(D!=null)for(J in D.ref!==void 0&&(it=void 0),D.key!==void 0&&(X=""+D.key),D)!zt.call(D,J)||J==="key"||J==="__self"||J==="__source"||J==="ref"&&D.ref===void 0||(j[J]=D[J]);var J=arguments.length-2;if(J===1)j.children=B;else if(1<J){for(var ue=Array(J),_t=0;_t<J;_t++)ue[_t]=arguments[_t+2];j.children=ue}return Ut(y.type,X,void 0,void 0,it,j)},W.createContext=function(y){return y={$$typeof:E,_currentValue:y,_currentValue2:y,_threadCount:0,Provider:null,Consumer:null},y.Provider=y,y.Consumer={$$typeof:m,_context:y},y},W.createElement=function(y,D,B){var j,X={},it=null;if(D!=null)for(j in D.key!==void 0&&(it=""+D.key),D)zt.call(D,j)&&j!=="key"&&j!=="__self"&&j!=="__source"&&(X[j]=D[j]);var J=arguments.length-2;if(J===1)X.children=B;else if(1<J){for(var ue=Array(J),_t=0;_t<J;_t++)ue[_t]=arguments[_t+2];X.children=ue}if(y&&y.defaultProps)for(j in J=y.defaultProps,J)X[j]===void 0&&(X[j]=J[j]);return Ut(y,it,void 0,void 0,null,X)},W.createRef=function(){return{current:null}},W.forwardRef=function(y){return{$$typeof:O,render:y}},W.isValidElement=Wt,W.lazy=function(y){return{$$typeof:U,_payload:{_status:-1,_result:y},_init:q}},W.memo=function(y,D){return{$$typeof:x,type:y,compare:D===void 0?null:D}},W.startTransition=function(y){var D=F.T,B={};F.T=B;try{var j=y(),X=F.S;X!==null&&X(B,j),typeof j=="object"&&j!==null&&typeof j.then=="function"&&j.then(mt,Z)}catch(it){Z(it)}finally{F.T=D}},W.unstable_useCacheRefresh=function(){return F.H.useCacheRefresh()},W.use=function(y){return F.H.use(y)},W.useActionState=function(y,D,B){return F.H.useActionState(y,D,B)},W.useCallback=function(y,D){return F.H.useCallback(y,D)},W.useContext=function(y){return F.H.useContext(y)},W.useDebugValue=function(){},W.useDeferredValue=function(y,D){return F.H.useDeferredValue(y,D)},W.useEffect=function(y,D,B){var j=F.H;if(typeof B=="function")throw Error("useEffect CRUD overload is not enabled in this build of React.");return j.useEffect(y,D)},W.useId=function(){return F.H.useId()},W.useImperativeHandle=function(y,D,B){return F.H.useImperativeHandle(y,D,B)},W.useInsertionEffect=function(y,D){return F.H.useInsertionEffect(y,D)},W.useLayoutEffect=function(y,D){return F.H.useLayoutEffect(y,D)},W.useMemo=function(y,D){return F.H.useMemo(y,D)},W.useOptimistic=function(y,D){return F.H.useOptimistic(y,D)},W.useReducer=function(y,D,B){return F.H.useReducer(y,D,B)},W.useRef=function(y){return F.H.useRef(y)},W.useState=function(y){return F.H.useState(y)},W.useSyncExternalStore=function(y,D,B){return F.H.useSyncExternalStore(y,D,B)},W.useTransition=function(){return F.H.useTransition()},W.version="19.1.0",W}var wh;function Of(){return wh||(wh=1,ff.exports=lg()),ff.exports}var Jt=Of(),rf={exports:{}},Vn={},of={exports:{}},df={};/**
 * @license React
 * scheduler.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Dh;function ng(){return Dh||(Dh=1,function(f){function i(A,q){var Z=A.length;A.push(q);t:for(;0<Z;){var mt=Z-1>>>1,y=A[mt];if(0<o(y,q))A[mt]=q,A[Z]=y,Z=mt;else break t}}function c(A){return A.length===0?null:A[0]}function s(A){if(A.length===0)return null;var q=A[0],Z=A.pop();if(Z!==q){A[0]=Z;t:for(var mt=0,y=A.length,D=y>>>1;mt<D;){var B=2*(mt+1)-1,j=A[B],X=B+1,it=A[X];if(0>o(j,Z))X<y&&0>o(it,j)?(A[mt]=it,A[X]=Z,mt=X):(A[mt]=j,A[B]=Z,mt=B);else if(X<y&&0>o(it,Z))A[mt]=it,A[X]=Z,mt=X;else break t}}return q}function o(A,q){var Z=A.sortIndex-q.sortIndex;return Z!==0?Z:A.id-q.id}if(f.unstable_now=void 0,typeof performance=="object"&&typeof performance.now=="function"){var m=performance;f.unstable_now=function(){return m.now()}}else{var E=Date,O=E.now();f.unstable_now=function(){return E.now()-O}}var w=[],x=[],U=1,K=null,k=3,H=!1,L=!1,bt=!1,lt=!1,St=typeof setTimeout=="function"?setTimeout:null,gt=typeof clearTimeout=="function"?clearTimeout:null,ut=typeof setImmediate<"u"?setImmediate:null;function pt(A){for(var q=c(x);q!==null;){if(q.callback===null)s(x);else if(q.startTime<=A)s(x),q.sortIndex=q.expirationTime,i(w,q);else break;q=c(x)}}function F(A){if(bt=!1,pt(A),!L)if(c(w)!==null)L=!0,zt||(zt=!0,Lt());else{var q=c(x);q!==null&&Zt(F,q.startTime-A)}}var zt=!1,Ut=-1,Pt=5,Wt=-1;function el(){return lt?!0:!(f.unstable_now()-Wt<Pt)}function Xe(){if(lt=!1,zt){var A=f.unstable_now();Wt=A;var q=!0;try{t:{L=!1,bt&&(bt=!1,gt(Ut),Ut=-1),H=!0;var Z=k;try{e:{for(pt(A),K=c(w);K!==null&&!(K.expirationTime>A&&el());){var mt=K.callback;if(typeof mt=="function"){K.callback=null,k=K.priorityLevel;var y=mt(K.expirationTime<=A);if(A=f.unstable_now(),typeof y=="function"){K.callback=y,pt(A),q=!0;break e}K===c(w)&&s(w),pt(A)}else s(w);K=c(w)}if(K!==null)q=!0;else{var D=c(x);D!==null&&Zt(F,D.startTime-A),q=!1}}break t}finally{K=null,k=Z,H=!1}q=void 0}}finally{q?Lt():zt=!1}}}var Lt;if(typeof ut=="function")Lt=function(){ut(Xe)};else if(typeof MessageChannel<"u"){var za=new MessageChannel,Ua=za.port2;za.port1.onmessage=Xe,Lt=function(){Ua.postMessage(null)}}else Lt=function(){St(Xe,0)};function Zt(A,q){Ut=St(function(){A(f.unstable_now())},q)}f.unstable_IdlePriority=5,f.unstable_ImmediatePriority=1,f.unstable_LowPriority=4,f.unstable_NormalPriority=3,f.unstable_Profiling=null,f.unstable_UserBlockingPriority=2,f.unstable_cancelCallback=function(A){A.callback=null},f.unstable_forceFrameRate=function(A){0>A||125<A?console.error("forceFrameRate takes a positive int between 0 and 125, forcing frame rates higher than 125 fps is not supported"):Pt=0<A?Math.floor(1e3/A):5},f.unstable_getCurrentPriorityLevel=function(){return k},f.unstable_next=function(A){switch(k){case 1:case 2:case 3:var q=3;break;default:q=k}var Z=k;k=q;try{return A()}finally{k=Z}},f.unstable_requestPaint=function(){lt=!0},f.unstable_runWithPriority=function(A,q){switch(A){case 1:case 2:case 3:case 4:case 5:break;default:A=3}var Z=k;k=A;try{return q()}finally{k=Z}},f.unstable_scheduleCallback=function(A,q,Z){var mt=f.unstable_now();switch(typeof Z=="object"&&Z!==null?(Z=Z.delay,Z=typeof Z=="number"&&0<Z?mt+Z:mt):Z=mt,A){case 1:var y=-1;break;case 2:y=250;break;case 5:y=1073741823;break;case 4:y=1e4;break;default:y=5e3}return y=Z+y,A={id:U++,callback:q,priorityLevel:A,startTime:Z,expirationTime:y,sortIndex:-1},Z>mt?(A.sortIndex=Z,i(x,A),c(w)===null&&A===c(x)&&(bt?(gt(Ut),Ut=-1):bt=!0,Zt(F,Z-mt))):(A.sortIndex=y,i(w,A),L||H||(L=!0,zt||(zt=!0,Lt()))),A},f.unstable_shouldYield=el,f.unstable_wrapCallback=function(A){var q=k;return function(){var Z=k;k=q;try{return A.apply(this,arguments)}finally{k=Z}}}}(df)),df}var zh;function ug(){return zh||(zh=1,of.exports=ng()),of.exports}var hf={exports:{}},kt={};/**
 * @license React
 * react-dom.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var Uh;function ig(){if(Uh)return kt;Uh=1;var f=Of();function i(w){var x="https://react.dev/errors/"+w;if(1<arguments.length){x+="?args[]="+encodeURIComponent(arguments[1]);for(var U=2;U<arguments.length;U++)x+="&args[]="+encodeURIComponent(arguments[U])}return"Minified React error #"+w+"; visit "+x+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function c(){}var s={d:{f:c,r:function(){throw Error(i(522))},D:c,C:c,L:c,m:c,X:c,S:c,M:c},p:0,findDOMNode:null},o=Symbol.for("react.portal");function m(w,x,U){var K=3<arguments.length&&arguments[3]!==void 0?arguments[3]:null;return{$$typeof:o,key:K==null?null:""+K,children:w,containerInfo:x,implementation:U}}var E=f.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE;function O(w,x){if(w==="font")return"";if(typeof x=="string")return x==="use-credentials"?x:""}return kt.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE=s,kt.createPortal=function(w,x){var U=2<arguments.length&&arguments[2]!==void 0?arguments[2]:null;if(!x||x.nodeType!==1&&x.nodeType!==9&&x.nodeType!==11)throw Error(i(299));return m(w,x,null,U)},kt.flushSync=function(w){var x=E.T,U=s.p;try{if(E.T=null,s.p=2,w)return w()}finally{E.T=x,s.p=U,s.d.f()}},kt.preconnect=function(w,x){typeof w=="string"&&(x?(x=x.crossOrigin,x=typeof x=="string"?x==="use-credentials"?x:"":void 0):x=null,s.d.C(w,x))},kt.prefetchDNS=function(w){typeof w=="string"&&s.d.D(w)},kt.preinit=function(w,x){if(typeof w=="string"&&x&&typeof x.as=="string"){var U=x.as,K=O(U,x.crossOrigin),k=typeof x.integrity=="string"?x.integrity:void 0,H=typeof x.fetchPriority=="string"?x.fetchPriority:void 0;U==="style"?s.d.S(w,typeof x.precedence=="string"?x.precedence:void 0,{crossOrigin:K,integrity:k,fetchPriority:H}):U==="script"&&s.d.X(w,{crossOrigin:K,integrity:k,fetchPriority:H,nonce:typeof x.nonce=="string"?x.nonce:void 0})}},kt.preinitModule=function(w,x){if(typeof w=="string")if(typeof x=="object"&&x!==null){if(x.as==null||x.as==="script"){var U=O(x.as,x.crossOrigin);s.d.M(w,{crossOrigin:U,integrity:typeof x.integrity=="string"?x.integrity:void 0,nonce:typeof x.nonce=="string"?x.nonce:void 0})}}else x==null&&s.d.M(w)},kt.preload=function(w,x){if(typeof w=="string"&&typeof x=="object"&&x!==null&&typeof x.as=="string"){var U=x.as,K=O(U,x.crossOrigin);s.d.L(w,U,{crossOrigin:K,integrity:typeof x.integrity=="string"?x.integrity:void 0,nonce:typeof x.nonce=="string"?x.nonce:void 0,type:typeof x.type=="string"?x.type:void 0,fetchPriority:typeof x.fetchPriority=="string"?x.fetchPriority:void 0,referrerPolicy:typeof x.referrerPolicy=="string"?x.referrerPolicy:void 0,imageSrcSet:typeof x.imageSrcSet=="string"?x.imageSrcSet:void 0,imageSizes:typeof x.imageSizes=="string"?x.imageSizes:void 0,media:typeof x.media=="string"?x.media:void 0})}},kt.preloadModule=function(w,x){if(typeof w=="string")if(x){var U=O(x.as,x.crossOrigin);s.d.m(w,{as:typeof x.as=="string"&&x.as!=="script"?x.as:void 0,crossOrigin:U,integrity:typeof x.integrity=="string"?x.integrity:void 0})}else s.d.m(w)},kt.requestFormReset=function(w){s.d.r(w)},kt.unstable_batchedUpdates=function(w,x){return w(x)},kt.useFormState=function(w,x,U){return E.H.useFormState(w,x,U)},kt.useFormStatus=function(){return E.H.useHostTransitionStatus()},kt.version="19.1.0",kt}var jh;function sg(){if(jh)return hf.exports;jh=1;function f(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(f)}catch(i){console.error(i)}}return f(),hf.exports=ig(),hf.exports}/**
 * @license React
 * react-dom-client.production.js
 *
 * Copyright (c) Meta Platforms, Inc. and affiliates.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */var qh;function cg(){if(qh)return Vn;qh=1;var f=ug(),i=Of(),c=sg();function s(t){var e="https://react.dev/errors/"+t;if(1<arguments.length){e+="?args[]="+encodeURIComponent(arguments[1]);for(var a=2;a<arguments.length;a++)e+="&args[]="+encodeURIComponent(arguments[a])}return"Minified React error #"+t+"; visit "+e+" for the full message or use the non-minified dev environment for full errors and additional helpful warnings."}function o(t){return!(!t||t.nodeType!==1&&t.nodeType!==9&&t.nodeType!==11)}function m(t){var e=t,a=t;if(t.alternate)for(;e.return;)e=e.return;else{t=e;do e=t,(e.flags&4098)!==0&&(a=e.return),t=e.return;while(t)}return e.tag===3?a:null}function E(t){if(t.tag===13){var e=t.memoizedState;if(e===null&&(t=t.alternate,t!==null&&(e=t.memoizedState)),e!==null)return e.dehydrated}return null}function O(t){if(m(t)!==t)throw Error(s(188))}function w(t){var e=t.alternate;if(!e){if(e=m(t),e===null)throw Error(s(188));return e!==t?null:t}for(var a=t,l=e;;){var n=a.return;if(n===null)break;var u=n.alternate;if(u===null){if(l=n.return,l!==null){a=l;continue}break}if(n.child===u.child){for(u=n.child;u;){if(u===a)return O(n),t;if(u===l)return O(n),e;u=u.sibling}throw Error(s(188))}if(a.return!==l.return)a=n,l=u;else{for(var r=!1,d=n.child;d;){if(d===a){r=!0,a=n,l=u;break}if(d===l){r=!0,l=n,a=u;break}d=d.sibling}if(!r){for(d=u.child;d;){if(d===a){r=!0,a=u,l=n;break}if(d===l){r=!0,l=u,a=n;break}d=d.sibling}if(!r)throw Error(s(189))}}if(a.alternate!==l)throw Error(s(190))}if(a.tag!==3)throw Error(s(188));return a.stateNode.current===a?t:e}function x(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t;for(t=t.child;t!==null;){if(e=x(t),e!==null)return e;t=t.sibling}return null}var U=Object.assign,K=Symbol.for("react.element"),k=Symbol.for("react.transitional.element"),H=Symbol.for("react.portal"),L=Symbol.for("react.fragment"),bt=Symbol.for("react.strict_mode"),lt=Symbol.for("react.profiler"),St=Symbol.for("react.provider"),gt=Symbol.for("react.consumer"),ut=Symbol.for("react.context"),pt=Symbol.for("react.forward_ref"),F=Symbol.for("react.suspense"),zt=Symbol.for("react.suspense_list"),Ut=Symbol.for("react.memo"),Pt=Symbol.for("react.lazy"),Wt=Symbol.for("react.activity"),el=Symbol.for("react.memo_cache_sentinel"),Xe=Symbol.iterator;function Lt(t){return t===null||typeof t!="object"?null:(t=Xe&&t[Xe]||t["@@iterator"],typeof t=="function"?t:null)}var za=Symbol.for("react.client.reference");function Ua(t){if(t==null)return null;if(typeof t=="function")return t.$$typeof===za?null:t.displayName||t.name||null;if(typeof t=="string")return t;switch(t){case L:return"Fragment";case lt:return"Profiler";case bt:return"StrictMode";case F:return"Suspense";case zt:return"SuspenseList";case Wt:return"Activity"}if(typeof t=="object")switch(t.$$typeof){case H:return"Portal";case ut:return(t.displayName||"Context")+".Provider";case gt:return(t._context.displayName||"Context")+".Consumer";case pt:var e=t.render;return t=t.displayName,t||(t=e.displayName||e.name||"",t=t!==""?"ForwardRef("+t+")":"ForwardRef"),t;case Ut:return e=t.displayName||null,e!==null?e:Ua(t.type)||"Memo";case Pt:e=t._payload,t=t._init;try{return Ua(t(e))}catch{}}return null}var Zt=Array.isArray,A=i.__CLIENT_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,q=c.__DOM_INTERNALS_DO_NOT_USE_OR_WARN_USERS_THEY_CANNOT_UPGRADE,Z={pending:!1,data:null,method:null,action:null},mt=[],y=-1;function D(t){return{current:t}}function B(t){0>y||(t.current=mt[y],mt[y]=null,y--)}function j(t,e){y++,mt[y]=t.current,t.current=e}var X=D(null),it=D(null),J=D(null),ue=D(null);function _t(t,e){switch(j(J,e),j(it,t),j(X,null),e.nodeType){case 9:case 11:t=(t=e.documentElement)&&(t=t.namespaceURI)?th(t):0;break;default:if(t=e.tagName,e=e.namespaceURI)e=th(e),t=eh(e,t);else switch(t){case"svg":t=1;break;case"math":t=2;break;default:t=0}}B(X),j(X,t)}function ua(){B(X),B(it),B(J)}function Ki(t){t.memoizedState!==null&&j(ue,t);var e=X.current,a=eh(e,t.type);e!==a&&(j(it,t),j(X,a))}function cu(t){it.current===t&&(B(X),B(it)),ue.current===t&&(B(ue),Yn._currentValue=Z)}var ki=Object.prototype.hasOwnProperty,Ji=f.unstable_scheduleCallback,$i=f.unstable_cancelCallback,Dm=f.unstable_shouldYield,zm=f.unstable_requestPaint,Ue=f.unstable_now,Um=f.unstable_getCurrentPriorityLevel,Uf=f.unstable_ImmediatePriority,jf=f.unstable_UserBlockingPriority,fu=f.unstable_NormalPriority,jm=f.unstable_LowPriority,qf=f.unstable_IdlePriority,qm=f.log,Hm=f.unstable_setDisableYieldValue,Kl=null,ie=null;function ia(t){if(typeof qm=="function"&&Hm(t),ie&&typeof ie.setStrictMode=="function")try{ie.setStrictMode(Kl,t)}catch{}}var se=Math.clz32?Math.clz32:Ym,Bm=Math.log,Cm=Math.LN2;function Ym(t){return t>>>=0,t===0?32:31-(Bm(t)/Cm|0)|0}var ru=256,ou=4194304;function ja(t){var e=t&42;if(e!==0)return e;switch(t&-t){case 1:return 1;case 2:return 2;case 4:return 4;case 8:return 8;case 16:return 16;case 32:return 32;case 64:return 64;case 128:return 128;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return t&4194048;case 4194304:case 8388608:case 16777216:case 33554432:return t&62914560;case 67108864:return 67108864;case 134217728:return 134217728;case 268435456:return 268435456;case 536870912:return 536870912;case 1073741824:return 0;default:return t}}function du(t,e,a){var l=t.pendingLanes;if(l===0)return 0;var n=0,u=t.suspendedLanes,r=t.pingedLanes;t=t.warmLanes;var d=l&134217727;return d!==0?(l=d&~u,l!==0?n=ja(l):(r&=d,r!==0?n=ja(r):a||(a=d&~t,a!==0&&(n=ja(a))))):(d=l&~u,d!==0?n=ja(d):r!==0?n=ja(r):a||(a=l&~t,a!==0&&(n=ja(a)))),n===0?0:e!==0&&e!==n&&(e&u)===0&&(u=n&-n,a=e&-e,u>=a||u===32&&(a&4194048)!==0)?e:n}function kl(t,e){return(t.pendingLanes&~(t.suspendedLanes&~t.pingedLanes)&e)===0}function Xm(t,e){switch(t){case 1:case 2:case 4:case 8:case 64:return e+250;case 16:case 32:case 128:case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:return e+5e3;case 4194304:case 8388608:case 16777216:case 33554432:return-1;case 67108864:case 134217728:case 268435456:case 536870912:case 1073741824:return-1;default:return-1}}function Hf(){var t=ru;return ru<<=1,(ru&4194048)===0&&(ru=256),t}function Bf(){var t=ou;return ou<<=1,(ou&62914560)===0&&(ou=4194304),t}function Wi(t){for(var e=[],a=0;31>a;a++)e.push(t);return e}function Jl(t,e){t.pendingLanes|=e,e!==268435456&&(t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0)}function Gm(t,e,a,l,n,u){var r=t.pendingLanes;t.pendingLanes=a,t.suspendedLanes=0,t.pingedLanes=0,t.warmLanes=0,t.expiredLanes&=a,t.entangledLanes&=a,t.errorRecoveryDisabledLanes&=a,t.shellSuspendCounter=0;var d=t.entanglements,h=t.expirationTimes,S=t.hiddenUpdates;for(a=r&~a;0<a;){var M=31-se(a),N=1<<M;d[M]=0,h[M]=-1;var p=S[M];if(p!==null)for(S[M]=null,M=0;M<p.length;M++){var _=p[M];_!==null&&(_.lane&=-536870913)}a&=~N}l!==0&&Cf(t,l,0),u!==0&&n===0&&t.tag!==0&&(t.suspendedLanes|=u&~(r&~e))}function Cf(t,e,a){t.pendingLanes|=e,t.suspendedLanes&=~e;var l=31-se(e);t.entangledLanes|=e,t.entanglements[l]=t.entanglements[l]|1073741824|a&4194090}function Yf(t,e){var a=t.entangledLanes|=e;for(t=t.entanglements;a;){var l=31-se(a),n=1<<l;n&e|t[l]&e&&(t[l]|=e),a&=~n}}function Fi(t){switch(t){case 2:t=1;break;case 8:t=4;break;case 32:t=16;break;case 256:case 512:case 1024:case 2048:case 4096:case 8192:case 16384:case 32768:case 65536:case 131072:case 262144:case 524288:case 1048576:case 2097152:case 4194304:case 8388608:case 16777216:case 33554432:t=128;break;case 268435456:t=134217728;break;default:t=0}return t}function Pi(t){return t&=-t,2<t?8<t?(t&134217727)!==0?32:268435456:8:2}function Xf(){var t=q.p;return t!==0?t:(t=window.event,t===void 0?32:ph(t.type))}function Qm(t,e){var a=q.p;try{return q.p=t,e()}finally{q.p=a}}var sa=Math.random().toString(36).slice(2),Vt="__reactFiber$"+sa,It="__reactProps$"+sa,al="__reactContainer$"+sa,Ii="__reactEvents$"+sa,Lm="__reactListeners$"+sa,Zm="__reactHandles$"+sa,Gf="__reactResources$"+sa,$l="__reactMarker$"+sa;function ts(t){delete t[Vt],delete t[It],delete t[Ii],delete t[Lm],delete t[Zm]}function ll(t){var e=t[Vt];if(e)return e;for(var a=t.parentNode;a;){if(e=a[al]||a[Vt]){if(a=e.alternate,e.child!==null||a!==null&&a.child!==null)for(t=uh(t);t!==null;){if(a=t[Vt])return a;t=uh(t)}return e}t=a,a=t.parentNode}return null}function nl(t){if(t=t[Vt]||t[al]){var e=t.tag;if(e===5||e===6||e===13||e===26||e===27||e===3)return t}return null}function Wl(t){var e=t.tag;if(e===5||e===26||e===27||e===6)return t.stateNode;throw Error(s(33))}function ul(t){var e=t[Gf];return e||(e=t[Gf]={hoistableStyles:new Map,hoistableScripts:new Map}),e}function Ht(t){t[$l]=!0}var Qf=new Set,Lf={};function qa(t,e){il(t,e),il(t+"Capture",e)}function il(t,e){for(Lf[t]=e,t=0;t<e.length;t++)Qf.add(e[t])}var Vm=RegExp("^[:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD][:A-Z_a-z\\u00C0-\\u00D6\\u00D8-\\u00F6\\u00F8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C-\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD\\-.0-9\\u00B7\\u0300-\\u036F\\u203F-\\u2040]*$"),Zf={},Vf={};function Km(t){return ki.call(Vf,t)?!0:ki.call(Zf,t)?!1:Vm.test(t)?Vf[t]=!0:(Zf[t]=!0,!1)}function hu(t,e,a){if(Km(e))if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":t.removeAttribute(e);return;case"boolean":var l=e.toLowerCase().slice(0,5);if(l!=="data-"&&l!=="aria-"){t.removeAttribute(e);return}}t.setAttribute(e,""+a)}}function mu(t,e,a){if(a===null)t.removeAttribute(e);else{switch(typeof a){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(e);return}t.setAttribute(e,""+a)}}function Ge(t,e,a,l){if(l===null)t.removeAttribute(a);else{switch(typeof l){case"undefined":case"function":case"symbol":case"boolean":t.removeAttribute(a);return}t.setAttributeNS(e,a,""+l)}}var es,Kf;function sl(t){if(es===void 0)try{throw Error()}catch(a){var e=a.stack.trim().match(/\n( *(at )?)/);es=e&&e[1]||"",Kf=-1<a.stack.indexOf(`
    at`)?" (<anonymous>)":-1<a.stack.indexOf("@")?"@unknown:0:0":""}return`
`+es+t+Kf}var as=!1;function ls(t,e){if(!t||as)return"";as=!0;var a=Error.prepareStackTrace;Error.prepareStackTrace=void 0;try{var l={DetermineComponentFrameRoot:function(){try{if(e){var N=function(){throw Error()};if(Object.defineProperty(N.prototype,"props",{set:function(){throw Error()}}),typeof Reflect=="object"&&Reflect.construct){try{Reflect.construct(N,[])}catch(_){var p=_}Reflect.construct(t,[],N)}else{try{N.call()}catch(_){p=_}t.call(N.prototype)}}else{try{throw Error()}catch(_){p=_}(N=t())&&typeof N.catch=="function"&&N.catch(function(){})}}catch(_){if(_&&p&&typeof _.stack=="string")return[_.stack,p.stack]}return[null,null]}};l.DetermineComponentFrameRoot.displayName="DetermineComponentFrameRoot";var n=Object.getOwnPropertyDescriptor(l.DetermineComponentFrameRoot,"name");n&&n.configurable&&Object.defineProperty(l.DetermineComponentFrameRoot,"name",{value:"DetermineComponentFrameRoot"});var u=l.DetermineComponentFrameRoot(),r=u[0],d=u[1];if(r&&d){var h=r.split(`
`),S=d.split(`
`);for(n=l=0;l<h.length&&!h[l].includes("DetermineComponentFrameRoot");)l++;for(;n<S.length&&!S[n].includes("DetermineComponentFrameRoot");)n++;if(l===h.length||n===S.length)for(l=h.length-1,n=S.length-1;1<=l&&0<=n&&h[l]!==S[n];)n--;for(;1<=l&&0<=n;l--,n--)if(h[l]!==S[n]){if(l!==1||n!==1)do if(l--,n--,0>n||h[l]!==S[n]){var M=`
`+h[l].replace(" at new "," at ");return t.displayName&&M.includes("<anonymous>")&&(M=M.replace("<anonymous>",t.displayName)),M}while(1<=l&&0<=n);break}}}finally{as=!1,Error.prepareStackTrace=a}return(a=t?t.displayName||t.name:"")?sl(a):""}function km(t){switch(t.tag){case 26:case 27:case 5:return sl(t.type);case 16:return sl("Lazy");case 13:return sl("Suspense");case 19:return sl("SuspenseList");case 0:case 15:return ls(t.type,!1);case 11:return ls(t.type.render,!1);case 1:return ls(t.type,!0);case 31:return sl("Activity");default:return""}}function kf(t){try{var e="";do e+=km(t),t=t.return;while(t);return e}catch(a){return`
Error generating stack: `+a.message+`
`+a.stack}}function ve(t){switch(typeof t){case"bigint":case"boolean":case"number":case"string":case"undefined":return t;case"object":return t;default:return""}}function Jf(t){var e=t.type;return(t=t.nodeName)&&t.toLowerCase()==="input"&&(e==="checkbox"||e==="radio")}function Jm(t){var e=Jf(t)?"checked":"value",a=Object.getOwnPropertyDescriptor(t.constructor.prototype,e),l=""+t[e];if(!t.hasOwnProperty(e)&&typeof a<"u"&&typeof a.get=="function"&&typeof a.set=="function"){var n=a.get,u=a.set;return Object.defineProperty(t,e,{configurable:!0,get:function(){return n.call(this)},set:function(r){l=""+r,u.call(this,r)}}),Object.defineProperty(t,e,{enumerable:a.enumerable}),{getValue:function(){return l},setValue:function(r){l=""+r},stopTracking:function(){t._valueTracker=null,delete t[e]}}}}function yu(t){t._valueTracker||(t._valueTracker=Jm(t))}function $f(t){if(!t)return!1;var e=t._valueTracker;if(!e)return!0;var a=e.getValue(),l="";return t&&(l=Jf(t)?t.checked?"true":"false":t.value),t=l,t!==a?(e.setValue(t),!0):!1}function gu(t){if(t=t||(typeof document<"u"?document:void 0),typeof t>"u")return null;try{return t.activeElement||t.body}catch{return t.body}}var $m=/[\n"\\]/g;function be(t){return t.replace($m,function(e){return"\\"+e.charCodeAt(0).toString(16)+" "})}function ns(t,e,a,l,n,u,r,d){t.name="",r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"?t.type=r:t.removeAttribute("type"),e!=null?r==="number"?(e===0&&t.value===""||t.value!=e)&&(t.value=""+ve(e)):t.value!==""+ve(e)&&(t.value=""+ve(e)):r!=="submit"&&r!=="reset"||t.removeAttribute("value"),e!=null?us(t,r,ve(e)):a!=null?us(t,r,ve(a)):l!=null&&t.removeAttribute("value"),n==null&&u!=null&&(t.defaultChecked=!!u),n!=null&&(t.checked=n&&typeof n!="function"&&typeof n!="symbol"),d!=null&&typeof d!="function"&&typeof d!="symbol"&&typeof d!="boolean"?t.name=""+ve(d):t.removeAttribute("name")}function Wf(t,e,a,l,n,u,r,d){if(u!=null&&typeof u!="function"&&typeof u!="symbol"&&typeof u!="boolean"&&(t.type=u),e!=null||a!=null){if(!(u!=="submit"&&u!=="reset"||e!=null))return;a=a!=null?""+ve(a):"",e=e!=null?""+ve(e):a,d||e===t.value||(t.value=e),t.defaultValue=e}l=l??n,l=typeof l!="function"&&typeof l!="symbol"&&!!l,t.checked=d?t.checked:!!l,t.defaultChecked=!!l,r!=null&&typeof r!="function"&&typeof r!="symbol"&&typeof r!="boolean"&&(t.name=r)}function us(t,e,a){e==="number"&&gu(t.ownerDocument)===t||t.defaultValue===""+a||(t.defaultValue=""+a)}function cl(t,e,a,l){if(t=t.options,e){e={};for(var n=0;n<a.length;n++)e["$"+a[n]]=!0;for(a=0;a<t.length;a++)n=e.hasOwnProperty("$"+t[a].value),t[a].selected!==n&&(t[a].selected=n),n&&l&&(t[a].defaultSelected=!0)}else{for(a=""+ve(a),e=null,n=0;n<t.length;n++){if(t[n].value===a){t[n].selected=!0,l&&(t[n].defaultSelected=!0);return}e!==null||t[n].disabled||(e=t[n])}e!==null&&(e.selected=!0)}}function Ff(t,e,a){if(e!=null&&(e=""+ve(e),e!==t.value&&(t.value=e),a==null)){t.defaultValue!==e&&(t.defaultValue=e);return}t.defaultValue=a!=null?""+ve(a):""}function Pf(t,e,a,l){if(e==null){if(l!=null){if(a!=null)throw Error(s(92));if(Zt(l)){if(1<l.length)throw Error(s(93));l=l[0]}a=l}a==null&&(a=""),e=a}a=ve(e),t.defaultValue=a,l=t.textContent,l===a&&l!==""&&l!==null&&(t.value=l)}function fl(t,e){if(e){var a=t.firstChild;if(a&&a===t.lastChild&&a.nodeType===3){a.nodeValue=e;return}}t.textContent=e}var Wm=new Set("animationIterationCount aspectRatio borderImageOutset borderImageSlice borderImageWidth boxFlex boxFlexGroup boxOrdinalGroup columnCount columns flex flexGrow flexPositive flexShrink flexNegative flexOrder gridArea gridRow gridRowEnd gridRowSpan gridRowStart gridColumn gridColumnEnd gridColumnSpan gridColumnStart fontWeight lineClamp lineHeight opacity order orphans scale tabSize widows zIndex zoom fillOpacity floodOpacity stopOpacity strokeDasharray strokeDashoffset strokeMiterlimit strokeOpacity strokeWidth MozAnimationIterationCount MozBoxFlex MozBoxFlexGroup MozLineClamp msAnimationIterationCount msFlex msZoom msFlexGrow msFlexNegative msFlexOrder msFlexPositive msFlexShrink msGridColumn msGridColumnSpan msGridRow msGridRowSpan WebkitAnimationIterationCount WebkitBoxFlex WebKitBoxFlexGroup WebkitBoxOrdinalGroup WebkitColumnCount WebkitColumns WebkitFlex WebkitFlexGrow WebkitFlexPositive WebkitFlexShrink WebkitLineClamp".split(" "));function If(t,e,a){var l=e.indexOf("--")===0;a==null||typeof a=="boolean"||a===""?l?t.setProperty(e,""):e==="float"?t.cssFloat="":t[e]="":l?t.setProperty(e,a):typeof a!="number"||a===0||Wm.has(e)?e==="float"?t.cssFloat=a:t[e]=(""+a).trim():t[e]=a+"px"}function tr(t,e,a){if(e!=null&&typeof e!="object")throw Error(s(62));if(t=t.style,a!=null){for(var l in a)!a.hasOwnProperty(l)||e!=null&&e.hasOwnProperty(l)||(l.indexOf("--")===0?t.setProperty(l,""):l==="float"?t.cssFloat="":t[l]="");for(var n in e)l=e[n],e.hasOwnProperty(n)&&a[n]!==l&&If(t,n,l)}else for(var u in e)e.hasOwnProperty(u)&&If(t,u,e[u])}function is(t){if(t.indexOf("-")===-1)return!1;switch(t){case"annotation-xml":case"color-profile":case"font-face":case"font-face-src":case"font-face-uri":case"font-face-format":case"font-face-name":case"missing-glyph":return!1;default:return!0}}var Fm=new Map([["acceptCharset","accept-charset"],["htmlFor","for"],["httpEquiv","http-equiv"],["crossOrigin","crossorigin"],["accentHeight","accent-height"],["alignmentBaseline","alignment-baseline"],["arabicForm","arabic-form"],["baselineShift","baseline-shift"],["capHeight","cap-height"],["clipPath","clip-path"],["clipRule","clip-rule"],["colorInterpolation","color-interpolation"],["colorInterpolationFilters","color-interpolation-filters"],["colorProfile","color-profile"],["colorRendering","color-rendering"],["dominantBaseline","dominant-baseline"],["enableBackground","enable-background"],["fillOpacity","fill-opacity"],["fillRule","fill-rule"],["floodColor","flood-color"],["floodOpacity","flood-opacity"],["fontFamily","font-family"],["fontSize","font-size"],["fontSizeAdjust","font-size-adjust"],["fontStretch","font-stretch"],["fontStyle","font-style"],["fontVariant","font-variant"],["fontWeight","font-weight"],["glyphName","glyph-name"],["glyphOrientationHorizontal","glyph-orientation-horizontal"],["glyphOrientationVertical","glyph-orientation-vertical"],["horizAdvX","horiz-adv-x"],["horizOriginX","horiz-origin-x"],["imageRendering","image-rendering"],["letterSpacing","letter-spacing"],["lightingColor","lighting-color"],["markerEnd","marker-end"],["markerMid","marker-mid"],["markerStart","marker-start"],["overlinePosition","overline-position"],["overlineThickness","overline-thickness"],["paintOrder","paint-order"],["panose-1","panose-1"],["pointerEvents","pointer-events"],["renderingIntent","rendering-intent"],["shapeRendering","shape-rendering"],["stopColor","stop-color"],["stopOpacity","stop-opacity"],["strikethroughPosition","strikethrough-position"],["strikethroughThickness","strikethrough-thickness"],["strokeDasharray","stroke-dasharray"],["strokeDashoffset","stroke-dashoffset"],["strokeLinecap","stroke-linecap"],["strokeLinejoin","stroke-linejoin"],["strokeMiterlimit","stroke-miterlimit"],["strokeOpacity","stroke-opacity"],["strokeWidth","stroke-width"],["textAnchor","text-anchor"],["textDecoration","text-decoration"],["textRendering","text-rendering"],["transformOrigin","transform-origin"],["underlinePosition","underline-position"],["underlineThickness","underline-thickness"],["unicodeBidi","unicode-bidi"],["unicodeRange","unicode-range"],["unitsPerEm","units-per-em"],["vAlphabetic","v-alphabetic"],["vHanging","v-hanging"],["vIdeographic","v-ideographic"],["vMathematical","v-mathematical"],["vectorEffect","vector-effect"],["vertAdvY","vert-adv-y"],["vertOriginX","vert-origin-x"],["vertOriginY","vert-origin-y"],["wordSpacing","word-spacing"],["writingMode","writing-mode"],["xmlnsXlink","xmlns:xlink"],["xHeight","x-height"]]),Pm=/^[\u0000-\u001F ]*j[\r\n\t]*a[\r\n\t]*v[\r\n\t]*a[\r\n\t]*s[\r\n\t]*c[\r\n\t]*r[\r\n\t]*i[\r\n\t]*p[\r\n\t]*t[\r\n\t]*:/i;function vu(t){return Pm.test(""+t)?"javascript:throw new Error('React has blocked a javascript: URL as a security precaution.')":t}var ss=null;function cs(t){return t=t.target||t.srcElement||window,t.correspondingUseElement&&(t=t.correspondingUseElement),t.nodeType===3?t.parentNode:t}var rl=null,ol=null;function er(t){var e=nl(t);if(e&&(t=e.stateNode)){var a=t[It]||null;t:switch(t=e.stateNode,e.type){case"input":if(ns(t,a.value,a.defaultValue,a.defaultValue,a.checked,a.defaultChecked,a.type,a.name),e=a.name,a.type==="radio"&&e!=null){for(a=t;a.parentNode;)a=a.parentNode;for(a=a.querySelectorAll('input[name="'+be(""+e)+'"][type="radio"]'),e=0;e<a.length;e++){var l=a[e];if(l!==t&&l.form===t.form){var n=l[It]||null;if(!n)throw Error(s(90));ns(l,n.value,n.defaultValue,n.defaultValue,n.checked,n.defaultChecked,n.type,n.name)}}for(e=0;e<a.length;e++)l=a[e],l.form===t.form&&$f(l)}break t;case"textarea":Ff(t,a.value,a.defaultValue);break t;case"select":e=a.value,e!=null&&cl(t,!!a.multiple,e,!1)}}}var fs=!1;function ar(t,e,a){if(fs)return t(e,a);fs=!0;try{var l=t(e);return l}finally{if(fs=!1,(rl!==null||ol!==null)&&(ai(),rl&&(e=rl,t=ol,ol=rl=null,er(e),t)))for(e=0;e<t.length;e++)er(t[e])}}function Fl(t,e){var a=t.stateNode;if(a===null)return null;var l=a[It]||null;if(l===null)return null;a=l[e];t:switch(e){case"onClick":case"onClickCapture":case"onDoubleClick":case"onDoubleClickCapture":case"onMouseDown":case"onMouseDownCapture":case"onMouseMove":case"onMouseMoveCapture":case"onMouseUp":case"onMouseUpCapture":case"onMouseEnter":(l=!l.disabled)||(t=t.type,l=!(t==="button"||t==="input"||t==="select"||t==="textarea")),t=!l;break t;default:t=!1}if(t)return null;if(a&&typeof a!="function")throw Error(s(231,e,typeof a));return a}var Qe=!(typeof window>"u"||typeof window.document>"u"||typeof window.document.createElement>"u"),rs=!1;if(Qe)try{var Pl={};Object.defineProperty(Pl,"passive",{get:function(){rs=!0}}),window.addEventListener("test",Pl,Pl),window.removeEventListener("test",Pl,Pl)}catch{rs=!1}var ca=null,os=null,bu=null;function lr(){if(bu)return bu;var t,e=os,a=e.length,l,n="value"in ca?ca.value:ca.textContent,u=n.length;for(t=0;t<a&&e[t]===n[t];t++);var r=a-t;for(l=1;l<=r&&e[a-l]===n[u-l];l++);return bu=n.slice(t,1<l?1-l:void 0)}function Su(t){var e=t.keyCode;return"charCode"in t?(t=t.charCode,t===0&&e===13&&(t=13)):t=e,t===10&&(t=13),32<=t||t===13?t:0}function pu(){return!0}function nr(){return!1}function te(t){function e(a,l,n,u,r){this._reactName=a,this._targetInst=n,this.type=l,this.nativeEvent=u,this.target=r,this.currentTarget=null;for(var d in t)t.hasOwnProperty(d)&&(a=t[d],this[d]=a?a(u):u[d]);return this.isDefaultPrevented=(u.defaultPrevented!=null?u.defaultPrevented:u.returnValue===!1)?pu:nr,this.isPropagationStopped=nr,this}return U(e.prototype,{preventDefault:function(){this.defaultPrevented=!0;var a=this.nativeEvent;a&&(a.preventDefault?a.preventDefault():typeof a.returnValue!="unknown"&&(a.returnValue=!1),this.isDefaultPrevented=pu)},stopPropagation:function(){var a=this.nativeEvent;a&&(a.stopPropagation?a.stopPropagation():typeof a.cancelBubble!="unknown"&&(a.cancelBubble=!0),this.isPropagationStopped=pu)},persist:function(){},isPersistent:pu}),e}var Ha={eventPhase:0,bubbles:0,cancelable:0,timeStamp:function(t){return t.timeStamp||Date.now()},defaultPrevented:0,isTrusted:0},_u=te(Ha),Il=U({},Ha,{view:0,detail:0}),Im=te(Il),ds,hs,tn,xu=U({},Il,{screenX:0,screenY:0,clientX:0,clientY:0,pageX:0,pageY:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,getModifierState:ys,button:0,buttons:0,relatedTarget:function(t){return t.relatedTarget===void 0?t.fromElement===t.srcElement?t.toElement:t.fromElement:t.relatedTarget},movementX:function(t){return"movementX"in t?t.movementX:(t!==tn&&(tn&&t.type==="mousemove"?(ds=t.screenX-tn.screenX,hs=t.screenY-tn.screenY):hs=ds=0,tn=t),ds)},movementY:function(t){return"movementY"in t?t.movementY:hs}}),ur=te(xu),t0=U({},xu,{dataTransfer:0}),e0=te(t0),a0=U({},Il,{relatedTarget:0}),ms=te(a0),l0=U({},Ha,{animationName:0,elapsedTime:0,pseudoElement:0}),n0=te(l0),u0=U({},Ha,{clipboardData:function(t){return"clipboardData"in t?t.clipboardData:window.clipboardData}}),i0=te(u0),s0=U({},Ha,{data:0}),ir=te(s0),c0={Esc:"Escape",Spacebar:" ",Left:"ArrowLeft",Up:"ArrowUp",Right:"ArrowRight",Down:"ArrowDown",Del:"Delete",Win:"OS",Menu:"ContextMenu",Apps:"ContextMenu",Scroll:"ScrollLock",MozPrintableKey:"Unidentified"},f0={8:"Backspace",9:"Tab",12:"Clear",13:"Enter",16:"Shift",17:"Control",18:"Alt",19:"Pause",20:"CapsLock",27:"Escape",32:" ",33:"PageUp",34:"PageDown",35:"End",36:"Home",37:"ArrowLeft",38:"ArrowUp",39:"ArrowRight",40:"ArrowDown",45:"Insert",46:"Delete",112:"F1",113:"F2",114:"F3",115:"F4",116:"F5",117:"F6",118:"F7",119:"F8",120:"F9",121:"F10",122:"F11",123:"F12",144:"NumLock",145:"ScrollLock",224:"Meta"},r0={Alt:"altKey",Control:"ctrlKey",Meta:"metaKey",Shift:"shiftKey"};function o0(t){var e=this.nativeEvent;return e.getModifierState?e.getModifierState(t):(t=r0[t])?!!e[t]:!1}function ys(){return o0}var d0=U({},Il,{key:function(t){if(t.key){var e=c0[t.key]||t.key;if(e!=="Unidentified")return e}return t.type==="keypress"?(t=Su(t),t===13?"Enter":String.fromCharCode(t)):t.type==="keydown"||t.type==="keyup"?f0[t.keyCode]||"Unidentified":""},code:0,location:0,ctrlKey:0,shiftKey:0,altKey:0,metaKey:0,repeat:0,locale:0,getModifierState:ys,charCode:function(t){return t.type==="keypress"?Su(t):0},keyCode:function(t){return t.type==="keydown"||t.type==="keyup"?t.keyCode:0},which:function(t){return t.type==="keypress"?Su(t):t.type==="keydown"||t.type==="keyup"?t.keyCode:0}}),h0=te(d0),m0=U({},xu,{pointerId:0,width:0,height:0,pressure:0,tangentialPressure:0,tiltX:0,tiltY:0,twist:0,pointerType:0,isPrimary:0}),sr=te(m0),y0=U({},Il,{touches:0,targetTouches:0,changedTouches:0,altKey:0,metaKey:0,ctrlKey:0,shiftKey:0,getModifierState:ys}),g0=te(y0),v0=U({},Ha,{propertyName:0,elapsedTime:0,pseudoElement:0}),b0=te(v0),S0=U({},xu,{deltaX:function(t){return"deltaX"in t?t.deltaX:"wheelDeltaX"in t?-t.wheelDeltaX:0},deltaY:function(t){return"deltaY"in t?t.deltaY:"wheelDeltaY"in t?-t.wheelDeltaY:"wheelDelta"in t?-t.wheelDelta:0},deltaZ:0,deltaMode:0}),p0=te(S0),_0=U({},Ha,{newState:0,oldState:0}),x0=te(_0),E0=[9,13,27,32],gs=Qe&&"CompositionEvent"in window,en=null;Qe&&"documentMode"in document&&(en=document.documentMode);var T0=Qe&&"TextEvent"in window&&!en,cr=Qe&&(!gs||en&&8<en&&11>=en),fr=" ",rr=!1;function or(t,e){switch(t){case"keyup":return E0.indexOf(e.keyCode)!==-1;case"keydown":return e.keyCode!==229;case"keypress":case"mousedown":case"focusout":return!0;default:return!1}}function dr(t){return t=t.detail,typeof t=="object"&&"data"in t?t.data:null}var dl=!1;function M0(t,e){switch(t){case"compositionend":return dr(e);case"keypress":return e.which!==32?null:(rr=!0,fr);case"textInput":return t=e.data,t===fr&&rr?null:t;default:return null}}function A0(t,e){if(dl)return t==="compositionend"||!gs&&or(t,e)?(t=lr(),bu=os=ca=null,dl=!1,t):null;switch(t){case"paste":return null;case"keypress":if(!(e.ctrlKey||e.altKey||e.metaKey)||e.ctrlKey&&e.altKey){if(e.char&&1<e.char.length)return e.char;if(e.which)return String.fromCharCode(e.which)}return null;case"compositionend":return cr&&e.locale!=="ko"?null:e.data;default:return null}}var R0={color:!0,date:!0,datetime:!0,"datetime-local":!0,email:!0,month:!0,number:!0,password:!0,range:!0,search:!0,tel:!0,text:!0,time:!0,url:!0,week:!0};function hr(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e==="input"?!!R0[t.type]:e==="textarea"}function mr(t,e,a,l){rl?ol?ol.push(l):ol=[l]:rl=l,e=ci(e,"onChange"),0<e.length&&(a=new _u("onChange","change",null,a,l),t.push({event:a,listeners:e}))}var an=null,ln=null;function O0(t){$d(t,0)}function Eu(t){var e=Wl(t);if($f(e))return t}function yr(t,e){if(t==="change")return e}var gr=!1;if(Qe){var vs;if(Qe){var bs="oninput"in document;if(!bs){var vr=document.createElement("div");vr.setAttribute("oninput","return;"),bs=typeof vr.oninput=="function"}vs=bs}else vs=!1;gr=vs&&(!document.documentMode||9<document.documentMode)}function br(){an&&(an.detachEvent("onpropertychange",Sr),ln=an=null)}function Sr(t){if(t.propertyName==="value"&&Eu(ln)){var e=[];mr(e,ln,t,cs(t)),ar(O0,e)}}function N0(t,e,a){t==="focusin"?(br(),an=e,ln=a,an.attachEvent("onpropertychange",Sr)):t==="focusout"&&br()}function w0(t){if(t==="selectionchange"||t==="keyup"||t==="keydown")return Eu(ln)}function D0(t,e){if(t==="click")return Eu(e)}function z0(t,e){if(t==="input"||t==="change")return Eu(e)}function U0(t,e){return t===e&&(t!==0||1/t===1/e)||t!==t&&e!==e}var ce=typeof Object.is=="function"?Object.is:U0;function nn(t,e){if(ce(t,e))return!0;if(typeof t!="object"||t===null||typeof e!="object"||e===null)return!1;var a=Object.keys(t),l=Object.keys(e);if(a.length!==l.length)return!1;for(l=0;l<a.length;l++){var n=a[l];if(!ki.call(e,n)||!ce(t[n],e[n]))return!1}return!0}function pr(t){for(;t&&t.firstChild;)t=t.firstChild;return t}function _r(t,e){var a=pr(t);t=0;for(var l;a;){if(a.nodeType===3){if(l=t+a.textContent.length,t<=e&&l>=e)return{node:a,offset:e-t};t=l}t:{for(;a;){if(a.nextSibling){a=a.nextSibling;break t}a=a.parentNode}a=void 0}a=pr(a)}}function xr(t,e){return t&&e?t===e?!0:t&&t.nodeType===3?!1:e&&e.nodeType===3?xr(t,e.parentNode):"contains"in t?t.contains(e):t.compareDocumentPosition?!!(t.compareDocumentPosition(e)&16):!1:!1}function Er(t){t=t!=null&&t.ownerDocument!=null&&t.ownerDocument.defaultView!=null?t.ownerDocument.defaultView:window;for(var e=gu(t.document);e instanceof t.HTMLIFrameElement;){try{var a=typeof e.contentWindow.location.href=="string"}catch{a=!1}if(a)t=e.contentWindow;else break;e=gu(t.document)}return e}function Ss(t){var e=t&&t.nodeName&&t.nodeName.toLowerCase();return e&&(e==="input"&&(t.type==="text"||t.type==="search"||t.type==="tel"||t.type==="url"||t.type==="password")||e==="textarea"||t.contentEditable==="true")}var j0=Qe&&"documentMode"in document&&11>=document.documentMode,hl=null,ps=null,un=null,_s=!1;function Tr(t,e,a){var l=a.window===a?a.document:a.nodeType===9?a:a.ownerDocument;_s||hl==null||hl!==gu(l)||(l=hl,"selectionStart"in l&&Ss(l)?l={start:l.selectionStart,end:l.selectionEnd}:(l=(l.ownerDocument&&l.ownerDocument.defaultView||window).getSelection(),l={anchorNode:l.anchorNode,anchorOffset:l.anchorOffset,focusNode:l.focusNode,focusOffset:l.focusOffset}),un&&nn(un,l)||(un=l,l=ci(ps,"onSelect"),0<l.length&&(e=new _u("onSelect","select",null,e,a),t.push({event:e,listeners:l}),e.target=hl)))}function Ba(t,e){var a={};return a[t.toLowerCase()]=e.toLowerCase(),a["Webkit"+t]="webkit"+e,a["Moz"+t]="moz"+e,a}var ml={animationend:Ba("Animation","AnimationEnd"),animationiteration:Ba("Animation","AnimationIteration"),animationstart:Ba("Animation","AnimationStart"),transitionrun:Ba("Transition","TransitionRun"),transitionstart:Ba("Transition","TransitionStart"),transitioncancel:Ba("Transition","TransitionCancel"),transitionend:Ba("Transition","TransitionEnd")},xs={},Mr={};Qe&&(Mr=document.createElement("div").style,"AnimationEvent"in window||(delete ml.animationend.animation,delete ml.animationiteration.animation,delete ml.animationstart.animation),"TransitionEvent"in window||delete ml.transitionend.transition);function Ca(t){if(xs[t])return xs[t];if(!ml[t])return t;var e=ml[t],a;for(a in e)if(e.hasOwnProperty(a)&&a in Mr)return xs[t]=e[a];return t}var Ar=Ca("animationend"),Rr=Ca("animationiteration"),Or=Ca("animationstart"),q0=Ca("transitionrun"),H0=Ca("transitionstart"),B0=Ca("transitioncancel"),Nr=Ca("transitionend"),wr=new Map,Es="abort auxClick beforeToggle cancel canPlay canPlayThrough click close contextMenu copy cut drag dragEnd dragEnter dragExit dragLeave dragOver dragStart drop durationChange emptied encrypted ended error gotPointerCapture input invalid keyDown keyPress keyUp load loadedData loadedMetadata loadStart lostPointerCapture mouseDown mouseMove mouseOut mouseOver mouseUp paste pause play playing pointerCancel pointerDown pointerMove pointerOut pointerOver pointerUp progress rateChange reset resize seeked seeking stalled submit suspend timeUpdate touchCancel touchEnd touchStart volumeChange scroll toggle touchMove waiting wheel".split(" ");Es.push("scrollEnd");function Oe(t,e){wr.set(t,e),qa(e,[t])}var Dr=new WeakMap;function Se(t,e){if(typeof t=="object"&&t!==null){var a=Dr.get(t);return a!==void 0?a:(e={value:t,source:e,stack:kf(e)},Dr.set(t,e),e)}return{value:t,source:e,stack:kf(e)}}var pe=[],yl=0,Ts=0;function Tu(){for(var t=yl,e=Ts=yl=0;e<t;){var a=pe[e];pe[e++]=null;var l=pe[e];pe[e++]=null;var n=pe[e];pe[e++]=null;var u=pe[e];if(pe[e++]=null,l!==null&&n!==null){var r=l.pending;r===null?n.next=n:(n.next=r.next,r.next=n),l.pending=n}u!==0&&zr(a,n,u)}}function Mu(t,e,a,l){pe[yl++]=t,pe[yl++]=e,pe[yl++]=a,pe[yl++]=l,Ts|=l,t.lanes|=l,t=t.alternate,t!==null&&(t.lanes|=l)}function Ms(t,e,a,l){return Mu(t,e,a,l),Au(t)}function gl(t,e){return Mu(t,null,null,e),Au(t)}function zr(t,e,a){t.lanes|=a;var l=t.alternate;l!==null&&(l.lanes|=a);for(var n=!1,u=t.return;u!==null;)u.childLanes|=a,l=u.alternate,l!==null&&(l.childLanes|=a),u.tag===22&&(t=u.stateNode,t===null||t._visibility&1||(n=!0)),t=u,u=u.return;return t.tag===3?(u=t.stateNode,n&&e!==null&&(n=31-se(a),t=u.hiddenUpdates,l=t[n],l===null?t[n]=[e]:l.push(e),e.lane=a|536870912),u):null}function Au(t){if(50<Dn)throw Dn=0,Dc=null,Error(s(185));for(var e=t.return;e!==null;)t=e,e=t.return;return t.tag===3?t.stateNode:null}var vl={};function C0(t,e,a,l){this.tag=t,this.key=a,this.sibling=this.child=this.return=this.stateNode=this.type=this.elementType=null,this.index=0,this.refCleanup=this.ref=null,this.pendingProps=e,this.dependencies=this.memoizedState=this.updateQueue=this.memoizedProps=null,this.mode=l,this.subtreeFlags=this.flags=0,this.deletions=null,this.childLanes=this.lanes=0,this.alternate=null}function fe(t,e,a,l){return new C0(t,e,a,l)}function As(t){return t=t.prototype,!(!t||!t.isReactComponent)}function Le(t,e){var a=t.alternate;return a===null?(a=fe(t.tag,e,t.key,t.mode),a.elementType=t.elementType,a.type=t.type,a.stateNode=t.stateNode,a.alternate=t,t.alternate=a):(a.pendingProps=e,a.type=t.type,a.flags=0,a.subtreeFlags=0,a.deletions=null),a.flags=t.flags&65011712,a.childLanes=t.childLanes,a.lanes=t.lanes,a.child=t.child,a.memoizedProps=t.memoizedProps,a.memoizedState=t.memoizedState,a.updateQueue=t.updateQueue,e=t.dependencies,a.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext},a.sibling=t.sibling,a.index=t.index,a.ref=t.ref,a.refCleanup=t.refCleanup,a}function Ur(t,e){t.flags&=65011714;var a=t.alternate;return a===null?(t.childLanes=0,t.lanes=e,t.child=null,t.subtreeFlags=0,t.memoizedProps=null,t.memoizedState=null,t.updateQueue=null,t.dependencies=null,t.stateNode=null):(t.childLanes=a.childLanes,t.lanes=a.lanes,t.child=a.child,t.subtreeFlags=0,t.deletions=null,t.memoizedProps=a.memoizedProps,t.memoizedState=a.memoizedState,t.updateQueue=a.updateQueue,t.type=a.type,e=a.dependencies,t.dependencies=e===null?null:{lanes:e.lanes,firstContext:e.firstContext}),t}function Ru(t,e,a,l,n,u){var r=0;if(l=t,typeof t=="function")As(t)&&(r=1);else if(typeof t=="string")r=Xy(t,a,X.current)?26:t==="html"||t==="head"||t==="body"?27:5;else t:switch(t){case Wt:return t=fe(31,a,e,n),t.elementType=Wt,t.lanes=u,t;case L:return Ya(a.children,n,u,e);case bt:r=8,n|=24;break;case lt:return t=fe(12,a,e,n|2),t.elementType=lt,t.lanes=u,t;case F:return t=fe(13,a,e,n),t.elementType=F,t.lanes=u,t;case zt:return t=fe(19,a,e,n),t.elementType=zt,t.lanes=u,t;default:if(typeof t=="object"&&t!==null)switch(t.$$typeof){case St:case ut:r=10;break t;case gt:r=9;break t;case pt:r=11;break t;case Ut:r=14;break t;case Pt:r=16,l=null;break t}r=29,a=Error(s(130,t===null?"null":typeof t,"")),l=null}return e=fe(r,a,e,n),e.elementType=t,e.type=l,e.lanes=u,e}function Ya(t,e,a,l){return t=fe(7,t,l,e),t.lanes=a,t}function Rs(t,e,a){return t=fe(6,t,null,e),t.lanes=a,t}function Os(t,e,a){return e=fe(4,t.children!==null?t.children:[],t.key,e),e.lanes=a,e.stateNode={containerInfo:t.containerInfo,pendingChildren:null,implementation:t.implementation},e}var bl=[],Sl=0,Ou=null,Nu=0,_e=[],xe=0,Xa=null,Ze=1,Ve="";function Ga(t,e){bl[Sl++]=Nu,bl[Sl++]=Ou,Ou=t,Nu=e}function jr(t,e,a){_e[xe++]=Ze,_e[xe++]=Ve,_e[xe++]=Xa,Xa=t;var l=Ze;t=Ve;var n=32-se(l)-1;l&=~(1<<n),a+=1;var u=32-se(e)+n;if(30<u){var r=n-n%5;u=(l&(1<<r)-1).toString(32),l>>=r,n-=r,Ze=1<<32-se(e)+n|a<<n|l,Ve=u+t}else Ze=1<<u|a<<n|l,Ve=t}function Ns(t){t.return!==null&&(Ga(t,1),jr(t,1,0))}function ws(t){for(;t===Ou;)Ou=bl[--Sl],bl[Sl]=null,Nu=bl[--Sl],bl[Sl]=null;for(;t===Xa;)Xa=_e[--xe],_e[xe]=null,Ve=_e[--xe],_e[xe]=null,Ze=_e[--xe],_e[xe]=null}var Ft=null,Mt=null,ct=!1,Qa=null,je=!1,Ds=Error(s(519));function La(t){var e=Error(s(418,""));throw fn(Se(e,t)),Ds}function qr(t){var e=t.stateNode,a=t.type,l=t.memoizedProps;switch(e[Vt]=t,e[It]=l,a){case"dialog":at("cancel",e),at("close",e);break;case"iframe":case"object":case"embed":at("load",e);break;case"video":case"audio":for(a=0;a<Un.length;a++)at(Un[a],e);break;case"source":at("error",e);break;case"img":case"image":case"link":at("error",e),at("load",e);break;case"details":at("toggle",e);break;case"input":at("invalid",e),Wf(e,l.value,l.defaultValue,l.checked,l.defaultChecked,l.type,l.name,!0),yu(e);break;case"select":at("invalid",e);break;case"textarea":at("invalid",e),Pf(e,l.value,l.defaultValue,l.children),yu(e)}a=l.children,typeof a!="string"&&typeof a!="number"&&typeof a!="bigint"||e.textContent===""+a||l.suppressHydrationWarning===!0||Id(e.textContent,a)?(l.popover!=null&&(at("beforetoggle",e),at("toggle",e)),l.onScroll!=null&&at("scroll",e),l.onScrollEnd!=null&&at("scrollend",e),l.onClick!=null&&(e.onclick=fi),e=!0):e=!1,e||La(t)}function Hr(t){for(Ft=t.return;Ft;)switch(Ft.tag){case 5:case 13:je=!1;return;case 27:case 3:je=!0;return;default:Ft=Ft.return}}function sn(t){if(t!==Ft)return!1;if(!ct)return Hr(t),ct=!0,!1;var e=t.tag,a;if((a=e!==3&&e!==27)&&((a=e===5)&&(a=t.type,a=!(a!=="form"&&a!=="button")||kc(t.type,t.memoizedProps)),a=!a),a&&Mt&&La(t),Hr(t),e===13){if(t=t.memoizedState,t=t!==null?t.dehydrated:null,!t)throw Error(s(317));t:{for(t=t.nextSibling,e=0;t;){if(t.nodeType===8)if(a=t.data,a==="/$"){if(e===0){Mt=we(t.nextSibling);break t}e--}else a!=="$"&&a!=="$!"&&a!=="$?"||e++;t=t.nextSibling}Mt=null}}else e===27?(e=Mt,Ta(t.type)?(t=Fc,Fc=null,Mt=t):Mt=e):Mt=Ft?we(t.stateNode.nextSibling):null;return!0}function cn(){Mt=Ft=null,ct=!1}function Br(){var t=Qa;return t!==null&&(le===null?le=t:le.push.apply(le,t),Qa=null),t}function fn(t){Qa===null?Qa=[t]:Qa.push(t)}var zs=D(null),Za=null,Ke=null;function fa(t,e,a){j(zs,e._currentValue),e._currentValue=a}function ke(t){t._currentValue=zs.current,B(zs)}function Us(t,e,a){for(;t!==null;){var l=t.alternate;if((t.childLanes&e)!==e?(t.childLanes|=e,l!==null&&(l.childLanes|=e)):l!==null&&(l.childLanes&e)!==e&&(l.childLanes|=e),t===a)break;t=t.return}}function js(t,e,a,l){var n=t.child;for(n!==null&&(n.return=t);n!==null;){var u=n.dependencies;if(u!==null){var r=n.child;u=u.firstContext;t:for(;u!==null;){var d=u;u=n;for(var h=0;h<e.length;h++)if(d.context===e[h]){u.lanes|=a,d=u.alternate,d!==null&&(d.lanes|=a),Us(u.return,a,t),l||(r=null);break t}u=d.next}}else if(n.tag===18){if(r=n.return,r===null)throw Error(s(341));r.lanes|=a,u=r.alternate,u!==null&&(u.lanes|=a),Us(r,a,t),r=null}else r=n.child;if(r!==null)r.return=n;else for(r=n;r!==null;){if(r===t){r=null;break}if(n=r.sibling,n!==null){n.return=r.return,r=n;break}r=r.return}n=r}}function rn(t,e,a,l){t=null;for(var n=e,u=!1;n!==null;){if(!u){if((n.flags&524288)!==0)u=!0;else if((n.flags&262144)!==0)break}if(n.tag===10){var r=n.alternate;if(r===null)throw Error(s(387));if(r=r.memoizedProps,r!==null){var d=n.type;ce(n.pendingProps.value,r.value)||(t!==null?t.push(d):t=[d])}}else if(n===ue.current){if(r=n.alternate,r===null)throw Error(s(387));r.memoizedState.memoizedState!==n.memoizedState.memoizedState&&(t!==null?t.push(Yn):t=[Yn])}n=n.return}t!==null&&js(e,t,a,l),e.flags|=262144}function wu(t){for(t=t.firstContext;t!==null;){if(!ce(t.context._currentValue,t.memoizedValue))return!0;t=t.next}return!1}function Va(t){Za=t,Ke=null,t=t.dependencies,t!==null&&(t.firstContext=null)}function Kt(t){return Cr(Za,t)}function Du(t,e){return Za===null&&Va(t),Cr(t,e)}function Cr(t,e){var a=e._currentValue;if(e={context:e,memoizedValue:a,next:null},Ke===null){if(t===null)throw Error(s(308));Ke=e,t.dependencies={lanes:0,firstContext:e},t.flags|=524288}else Ke=Ke.next=e;return a}var Y0=typeof AbortController<"u"?AbortController:function(){var t=[],e=this.signal={aborted:!1,addEventListener:function(a,l){t.push(l)}};this.abort=function(){e.aborted=!0,t.forEach(function(a){return a()})}},X0=f.unstable_scheduleCallback,G0=f.unstable_NormalPriority,jt={$$typeof:ut,Consumer:null,Provider:null,_currentValue:null,_currentValue2:null,_threadCount:0};function qs(){return{controller:new Y0,data:new Map,refCount:0}}function on(t){t.refCount--,t.refCount===0&&X0(G0,function(){t.controller.abort()})}var dn=null,Hs=0,pl=0,_l=null;function Q0(t,e){if(dn===null){var a=dn=[];Hs=0,pl=Cc(),_l={status:"pending",value:void 0,then:function(l){a.push(l)}}}return Hs++,e.then(Yr,Yr),e}function Yr(){if(--Hs===0&&dn!==null){_l!==null&&(_l.status="fulfilled");var t=dn;dn=null,pl=0,_l=null;for(var e=0;e<t.length;e++)(0,t[e])()}}function L0(t,e){var a=[],l={status:"pending",value:null,reason:null,then:function(n){a.push(n)}};return t.then(function(){l.status="fulfilled",l.value=e;for(var n=0;n<a.length;n++)(0,a[n])(e)},function(n){for(l.status="rejected",l.reason=n,n=0;n<a.length;n++)(0,a[n])(void 0)}),l}var Xr=A.S;A.S=function(t,e){typeof e=="object"&&e!==null&&typeof e.then=="function"&&Q0(t,e),Xr!==null&&Xr(t,e)};var Ka=D(null);function Bs(){var t=Ka.current;return t!==null?t:vt.pooledCache}function zu(t,e){e===null?j(Ka,Ka.current):j(Ka,e.pool)}function Gr(){var t=Bs();return t===null?null:{parent:jt._currentValue,pool:t}}var hn=Error(s(460)),Qr=Error(s(474)),Uu=Error(s(542)),Cs={then:function(){}};function Lr(t){return t=t.status,t==="fulfilled"||t==="rejected"}function ju(){}function Zr(t,e,a){switch(a=t[a],a===void 0?t.push(e):a!==e&&(e.then(ju,ju),e=a),e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Kr(t),t;default:if(typeof e.status=="string")e.then(ju,ju);else{if(t=vt,t!==null&&100<t.shellSuspendCounter)throw Error(s(482));t=e,t.status="pending",t.then(function(l){if(e.status==="pending"){var n=e;n.status="fulfilled",n.value=l}},function(l){if(e.status==="pending"){var n=e;n.status="rejected",n.reason=l}})}switch(e.status){case"fulfilled":return e.value;case"rejected":throw t=e.reason,Kr(t),t}throw mn=e,hn}}var mn=null;function Vr(){if(mn===null)throw Error(s(459));var t=mn;return mn=null,t}function Kr(t){if(t===hn||t===Uu)throw Error(s(483))}var ra=!1;function Ys(t){t.updateQueue={baseState:t.memoizedState,firstBaseUpdate:null,lastBaseUpdate:null,shared:{pending:null,lanes:0,hiddenCallbacks:null},callbacks:null}}function Xs(t,e){t=t.updateQueue,e.updateQueue===t&&(e.updateQueue={baseState:t.baseState,firstBaseUpdate:t.firstBaseUpdate,lastBaseUpdate:t.lastBaseUpdate,shared:t.shared,callbacks:null})}function oa(t){return{lane:t,tag:0,payload:null,callback:null,next:null}}function da(t,e,a){var l=t.updateQueue;if(l===null)return null;if(l=l.shared,(ft&2)!==0){var n=l.pending;return n===null?e.next=e:(e.next=n.next,n.next=e),l.pending=e,e=Au(t),zr(t,null,a),e}return Mu(t,l,e,a),Au(t)}function yn(t,e,a){if(e=e.updateQueue,e!==null&&(e=e.shared,(a&4194048)!==0)){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,Yf(t,a)}}function Gs(t,e){var a=t.updateQueue,l=t.alternate;if(l!==null&&(l=l.updateQueue,a===l)){var n=null,u=null;if(a=a.firstBaseUpdate,a!==null){do{var r={lane:a.lane,tag:a.tag,payload:a.payload,callback:null,next:null};u===null?n=u=r:u=u.next=r,a=a.next}while(a!==null);u===null?n=u=e:u=u.next=e}else n=u=e;a={baseState:l.baseState,firstBaseUpdate:n,lastBaseUpdate:u,shared:l.shared,callbacks:l.callbacks},t.updateQueue=a;return}t=a.lastBaseUpdate,t===null?a.firstBaseUpdate=e:t.next=e,a.lastBaseUpdate=e}var Qs=!1;function gn(){if(Qs){var t=_l;if(t!==null)throw t}}function vn(t,e,a,l){Qs=!1;var n=t.updateQueue;ra=!1;var u=n.firstBaseUpdate,r=n.lastBaseUpdate,d=n.shared.pending;if(d!==null){n.shared.pending=null;var h=d,S=h.next;h.next=null,r===null?u=S:r.next=S,r=h;var M=t.alternate;M!==null&&(M=M.updateQueue,d=M.lastBaseUpdate,d!==r&&(d===null?M.firstBaseUpdate=S:d.next=S,M.lastBaseUpdate=h))}if(u!==null){var N=n.baseState;r=0,M=S=h=null,d=u;do{var p=d.lane&-536870913,_=p!==d.lane;if(_?(nt&p)===p:(l&p)===p){p!==0&&p===pl&&(Qs=!0),M!==null&&(M=M.next={lane:0,tag:d.tag,payload:d.payload,callback:null,next:null});t:{var V=t,G=d;p=e;var ht=a;switch(G.tag){case 1:if(V=G.payload,typeof V=="function"){N=V.call(ht,N,p);break t}N=V;break t;case 3:V.flags=V.flags&-65537|128;case 0:if(V=G.payload,p=typeof V=="function"?V.call(ht,N,p):V,p==null)break t;N=U({},N,p);break t;case 2:ra=!0}}p=d.callback,p!==null&&(t.flags|=64,_&&(t.flags|=8192),_=n.callbacks,_===null?n.callbacks=[p]:_.push(p))}else _={lane:p,tag:d.tag,payload:d.payload,callback:d.callback,next:null},M===null?(S=M=_,h=N):M=M.next=_,r|=p;if(d=d.next,d===null){if(d=n.shared.pending,d===null)break;_=d,d=_.next,_.next=null,n.lastBaseUpdate=_,n.shared.pending=null}}while(!0);M===null&&(h=N),n.baseState=h,n.firstBaseUpdate=S,n.lastBaseUpdate=M,u===null&&(n.shared.lanes=0),pa|=r,t.lanes=r,t.memoizedState=N}}function kr(t,e){if(typeof t!="function")throw Error(s(191,t));t.call(e)}function Jr(t,e){var a=t.callbacks;if(a!==null)for(t.callbacks=null,t=0;t<a.length;t++)kr(a[t],e)}var xl=D(null),qu=D(0);function $r(t,e){t=ta,j(qu,t),j(xl,e),ta=t|e.baseLanes}function Ls(){j(qu,ta),j(xl,xl.current)}function Zs(){ta=qu.current,B(xl),B(qu)}var ha=0,P=null,ot=null,Nt=null,Hu=!1,El=!1,ka=!1,Bu=0,bn=0,Tl=null,Z0=0;function Rt(){throw Error(s(321))}function Vs(t,e){if(e===null)return!1;for(var a=0;a<e.length&&a<t.length;a++)if(!ce(t[a],e[a]))return!1;return!0}function Ks(t,e,a,l,n,u){return ha=u,P=e,e.memoizedState=null,e.updateQueue=null,e.lanes=0,A.H=t===null||t.memoizedState===null?Uo:jo,ka=!1,u=a(l,n),ka=!1,El&&(u=Fr(e,a,l,n)),Wr(t),u}function Wr(t){A.H=Lu;var e=ot!==null&&ot.next!==null;if(ha=0,Nt=ot=P=null,Hu=!1,bn=0,Tl=null,e)throw Error(s(300));t===null||Bt||(t=t.dependencies,t!==null&&wu(t)&&(Bt=!0))}function Fr(t,e,a,l){P=t;var n=0;do{if(El&&(Tl=null),bn=0,El=!1,25<=n)throw Error(s(301));if(n+=1,Nt=ot=null,t.updateQueue!=null){var u=t.updateQueue;u.lastEffect=null,u.events=null,u.stores=null,u.memoCache!=null&&(u.memoCache.index=0)}A.H=F0,u=e(a,l)}while(El);return u}function V0(){var t=A.H,e=t.useState()[0];return e=typeof e.then=="function"?Sn(e):e,t=t.useState()[0],(ot!==null?ot.memoizedState:null)!==t&&(P.flags|=1024),e}function ks(){var t=Bu!==0;return Bu=0,t}function Js(t,e,a){e.updateQueue=t.updateQueue,e.flags&=-2053,t.lanes&=~a}function $s(t){if(Hu){for(t=t.memoizedState;t!==null;){var e=t.queue;e!==null&&(e.pending=null),t=t.next}Hu=!1}ha=0,Nt=ot=P=null,El=!1,bn=Bu=0,Tl=null}function ee(){var t={memoizedState:null,baseState:null,baseQueue:null,queue:null,next:null};return Nt===null?P.memoizedState=Nt=t:Nt=Nt.next=t,Nt}function wt(){if(ot===null){var t=P.alternate;t=t!==null?t.memoizedState:null}else t=ot.next;var e=Nt===null?P.memoizedState:Nt.next;if(e!==null)Nt=e,ot=t;else{if(t===null)throw P.alternate===null?Error(s(467)):Error(s(310));ot=t,t={memoizedState:ot.memoizedState,baseState:ot.baseState,baseQueue:ot.baseQueue,queue:ot.queue,next:null},Nt===null?P.memoizedState=Nt=t:Nt=Nt.next=t}return Nt}function Ws(){return{lastEffect:null,events:null,stores:null,memoCache:null}}function Sn(t){var e=bn;return bn+=1,Tl===null&&(Tl=[]),t=Zr(Tl,t,e),e=P,(Nt===null?e.memoizedState:Nt.next)===null&&(e=e.alternate,A.H=e===null||e.memoizedState===null?Uo:jo),t}function Cu(t){if(t!==null&&typeof t=="object"){if(typeof t.then=="function")return Sn(t);if(t.$$typeof===ut)return Kt(t)}throw Error(s(438,String(t)))}function Fs(t){var e=null,a=P.updateQueue;if(a!==null&&(e=a.memoCache),e==null){var l=P.alternate;l!==null&&(l=l.updateQueue,l!==null&&(l=l.memoCache,l!=null&&(e={data:l.data.map(function(n){return n.slice()}),index:0})))}if(e==null&&(e={data:[],index:0}),a===null&&(a=Ws(),P.updateQueue=a),a.memoCache=e,a=e.data[e.index],a===void 0)for(a=e.data[e.index]=Array(t),l=0;l<t;l++)a[l]=el;return e.index++,a}function Je(t,e){return typeof e=="function"?e(t):e}function Yu(t){var e=wt();return Ps(e,ot,t)}function Ps(t,e,a){var l=t.queue;if(l===null)throw Error(s(311));l.lastRenderedReducer=a;var n=t.baseQueue,u=l.pending;if(u!==null){if(n!==null){var r=n.next;n.next=u.next,u.next=r}e.baseQueue=n=u,l.pending=null}if(u=t.baseState,n===null)t.memoizedState=u;else{e=n.next;var d=r=null,h=null,S=e,M=!1;do{var N=S.lane&-536870913;if(N!==S.lane?(nt&N)===N:(ha&N)===N){var p=S.revertLane;if(p===0)h!==null&&(h=h.next={lane:0,revertLane:0,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null}),N===pl&&(M=!0);else if((ha&p)===p){S=S.next,p===pl&&(M=!0);continue}else N={lane:0,revertLane:S.revertLane,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null},h===null?(d=h=N,r=u):h=h.next=N,P.lanes|=p,pa|=p;N=S.action,ka&&a(u,N),u=S.hasEagerState?S.eagerState:a(u,N)}else p={lane:N,revertLane:S.revertLane,action:S.action,hasEagerState:S.hasEagerState,eagerState:S.eagerState,next:null},h===null?(d=h=p,r=u):h=h.next=p,P.lanes|=N,pa|=N;S=S.next}while(S!==null&&S!==e);if(h===null?r=u:h.next=d,!ce(u,t.memoizedState)&&(Bt=!0,M&&(a=_l,a!==null)))throw a;t.memoizedState=u,t.baseState=r,t.baseQueue=h,l.lastRenderedState=u}return n===null&&(l.lanes=0),[t.memoizedState,l.dispatch]}function Is(t){var e=wt(),a=e.queue;if(a===null)throw Error(s(311));a.lastRenderedReducer=t;var l=a.dispatch,n=a.pending,u=e.memoizedState;if(n!==null){a.pending=null;var r=n=n.next;do u=t(u,r.action),r=r.next;while(r!==n);ce(u,e.memoizedState)||(Bt=!0),e.memoizedState=u,e.baseQueue===null&&(e.baseState=u),a.lastRenderedState=u}return[u,l]}function Pr(t,e,a){var l=P,n=wt(),u=ct;if(u){if(a===void 0)throw Error(s(407));a=a()}else a=e();var r=!ce((ot||n).memoizedState,a);r&&(n.memoizedState=a,Bt=!0),n=n.queue;var d=eo.bind(null,l,n,t);if(pn(2048,8,d,[t]),n.getSnapshot!==e||r||Nt!==null&&Nt.memoizedState.tag&1){if(l.flags|=2048,Ml(9,Xu(),to.bind(null,l,n,a,e),null),vt===null)throw Error(s(349));u||(ha&124)!==0||Ir(l,e,a)}return a}function Ir(t,e,a){t.flags|=16384,t={getSnapshot:e,value:a},e=P.updateQueue,e===null?(e=Ws(),P.updateQueue=e,e.stores=[t]):(a=e.stores,a===null?e.stores=[t]:a.push(t))}function to(t,e,a,l){e.value=a,e.getSnapshot=l,ao(e)&&lo(t)}function eo(t,e,a){return a(function(){ao(e)&&lo(t)})}function ao(t){var e=t.getSnapshot;t=t.value;try{var a=e();return!ce(t,a)}catch{return!0}}function lo(t){var e=gl(t,2);e!==null&&me(e,t,2)}function tc(t){var e=ee();if(typeof t=="function"){var a=t;if(t=a(),ka){ia(!0);try{a()}finally{ia(!1)}}}return e.memoizedState=e.baseState=t,e.queue={pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:t},e}function no(t,e,a,l){return t.baseState=a,Ps(t,ot,typeof l=="function"?l:Je)}function K0(t,e,a,l,n){if(Qu(t))throw Error(s(485));if(t=e.action,t!==null){var u={payload:n,action:t,next:null,isTransition:!0,status:"pending",value:null,reason:null,listeners:[],then:function(r){u.listeners.push(r)}};A.T!==null?a(!0):u.isTransition=!1,l(u),a=e.pending,a===null?(u.next=e.pending=u,uo(e,u)):(u.next=a.next,e.pending=a.next=u)}}function uo(t,e){var a=e.action,l=e.payload,n=t.state;if(e.isTransition){var u=A.T,r={};A.T=r;try{var d=a(n,l),h=A.S;h!==null&&h(r,d),io(t,e,d)}catch(S){ec(t,e,S)}finally{A.T=u}}else try{u=a(n,l),io(t,e,u)}catch(S){ec(t,e,S)}}function io(t,e,a){a!==null&&typeof a=="object"&&typeof a.then=="function"?a.then(function(l){so(t,e,l)},function(l){return ec(t,e,l)}):so(t,e,a)}function so(t,e,a){e.status="fulfilled",e.value=a,co(e),t.state=a,e=t.pending,e!==null&&(a=e.next,a===e?t.pending=null:(a=a.next,e.next=a,uo(t,a)))}function ec(t,e,a){var l=t.pending;if(t.pending=null,l!==null){l=l.next;do e.status="rejected",e.reason=a,co(e),e=e.next;while(e!==l)}t.action=null}function co(t){t=t.listeners;for(var e=0;e<t.length;e++)(0,t[e])()}function fo(t,e){return e}function ro(t,e){if(ct){var a=vt.formState;if(a!==null){t:{var l=P;if(ct){if(Mt){e:{for(var n=Mt,u=je;n.nodeType!==8;){if(!u){n=null;break e}if(n=we(n.nextSibling),n===null){n=null;break e}}u=n.data,n=u==="F!"||u==="F"?n:null}if(n){Mt=we(n.nextSibling),l=n.data==="F!";break t}}La(l)}l=!1}l&&(e=a[0])}}return a=ee(),a.memoizedState=a.baseState=e,l={pending:null,lanes:0,dispatch:null,lastRenderedReducer:fo,lastRenderedState:e},a.queue=l,a=wo.bind(null,P,l),l.dispatch=a,l=tc(!1),u=ic.bind(null,P,!1,l.queue),l=ee(),n={state:e,dispatch:null,action:t,pending:null},l.queue=n,a=K0.bind(null,P,n,u,a),n.dispatch=a,l.memoizedState=t,[e,a,!1]}function oo(t){var e=wt();return ho(e,ot,t)}function ho(t,e,a){if(e=Ps(t,e,fo)[0],t=Yu(Je)[0],typeof e=="object"&&e!==null&&typeof e.then=="function")try{var l=Sn(e)}catch(r){throw r===hn?Uu:r}else l=e;e=wt();var n=e.queue,u=n.dispatch;return a!==e.memoizedState&&(P.flags|=2048,Ml(9,Xu(),k0.bind(null,n,a),null)),[l,u,t]}function k0(t,e){t.action=e}function mo(t){var e=wt(),a=ot;if(a!==null)return ho(e,a,t);wt(),e=e.memoizedState,a=wt();var l=a.queue.dispatch;return a.memoizedState=t,[e,l,!1]}function Ml(t,e,a,l){return t={tag:t,create:a,deps:l,inst:e,next:null},e=P.updateQueue,e===null&&(e=Ws(),P.updateQueue=e),a=e.lastEffect,a===null?e.lastEffect=t.next=t:(l=a.next,a.next=t,t.next=l,e.lastEffect=t),t}function Xu(){return{destroy:void 0,resource:void 0}}function yo(){return wt().memoizedState}function Gu(t,e,a,l){var n=ee();l=l===void 0?null:l,P.flags|=t,n.memoizedState=Ml(1|e,Xu(),a,l)}function pn(t,e,a,l){var n=wt();l=l===void 0?null:l;var u=n.memoizedState.inst;ot!==null&&l!==null&&Vs(l,ot.memoizedState.deps)?n.memoizedState=Ml(e,u,a,l):(P.flags|=t,n.memoizedState=Ml(1|e,u,a,l))}function go(t,e){Gu(8390656,8,t,e)}function vo(t,e){pn(2048,8,t,e)}function bo(t,e){return pn(4,2,t,e)}function So(t,e){return pn(4,4,t,e)}function po(t,e){if(typeof e=="function"){t=t();var a=e(t);return function(){typeof a=="function"?a():e(null)}}if(e!=null)return t=t(),e.current=t,function(){e.current=null}}function _o(t,e,a){a=a!=null?a.concat([t]):null,pn(4,4,po.bind(null,e,t),a)}function ac(){}function xo(t,e){var a=wt();e=e===void 0?null:e;var l=a.memoizedState;return e!==null&&Vs(e,l[1])?l[0]:(a.memoizedState=[t,e],t)}function Eo(t,e){var a=wt();e=e===void 0?null:e;var l=a.memoizedState;if(e!==null&&Vs(e,l[1]))return l[0];if(l=t(),ka){ia(!0);try{t()}finally{ia(!1)}}return a.memoizedState=[l,e],l}function lc(t,e,a){return a===void 0||(ha&1073741824)!==0?t.memoizedState=e:(t.memoizedState=a,t=Ad(),P.lanes|=t,pa|=t,a)}function To(t,e,a,l){return ce(a,e)?a:xl.current!==null?(t=lc(t,a,l),ce(t,e)||(Bt=!0),t):(ha&42)===0?(Bt=!0,t.memoizedState=a):(t=Ad(),P.lanes|=t,pa|=t,e)}function Mo(t,e,a,l,n){var u=q.p;q.p=u!==0&&8>u?u:8;var r=A.T,d={};A.T=d,ic(t,!1,e,a);try{var h=n(),S=A.S;if(S!==null&&S(d,h),h!==null&&typeof h=="object"&&typeof h.then=="function"){var M=L0(h,l);_n(t,e,M,he(t))}else _n(t,e,l,he(t))}catch(N){_n(t,e,{then:function(){},status:"rejected",reason:N},he())}finally{q.p=u,A.T=r}}function J0(){}function nc(t,e,a,l){if(t.tag!==5)throw Error(s(476));var n=Ao(t).queue;Mo(t,n,e,Z,a===null?J0:function(){return Ro(t),a(l)})}function Ao(t){var e=t.memoizedState;if(e!==null)return e;e={memoizedState:Z,baseState:Z,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:Z},next:null};var a={};return e.next={memoizedState:a,baseState:a,baseQueue:null,queue:{pending:null,lanes:0,dispatch:null,lastRenderedReducer:Je,lastRenderedState:a},next:null},t.memoizedState=e,t=t.alternate,t!==null&&(t.memoizedState=e),e}function Ro(t){var e=Ao(t).next.queue;_n(t,e,{},he())}function uc(){return Kt(Yn)}function Oo(){return wt().memoizedState}function No(){return wt().memoizedState}function $0(t){for(var e=t.return;e!==null;){switch(e.tag){case 24:case 3:var a=he();t=oa(a);var l=da(e,t,a);l!==null&&(me(l,e,a),yn(l,e,a)),e={cache:qs()},t.payload=e;return}e=e.return}}function W0(t,e,a){var l=he();a={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null},Qu(t)?Do(e,a):(a=Ms(t,e,a,l),a!==null&&(me(a,t,l),zo(a,e,l)))}function wo(t,e,a){var l=he();_n(t,e,a,l)}function _n(t,e,a,l){var n={lane:l,revertLane:0,action:a,hasEagerState:!1,eagerState:null,next:null};if(Qu(t))Do(e,n);else{var u=t.alternate;if(t.lanes===0&&(u===null||u.lanes===0)&&(u=e.lastRenderedReducer,u!==null))try{var r=e.lastRenderedState,d=u(r,a);if(n.hasEagerState=!0,n.eagerState=d,ce(d,r))return Mu(t,e,n,0),vt===null&&Tu(),!1}catch{}finally{}if(a=Ms(t,e,n,l),a!==null)return me(a,t,l),zo(a,e,l),!0}return!1}function ic(t,e,a,l){if(l={lane:2,revertLane:Cc(),action:l,hasEagerState:!1,eagerState:null,next:null},Qu(t)){if(e)throw Error(s(479))}else e=Ms(t,a,l,2),e!==null&&me(e,t,2)}function Qu(t){var e=t.alternate;return t===P||e!==null&&e===P}function Do(t,e){El=Hu=!0;var a=t.pending;a===null?e.next=e:(e.next=a.next,a.next=e),t.pending=e}function zo(t,e,a){if((a&4194048)!==0){var l=e.lanes;l&=t.pendingLanes,a|=l,e.lanes=a,Yf(t,a)}}var Lu={readContext:Kt,use:Cu,useCallback:Rt,useContext:Rt,useEffect:Rt,useImperativeHandle:Rt,useLayoutEffect:Rt,useInsertionEffect:Rt,useMemo:Rt,useReducer:Rt,useRef:Rt,useState:Rt,useDebugValue:Rt,useDeferredValue:Rt,useTransition:Rt,useSyncExternalStore:Rt,useId:Rt,useHostTransitionStatus:Rt,useFormState:Rt,useActionState:Rt,useOptimistic:Rt,useMemoCache:Rt,useCacheRefresh:Rt},Uo={readContext:Kt,use:Cu,useCallback:function(t,e){return ee().memoizedState=[t,e===void 0?null:e],t},useContext:Kt,useEffect:go,useImperativeHandle:function(t,e,a){a=a!=null?a.concat([t]):null,Gu(4194308,4,po.bind(null,e,t),a)},useLayoutEffect:function(t,e){return Gu(4194308,4,t,e)},useInsertionEffect:function(t,e){Gu(4,2,t,e)},useMemo:function(t,e){var a=ee();e=e===void 0?null:e;var l=t();if(ka){ia(!0);try{t()}finally{ia(!1)}}return a.memoizedState=[l,e],l},useReducer:function(t,e,a){var l=ee();if(a!==void 0){var n=a(e);if(ka){ia(!0);try{a(e)}finally{ia(!1)}}}else n=e;return l.memoizedState=l.baseState=n,t={pending:null,lanes:0,dispatch:null,lastRenderedReducer:t,lastRenderedState:n},l.queue=t,t=t.dispatch=W0.bind(null,P,t),[l.memoizedState,t]},useRef:function(t){var e=ee();return t={current:t},e.memoizedState=t},useState:function(t){t=tc(t);var e=t.queue,a=wo.bind(null,P,e);return e.dispatch=a,[t.memoizedState,a]},useDebugValue:ac,useDeferredValue:function(t,e){var a=ee();return lc(a,t,e)},useTransition:function(){var t=tc(!1);return t=Mo.bind(null,P,t.queue,!0,!1),ee().memoizedState=t,[!1,t]},useSyncExternalStore:function(t,e,a){var l=P,n=ee();if(ct){if(a===void 0)throw Error(s(407));a=a()}else{if(a=e(),vt===null)throw Error(s(349));(nt&124)!==0||Ir(l,e,a)}n.memoizedState=a;var u={value:a,getSnapshot:e};return n.queue=u,go(eo.bind(null,l,u,t),[t]),l.flags|=2048,Ml(9,Xu(),to.bind(null,l,u,a,e),null),a},useId:function(){var t=ee(),e=vt.identifierPrefix;if(ct){var a=Ve,l=Ze;a=(l&~(1<<32-se(l)-1)).toString(32)+a,e="«"+e+"R"+a,a=Bu++,0<a&&(e+="H"+a.toString(32)),e+="»"}else a=Z0++,e="«"+e+"r"+a.toString(32)+"»";return t.memoizedState=e},useHostTransitionStatus:uc,useFormState:ro,useActionState:ro,useOptimistic:function(t){var e=ee();e.memoizedState=e.baseState=t;var a={pending:null,lanes:0,dispatch:null,lastRenderedReducer:null,lastRenderedState:null};return e.queue=a,e=ic.bind(null,P,!0,a),a.dispatch=e,[t,e]},useMemoCache:Fs,useCacheRefresh:function(){return ee().memoizedState=$0.bind(null,P)}},jo={readContext:Kt,use:Cu,useCallback:xo,useContext:Kt,useEffect:vo,useImperativeHandle:_o,useInsertionEffect:bo,useLayoutEffect:So,useMemo:Eo,useReducer:Yu,useRef:yo,useState:function(){return Yu(Je)},useDebugValue:ac,useDeferredValue:function(t,e){var a=wt();return To(a,ot.memoizedState,t,e)},useTransition:function(){var t=Yu(Je)[0],e=wt().memoizedState;return[typeof t=="boolean"?t:Sn(t),e]},useSyncExternalStore:Pr,useId:Oo,useHostTransitionStatus:uc,useFormState:oo,useActionState:oo,useOptimistic:function(t,e){var a=wt();return no(a,ot,t,e)},useMemoCache:Fs,useCacheRefresh:No},F0={readContext:Kt,use:Cu,useCallback:xo,useContext:Kt,useEffect:vo,useImperativeHandle:_o,useInsertionEffect:bo,useLayoutEffect:So,useMemo:Eo,useReducer:Is,useRef:yo,useState:function(){return Is(Je)},useDebugValue:ac,useDeferredValue:function(t,e){var a=wt();return ot===null?lc(a,t,e):To(a,ot.memoizedState,t,e)},useTransition:function(){var t=Is(Je)[0],e=wt().memoizedState;return[typeof t=="boolean"?t:Sn(t),e]},useSyncExternalStore:Pr,useId:Oo,useHostTransitionStatus:uc,useFormState:mo,useActionState:mo,useOptimistic:function(t,e){var a=wt();return ot!==null?no(a,ot,t,e):(a.baseState=t,[t,a.queue.dispatch])},useMemoCache:Fs,useCacheRefresh:No},Al=null,xn=0;function Zu(t){var e=xn;return xn+=1,Al===null&&(Al=[]),Zr(Al,t,e)}function En(t,e){e=e.props.ref,t.ref=e!==void 0?e:null}function Vu(t,e){throw e.$$typeof===K?Error(s(525)):(t=Object.prototype.toString.call(e),Error(s(31,t==="[object Object]"?"object with keys {"+Object.keys(e).join(", ")+"}":t)))}function qo(t){var e=t._init;return e(t._payload)}function Ho(t){function e(v,g){if(t){var b=v.deletions;b===null?(v.deletions=[g],v.flags|=16):b.push(g)}}function a(v,g){if(!t)return null;for(;g!==null;)e(v,g),g=g.sibling;return null}function l(v){for(var g=new Map;v!==null;)v.key!==null?g.set(v.key,v):g.set(v.index,v),v=v.sibling;return g}function n(v,g){return v=Le(v,g),v.index=0,v.sibling=null,v}function u(v,g,b){return v.index=b,t?(b=v.alternate,b!==null?(b=b.index,b<g?(v.flags|=67108866,g):b):(v.flags|=67108866,g)):(v.flags|=1048576,g)}function r(v){return t&&v.alternate===null&&(v.flags|=67108866),v}function d(v,g,b,R){return g===null||g.tag!==6?(g=Rs(b,v.mode,R),g.return=v,g):(g=n(g,b),g.return=v,g)}function h(v,g,b,R){var C=b.type;return C===L?M(v,g,b.props.children,R,b.key):g!==null&&(g.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Pt&&qo(C)===g.type)?(g=n(g,b.props),En(g,b),g.return=v,g):(g=Ru(b.type,b.key,b.props,null,v.mode,R),En(g,b),g.return=v,g)}function S(v,g,b,R){return g===null||g.tag!==4||g.stateNode.containerInfo!==b.containerInfo||g.stateNode.implementation!==b.implementation?(g=Os(b,v.mode,R),g.return=v,g):(g=n(g,b.children||[]),g.return=v,g)}function M(v,g,b,R,C){return g===null||g.tag!==7?(g=Ya(b,v.mode,R,C),g.return=v,g):(g=n(g,b),g.return=v,g)}function N(v,g,b){if(typeof g=="string"&&g!==""||typeof g=="number"||typeof g=="bigint")return g=Rs(""+g,v.mode,b),g.return=v,g;if(typeof g=="object"&&g!==null){switch(g.$$typeof){case k:return b=Ru(g.type,g.key,g.props,null,v.mode,b),En(b,g),b.return=v,b;case H:return g=Os(g,v.mode,b),g.return=v,g;case Pt:var R=g._init;return g=R(g._payload),N(v,g,b)}if(Zt(g)||Lt(g))return g=Ya(g,v.mode,b,null),g.return=v,g;if(typeof g.then=="function")return N(v,Zu(g),b);if(g.$$typeof===ut)return N(v,Du(v,g),b);Vu(v,g)}return null}function p(v,g,b,R){var C=g!==null?g.key:null;if(typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint")return C!==null?null:d(v,g,""+b,R);if(typeof b=="object"&&b!==null){switch(b.$$typeof){case k:return b.key===C?h(v,g,b,R):null;case H:return b.key===C?S(v,g,b,R):null;case Pt:return C=b._init,b=C(b._payload),p(v,g,b,R)}if(Zt(b)||Lt(b))return C!==null?null:M(v,g,b,R,null);if(typeof b.then=="function")return p(v,g,Zu(b),R);if(b.$$typeof===ut)return p(v,g,Du(v,b),R);Vu(v,b)}return null}function _(v,g,b,R,C){if(typeof R=="string"&&R!==""||typeof R=="number"||typeof R=="bigint")return v=v.get(b)||null,d(g,v,""+R,C);if(typeof R=="object"&&R!==null){switch(R.$$typeof){case k:return v=v.get(R.key===null?b:R.key)||null,h(g,v,R,C);case H:return v=v.get(R.key===null?b:R.key)||null,S(g,v,R,C);case Pt:var I=R._init;return R=I(R._payload),_(v,g,b,R,C)}if(Zt(R)||Lt(R))return v=v.get(b)||null,M(g,v,R,C,null);if(typeof R.then=="function")return _(v,g,b,Zu(R),C);if(R.$$typeof===ut)return _(v,g,b,Du(g,R),C);Vu(g,R)}return null}function V(v,g,b,R){for(var C=null,I=null,Y=g,Q=g=0,Yt=null;Y!==null&&Q<b.length;Q++){Y.index>Q?(Yt=Y,Y=null):Yt=Y.sibling;var st=p(v,Y,b[Q],R);if(st===null){Y===null&&(Y=Yt);break}t&&Y&&st.alternate===null&&e(v,Y),g=u(st,g,Q),I===null?C=st:I.sibling=st,I=st,Y=Yt}if(Q===b.length)return a(v,Y),ct&&Ga(v,Q),C;if(Y===null){for(;Q<b.length;Q++)Y=N(v,b[Q],R),Y!==null&&(g=u(Y,g,Q),I===null?C=Y:I.sibling=Y,I=Y);return ct&&Ga(v,Q),C}for(Y=l(Y);Q<b.length;Q++)Yt=_(Y,v,Q,b[Q],R),Yt!==null&&(t&&Yt.alternate!==null&&Y.delete(Yt.key===null?Q:Yt.key),g=u(Yt,g,Q),I===null?C=Yt:I.sibling=Yt,I=Yt);return t&&Y.forEach(function(Na){return e(v,Na)}),ct&&Ga(v,Q),C}function G(v,g,b,R){if(b==null)throw Error(s(151));for(var C=null,I=null,Y=g,Q=g=0,Yt=null,st=b.next();Y!==null&&!st.done;Q++,st=b.next()){Y.index>Q?(Yt=Y,Y=null):Yt=Y.sibling;var Na=p(v,Y,st.value,R);if(Na===null){Y===null&&(Y=Yt);break}t&&Y&&Na.alternate===null&&e(v,Y),g=u(Na,g,Q),I===null?C=Na:I.sibling=Na,I=Na,Y=Yt}if(st.done)return a(v,Y),ct&&Ga(v,Q),C;if(Y===null){for(;!st.done;Q++,st=b.next())st=N(v,st.value,R),st!==null&&(g=u(st,g,Q),I===null?C=st:I.sibling=st,I=st);return ct&&Ga(v,Q),C}for(Y=l(Y);!st.done;Q++,st=b.next())st=_(Y,v,Q,st.value,R),st!==null&&(t&&st.alternate!==null&&Y.delete(st.key===null?Q:st.key),g=u(st,g,Q),I===null?C=st:I.sibling=st,I=st);return t&&Y.forEach(function(Py){return e(v,Py)}),ct&&Ga(v,Q),C}function ht(v,g,b,R){if(typeof b=="object"&&b!==null&&b.type===L&&b.key===null&&(b=b.props.children),typeof b=="object"&&b!==null){switch(b.$$typeof){case k:t:{for(var C=b.key;g!==null;){if(g.key===C){if(C=b.type,C===L){if(g.tag===7){a(v,g.sibling),R=n(g,b.props.children),R.return=v,v=R;break t}}else if(g.elementType===C||typeof C=="object"&&C!==null&&C.$$typeof===Pt&&qo(C)===g.type){a(v,g.sibling),R=n(g,b.props),En(R,b),R.return=v,v=R;break t}a(v,g);break}else e(v,g);g=g.sibling}b.type===L?(R=Ya(b.props.children,v.mode,R,b.key),R.return=v,v=R):(R=Ru(b.type,b.key,b.props,null,v.mode,R),En(R,b),R.return=v,v=R)}return r(v);case H:t:{for(C=b.key;g!==null;){if(g.key===C)if(g.tag===4&&g.stateNode.containerInfo===b.containerInfo&&g.stateNode.implementation===b.implementation){a(v,g.sibling),R=n(g,b.children||[]),R.return=v,v=R;break t}else{a(v,g);break}else e(v,g);g=g.sibling}R=Os(b,v.mode,R),R.return=v,v=R}return r(v);case Pt:return C=b._init,b=C(b._payload),ht(v,g,b,R)}if(Zt(b))return V(v,g,b,R);if(Lt(b)){if(C=Lt(b),typeof C!="function")throw Error(s(150));return b=C.call(b),G(v,g,b,R)}if(typeof b.then=="function")return ht(v,g,Zu(b),R);if(b.$$typeof===ut)return ht(v,g,Du(v,b),R);Vu(v,b)}return typeof b=="string"&&b!==""||typeof b=="number"||typeof b=="bigint"?(b=""+b,g!==null&&g.tag===6?(a(v,g.sibling),R=n(g,b),R.return=v,v=R):(a(v,g),R=Rs(b,v.mode,R),R.return=v,v=R),r(v)):a(v,g)}return function(v,g,b,R){try{xn=0;var C=ht(v,g,b,R);return Al=null,C}catch(Y){if(Y===hn||Y===Uu)throw Y;var I=fe(29,Y,null,v.mode);return I.lanes=R,I.return=v,I}finally{}}}var Rl=Ho(!0),Bo=Ho(!1),Ee=D(null),qe=null;function ma(t){var e=t.alternate;j(qt,qt.current&1),j(Ee,t),qe===null&&(e===null||xl.current!==null||e.memoizedState!==null)&&(qe=t)}function Co(t){if(t.tag===22){if(j(qt,qt.current),j(Ee,t),qe===null){var e=t.alternate;e!==null&&e.memoizedState!==null&&(qe=t)}}else ya()}function ya(){j(qt,qt.current),j(Ee,Ee.current)}function $e(t){B(Ee),qe===t&&(qe=null),B(qt)}var qt=D(0);function Ku(t){for(var e=t;e!==null;){if(e.tag===13){var a=e.memoizedState;if(a!==null&&(a=a.dehydrated,a===null||a.data==="$?"||Wc(a)))return e}else if(e.tag===19&&e.memoizedProps.revealOrder!==void 0){if((e.flags&128)!==0)return e}else if(e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return null;e=e.return}e.sibling.return=e.return,e=e.sibling}return null}function sc(t,e,a,l){e=t.memoizedState,a=a(l,e),a=a==null?e:U({},e,a),t.memoizedState=a,t.lanes===0&&(t.updateQueue.baseState=a)}var cc={enqueueSetState:function(t,e,a){t=t._reactInternals;var l=he(),n=oa(l);n.payload=e,a!=null&&(n.callback=a),e=da(t,n,l),e!==null&&(me(e,t,l),yn(e,t,l))},enqueueReplaceState:function(t,e,a){t=t._reactInternals;var l=he(),n=oa(l);n.tag=1,n.payload=e,a!=null&&(n.callback=a),e=da(t,n,l),e!==null&&(me(e,t,l),yn(e,t,l))},enqueueForceUpdate:function(t,e){t=t._reactInternals;var a=he(),l=oa(a);l.tag=2,e!=null&&(l.callback=e),e=da(t,l,a),e!==null&&(me(e,t,a),yn(e,t,a))}};function Yo(t,e,a,l,n,u,r){return t=t.stateNode,typeof t.shouldComponentUpdate=="function"?t.shouldComponentUpdate(l,u,r):e.prototype&&e.prototype.isPureReactComponent?!nn(a,l)||!nn(n,u):!0}function Xo(t,e,a,l){t=e.state,typeof e.componentWillReceiveProps=="function"&&e.componentWillReceiveProps(a,l),typeof e.UNSAFE_componentWillReceiveProps=="function"&&e.UNSAFE_componentWillReceiveProps(a,l),e.state!==t&&cc.enqueueReplaceState(e,e.state,null)}function Ja(t,e){var a=e;if("ref"in e){a={};for(var l in e)l!=="ref"&&(a[l]=e[l])}if(t=t.defaultProps){a===e&&(a=U({},a));for(var n in t)a[n]===void 0&&(a[n]=t[n])}return a}var ku=typeof reportError=="function"?reportError:function(t){if(typeof window=="object"&&typeof window.ErrorEvent=="function"){var e=new window.ErrorEvent("error",{bubbles:!0,cancelable:!0,message:typeof t=="object"&&t!==null&&typeof t.message=="string"?String(t.message):String(t),error:t});if(!window.dispatchEvent(e))return}else if(typeof process=="object"&&typeof process.emit=="function"){process.emit("uncaughtException",t);return}console.error(t)};function Go(t){ku(t)}function Qo(t){console.error(t)}function Lo(t){ku(t)}function Ju(t,e){try{var a=t.onUncaughtError;a(e.value,{componentStack:e.stack})}catch(l){setTimeout(function(){throw l})}}function Zo(t,e,a){try{var l=t.onCaughtError;l(a.value,{componentStack:a.stack,errorBoundary:e.tag===1?e.stateNode:null})}catch(n){setTimeout(function(){throw n})}}function fc(t,e,a){return a=oa(a),a.tag=3,a.payload={element:null},a.callback=function(){Ju(t,e)},a}function Vo(t){return t=oa(t),t.tag=3,t}function Ko(t,e,a,l){var n=a.type.getDerivedStateFromError;if(typeof n=="function"){var u=l.value;t.payload=function(){return n(u)},t.callback=function(){Zo(e,a,l)}}var r=a.stateNode;r!==null&&typeof r.componentDidCatch=="function"&&(t.callback=function(){Zo(e,a,l),typeof n!="function"&&(_a===null?_a=new Set([this]):_a.add(this));var d=l.stack;this.componentDidCatch(l.value,{componentStack:d!==null?d:""})})}function P0(t,e,a,l,n){if(a.flags|=32768,l!==null&&typeof l=="object"&&typeof l.then=="function"){if(e=a.alternate,e!==null&&rn(e,a,n,!0),a=Ee.current,a!==null){switch(a.tag){case 13:return qe===null?Uc():a.alternate===null&&At===0&&(At=3),a.flags&=-257,a.flags|=65536,a.lanes=n,l===Cs?a.flags|=16384:(e=a.updateQueue,e===null?a.updateQueue=new Set([l]):e.add(l),qc(t,l,n)),!1;case 22:return a.flags|=65536,l===Cs?a.flags|=16384:(e=a.updateQueue,e===null?(e={transitions:null,markerInstances:null,retryQueue:new Set([l])},a.updateQueue=e):(a=e.retryQueue,a===null?e.retryQueue=new Set([l]):a.add(l)),qc(t,l,n)),!1}throw Error(s(435,a.tag))}return qc(t,l,n),Uc(),!1}if(ct)return e=Ee.current,e!==null?((e.flags&65536)===0&&(e.flags|=256),e.flags|=65536,e.lanes=n,l!==Ds&&(t=Error(s(422),{cause:l}),fn(Se(t,a)))):(l!==Ds&&(e=Error(s(423),{cause:l}),fn(Se(e,a))),t=t.current.alternate,t.flags|=65536,n&=-n,t.lanes|=n,l=Se(l,a),n=fc(t.stateNode,l,n),Gs(t,n),At!==4&&(At=2)),!1;var u=Error(s(520),{cause:l});if(u=Se(u,a),wn===null?wn=[u]:wn.push(u),At!==4&&(At=2),e===null)return!0;l=Se(l,a),a=e;do{switch(a.tag){case 3:return a.flags|=65536,t=n&-n,a.lanes|=t,t=fc(a.stateNode,l,t),Gs(a,t),!1;case 1:if(e=a.type,u=a.stateNode,(a.flags&128)===0&&(typeof e.getDerivedStateFromError=="function"||u!==null&&typeof u.componentDidCatch=="function"&&(_a===null||!_a.has(u))))return a.flags|=65536,n&=-n,a.lanes|=n,n=Vo(n),Ko(n,t,a,l),Gs(a,n),!1}a=a.return}while(a!==null);return!1}var ko=Error(s(461)),Bt=!1;function Xt(t,e,a,l){e.child=t===null?Bo(e,null,a,l):Rl(e,t.child,a,l)}function Jo(t,e,a,l,n){a=a.render;var u=e.ref;if("ref"in l){var r={};for(var d in l)d!=="ref"&&(r[d]=l[d])}else r=l;return Va(e),l=Ks(t,e,a,r,u,n),d=ks(),t!==null&&!Bt?(Js(t,e,n),We(t,e,n)):(ct&&d&&Ns(e),e.flags|=1,Xt(t,e,l,n),e.child)}function $o(t,e,a,l,n){if(t===null){var u=a.type;return typeof u=="function"&&!As(u)&&u.defaultProps===void 0&&a.compare===null?(e.tag=15,e.type=u,Wo(t,e,u,l,n)):(t=Ru(a.type,null,l,e,e.mode,n),t.ref=e.ref,t.return=e,e.child=t)}if(u=t.child,!vc(t,n)){var r=u.memoizedProps;if(a=a.compare,a=a!==null?a:nn,a(r,l)&&t.ref===e.ref)return We(t,e,n)}return e.flags|=1,t=Le(u,l),t.ref=e.ref,t.return=e,e.child=t}function Wo(t,e,a,l,n){if(t!==null){var u=t.memoizedProps;if(nn(u,l)&&t.ref===e.ref)if(Bt=!1,e.pendingProps=l=u,vc(t,n))(t.flags&131072)!==0&&(Bt=!0);else return e.lanes=t.lanes,We(t,e,n)}return rc(t,e,a,l,n)}function Fo(t,e,a){var l=e.pendingProps,n=l.children,u=t!==null?t.memoizedState:null;if(l.mode==="hidden"){if((e.flags&128)!==0){if(l=u!==null?u.baseLanes|a:a,t!==null){for(n=e.child=t.child,u=0;n!==null;)u=u|n.lanes|n.childLanes,n=n.sibling;e.childLanes=u&~l}else e.childLanes=0,e.child=null;return Po(t,e,l,a)}if((a&536870912)!==0)e.memoizedState={baseLanes:0,cachePool:null},t!==null&&zu(e,u!==null?u.cachePool:null),u!==null?$r(e,u):Ls(),Co(e);else return e.lanes=e.childLanes=536870912,Po(t,e,u!==null?u.baseLanes|a:a,a)}else u!==null?(zu(e,u.cachePool),$r(e,u),ya(),e.memoizedState=null):(t!==null&&zu(e,null),Ls(),ya());return Xt(t,e,n,a),e.child}function Po(t,e,a,l){var n=Bs();return n=n===null?null:{parent:jt._currentValue,pool:n},e.memoizedState={baseLanes:a,cachePool:n},t!==null&&zu(e,null),Ls(),Co(e),t!==null&&rn(t,e,l,!0),null}function $u(t,e){var a=e.ref;if(a===null)t!==null&&t.ref!==null&&(e.flags|=4194816);else{if(typeof a!="function"&&typeof a!="object")throw Error(s(284));(t===null||t.ref!==a)&&(e.flags|=4194816)}}function rc(t,e,a,l,n){return Va(e),a=Ks(t,e,a,l,void 0,n),l=ks(),t!==null&&!Bt?(Js(t,e,n),We(t,e,n)):(ct&&l&&Ns(e),e.flags|=1,Xt(t,e,a,n),e.child)}function Io(t,e,a,l,n,u){return Va(e),e.updateQueue=null,a=Fr(e,l,a,n),Wr(t),l=ks(),t!==null&&!Bt?(Js(t,e,u),We(t,e,u)):(ct&&l&&Ns(e),e.flags|=1,Xt(t,e,a,u),e.child)}function td(t,e,a,l,n){if(Va(e),e.stateNode===null){var u=vl,r=a.contextType;typeof r=="object"&&r!==null&&(u=Kt(r)),u=new a(l,u),e.memoizedState=u.state!==null&&u.state!==void 0?u.state:null,u.updater=cc,e.stateNode=u,u._reactInternals=e,u=e.stateNode,u.props=l,u.state=e.memoizedState,u.refs={},Ys(e),r=a.contextType,u.context=typeof r=="object"&&r!==null?Kt(r):vl,u.state=e.memoizedState,r=a.getDerivedStateFromProps,typeof r=="function"&&(sc(e,a,r,l),u.state=e.memoizedState),typeof a.getDerivedStateFromProps=="function"||typeof u.getSnapshotBeforeUpdate=="function"||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(r=u.state,typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount(),r!==u.state&&cc.enqueueReplaceState(u,u.state,null),vn(e,l,u,n),gn(),u.state=e.memoizedState),typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!0}else if(t===null){u=e.stateNode;var d=e.memoizedProps,h=Ja(a,d);u.props=h;var S=u.context,M=a.contextType;r=vl,typeof M=="object"&&M!==null&&(r=Kt(M));var N=a.getDerivedStateFromProps;M=typeof N=="function"||typeof u.getSnapshotBeforeUpdate=="function",d=e.pendingProps!==d,M||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(d||S!==r)&&Xo(e,u,l,r),ra=!1;var p=e.memoizedState;u.state=p,vn(e,l,u,n),gn(),S=e.memoizedState,d||p!==S||ra?(typeof N=="function"&&(sc(e,a,N,l),S=e.memoizedState),(h=ra||Yo(e,a,h,l,p,S,r))?(M||typeof u.UNSAFE_componentWillMount!="function"&&typeof u.componentWillMount!="function"||(typeof u.componentWillMount=="function"&&u.componentWillMount(),typeof u.UNSAFE_componentWillMount=="function"&&u.UNSAFE_componentWillMount()),typeof u.componentDidMount=="function"&&(e.flags|=4194308)):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),e.memoizedProps=l,e.memoizedState=S),u.props=l,u.state=S,u.context=r,l=h):(typeof u.componentDidMount=="function"&&(e.flags|=4194308),l=!1)}else{u=e.stateNode,Xs(t,e),r=e.memoizedProps,M=Ja(a,r),u.props=M,N=e.pendingProps,p=u.context,S=a.contextType,h=vl,typeof S=="object"&&S!==null&&(h=Kt(S)),d=a.getDerivedStateFromProps,(S=typeof d=="function"||typeof u.getSnapshotBeforeUpdate=="function")||typeof u.UNSAFE_componentWillReceiveProps!="function"&&typeof u.componentWillReceiveProps!="function"||(r!==N||p!==h)&&Xo(e,u,l,h),ra=!1,p=e.memoizedState,u.state=p,vn(e,l,u,n),gn();var _=e.memoizedState;r!==N||p!==_||ra||t!==null&&t.dependencies!==null&&wu(t.dependencies)?(typeof d=="function"&&(sc(e,a,d,l),_=e.memoizedState),(M=ra||Yo(e,a,M,l,p,_,h)||t!==null&&t.dependencies!==null&&wu(t.dependencies))?(S||typeof u.UNSAFE_componentWillUpdate!="function"&&typeof u.componentWillUpdate!="function"||(typeof u.componentWillUpdate=="function"&&u.componentWillUpdate(l,_,h),typeof u.UNSAFE_componentWillUpdate=="function"&&u.UNSAFE_componentWillUpdate(l,_,h)),typeof u.componentDidUpdate=="function"&&(e.flags|=4),typeof u.getSnapshotBeforeUpdate=="function"&&(e.flags|=1024)):(typeof u.componentDidUpdate!="function"||r===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),e.memoizedProps=l,e.memoizedState=_),u.props=l,u.state=_,u.context=h,l=M):(typeof u.componentDidUpdate!="function"||r===t.memoizedProps&&p===t.memoizedState||(e.flags|=4),typeof u.getSnapshotBeforeUpdate!="function"||r===t.memoizedProps&&p===t.memoizedState||(e.flags|=1024),l=!1)}return u=l,$u(t,e),l=(e.flags&128)!==0,u||l?(u=e.stateNode,a=l&&typeof a.getDerivedStateFromError!="function"?null:u.render(),e.flags|=1,t!==null&&l?(e.child=Rl(e,t.child,null,n),e.child=Rl(e,null,a,n)):Xt(t,e,a,n),e.memoizedState=u.state,t=e.child):t=We(t,e,n),t}function ed(t,e,a,l){return cn(),e.flags|=256,Xt(t,e,a,l),e.child}var oc={dehydrated:null,treeContext:null,retryLane:0,hydrationErrors:null};function dc(t){return{baseLanes:t,cachePool:Gr()}}function hc(t,e,a){return t=t!==null?t.childLanes&~a:0,e&&(t|=Te),t}function ad(t,e,a){var l=e.pendingProps,n=!1,u=(e.flags&128)!==0,r;if((r=u)||(r=t!==null&&t.memoizedState===null?!1:(qt.current&2)!==0),r&&(n=!0,e.flags&=-129),r=(e.flags&32)!==0,e.flags&=-33,t===null){if(ct){if(n?ma(e):ya(),ct){var d=Mt,h;if(h=d){t:{for(h=d,d=je;h.nodeType!==8;){if(!d){d=null;break t}if(h=we(h.nextSibling),h===null){d=null;break t}}d=h}d!==null?(e.memoizedState={dehydrated:d,treeContext:Xa!==null?{id:Ze,overflow:Ve}:null,retryLane:536870912,hydrationErrors:null},h=fe(18,null,null,0),h.stateNode=d,h.return=e,e.child=h,Ft=e,Mt=null,h=!0):h=!1}h||La(e)}if(d=e.memoizedState,d!==null&&(d=d.dehydrated,d!==null))return Wc(d)?e.lanes=32:e.lanes=536870912,null;$e(e)}return d=l.children,l=l.fallback,n?(ya(),n=e.mode,d=Wu({mode:"hidden",children:d},n),l=Ya(l,n,a,null),d.return=e,l.return=e,d.sibling=l,e.child=d,n=e.child,n.memoizedState=dc(a),n.childLanes=hc(t,r,a),e.memoizedState=oc,l):(ma(e),mc(e,d))}if(h=t.memoizedState,h!==null&&(d=h.dehydrated,d!==null)){if(u)e.flags&256?(ma(e),e.flags&=-257,e=yc(t,e,a)):e.memoizedState!==null?(ya(),e.child=t.child,e.flags|=128,e=null):(ya(),n=l.fallback,d=e.mode,l=Wu({mode:"visible",children:l.children},d),n=Ya(n,d,a,null),n.flags|=2,l.return=e,n.return=e,l.sibling=n,e.child=l,Rl(e,t.child,null,a),l=e.child,l.memoizedState=dc(a),l.childLanes=hc(t,r,a),e.memoizedState=oc,e=n);else if(ma(e),Wc(d)){if(r=d.nextSibling&&d.nextSibling.dataset,r)var S=r.dgst;r=S,l=Error(s(419)),l.stack="",l.digest=r,fn({value:l,source:null,stack:null}),e=yc(t,e,a)}else if(Bt||rn(t,e,a,!1),r=(a&t.childLanes)!==0,Bt||r){if(r=vt,r!==null&&(l=a&-a,l=(l&42)!==0?1:Fi(l),l=(l&(r.suspendedLanes|a))!==0?0:l,l!==0&&l!==h.retryLane))throw h.retryLane=l,gl(t,l),me(r,t,l),ko;d.data==="$?"||Uc(),e=yc(t,e,a)}else d.data==="$?"?(e.flags|=192,e.child=t.child,e=null):(t=h.treeContext,Mt=we(d.nextSibling),Ft=e,ct=!0,Qa=null,je=!1,t!==null&&(_e[xe++]=Ze,_e[xe++]=Ve,_e[xe++]=Xa,Ze=t.id,Ve=t.overflow,Xa=e),e=mc(e,l.children),e.flags|=4096);return e}return n?(ya(),n=l.fallback,d=e.mode,h=t.child,S=h.sibling,l=Le(h,{mode:"hidden",children:l.children}),l.subtreeFlags=h.subtreeFlags&65011712,S!==null?n=Le(S,n):(n=Ya(n,d,a,null),n.flags|=2),n.return=e,l.return=e,l.sibling=n,e.child=l,l=n,n=e.child,d=t.child.memoizedState,d===null?d=dc(a):(h=d.cachePool,h!==null?(S=jt._currentValue,h=h.parent!==S?{parent:S,pool:S}:h):h=Gr(),d={baseLanes:d.baseLanes|a,cachePool:h}),n.memoizedState=d,n.childLanes=hc(t,r,a),e.memoizedState=oc,l):(ma(e),a=t.child,t=a.sibling,a=Le(a,{mode:"visible",children:l.children}),a.return=e,a.sibling=null,t!==null&&(r=e.deletions,r===null?(e.deletions=[t],e.flags|=16):r.push(t)),e.child=a,e.memoizedState=null,a)}function mc(t,e){return e=Wu({mode:"visible",children:e},t.mode),e.return=t,t.child=e}function Wu(t,e){return t=fe(22,t,null,e),t.lanes=0,t.stateNode={_visibility:1,_pendingMarkers:null,_retryCache:null,_transitions:null},t}function yc(t,e,a){return Rl(e,t.child,null,a),t=mc(e,e.pendingProps.children),t.flags|=2,e.memoizedState=null,t}function ld(t,e,a){t.lanes|=e;var l=t.alternate;l!==null&&(l.lanes|=e),Us(t.return,e,a)}function gc(t,e,a,l,n){var u=t.memoizedState;u===null?t.memoizedState={isBackwards:e,rendering:null,renderingStartTime:0,last:l,tail:a,tailMode:n}:(u.isBackwards=e,u.rendering=null,u.renderingStartTime=0,u.last=l,u.tail=a,u.tailMode=n)}function nd(t,e,a){var l=e.pendingProps,n=l.revealOrder,u=l.tail;if(Xt(t,e,l.children,a),l=qt.current,(l&2)!==0)l=l&1|2,e.flags|=128;else{if(t!==null&&(t.flags&128)!==0)t:for(t=e.child;t!==null;){if(t.tag===13)t.memoizedState!==null&&ld(t,a,e);else if(t.tag===19)ld(t,a,e);else if(t.child!==null){t.child.return=t,t=t.child;continue}if(t===e)break t;for(;t.sibling===null;){if(t.return===null||t.return===e)break t;t=t.return}t.sibling.return=t.return,t=t.sibling}l&=1}switch(j(qt,l),n){case"forwards":for(a=e.child,n=null;a!==null;)t=a.alternate,t!==null&&Ku(t)===null&&(n=a),a=a.sibling;a=n,a===null?(n=e.child,e.child=null):(n=a.sibling,a.sibling=null),gc(e,!1,n,a,u);break;case"backwards":for(a=null,n=e.child,e.child=null;n!==null;){if(t=n.alternate,t!==null&&Ku(t)===null){e.child=n;break}t=n.sibling,n.sibling=a,a=n,n=t}gc(e,!0,a,null,u);break;case"together":gc(e,!1,null,null,void 0);break;default:e.memoizedState=null}return e.child}function We(t,e,a){if(t!==null&&(e.dependencies=t.dependencies),pa|=e.lanes,(a&e.childLanes)===0)if(t!==null){if(rn(t,e,a,!1),(a&e.childLanes)===0)return null}else return null;if(t!==null&&e.child!==t.child)throw Error(s(153));if(e.child!==null){for(t=e.child,a=Le(t,t.pendingProps),e.child=a,a.return=e;t.sibling!==null;)t=t.sibling,a=a.sibling=Le(t,t.pendingProps),a.return=e;a.sibling=null}return e.child}function vc(t,e){return(t.lanes&e)!==0?!0:(t=t.dependencies,!!(t!==null&&wu(t)))}function I0(t,e,a){switch(e.tag){case 3:_t(e,e.stateNode.containerInfo),fa(e,jt,t.memoizedState.cache),cn();break;case 27:case 5:Ki(e);break;case 4:_t(e,e.stateNode.containerInfo);break;case 10:fa(e,e.type,e.memoizedProps.value);break;case 13:var l=e.memoizedState;if(l!==null)return l.dehydrated!==null?(ma(e),e.flags|=128,null):(a&e.child.childLanes)!==0?ad(t,e,a):(ma(e),t=We(t,e,a),t!==null?t.sibling:null);ma(e);break;case 19:var n=(t.flags&128)!==0;if(l=(a&e.childLanes)!==0,l||(rn(t,e,a,!1),l=(a&e.childLanes)!==0),n){if(l)return nd(t,e,a);e.flags|=128}if(n=e.memoizedState,n!==null&&(n.rendering=null,n.tail=null,n.lastEffect=null),j(qt,qt.current),l)break;return null;case 22:case 23:return e.lanes=0,Fo(t,e,a);case 24:fa(e,jt,t.memoizedState.cache)}return We(t,e,a)}function ud(t,e,a){if(t!==null)if(t.memoizedProps!==e.pendingProps)Bt=!0;else{if(!vc(t,a)&&(e.flags&128)===0)return Bt=!1,I0(t,e,a);Bt=(t.flags&131072)!==0}else Bt=!1,ct&&(e.flags&1048576)!==0&&jr(e,Nu,e.index);switch(e.lanes=0,e.tag){case 16:t:{t=e.pendingProps;var l=e.elementType,n=l._init;if(l=n(l._payload),e.type=l,typeof l=="function")As(l)?(t=Ja(l,t),e.tag=1,e=td(null,e,l,t,a)):(e.tag=0,e=rc(null,e,l,t,a));else{if(l!=null){if(n=l.$$typeof,n===pt){e.tag=11,e=Jo(null,e,l,t,a);break t}else if(n===Ut){e.tag=14,e=$o(null,e,l,t,a);break t}}throw e=Ua(l)||l,Error(s(306,e,""))}}return e;case 0:return rc(t,e,e.type,e.pendingProps,a);case 1:return l=e.type,n=Ja(l,e.pendingProps),td(t,e,l,n,a);case 3:t:{if(_t(e,e.stateNode.containerInfo),t===null)throw Error(s(387));l=e.pendingProps;var u=e.memoizedState;n=u.element,Xs(t,e),vn(e,l,null,a);var r=e.memoizedState;if(l=r.cache,fa(e,jt,l),l!==u.cache&&js(e,[jt],a,!0),gn(),l=r.element,u.isDehydrated)if(u={element:l,isDehydrated:!1,cache:r.cache},e.updateQueue.baseState=u,e.memoizedState=u,e.flags&256){e=ed(t,e,l,a);break t}else if(l!==n){n=Se(Error(s(424)),e),fn(n),e=ed(t,e,l,a);break t}else{switch(t=e.stateNode.containerInfo,t.nodeType){case 9:t=t.body;break;default:t=t.nodeName==="HTML"?t.ownerDocument.body:t}for(Mt=we(t.firstChild),Ft=e,ct=!0,Qa=null,je=!0,a=Bo(e,null,l,a),e.child=a;a;)a.flags=a.flags&-3|4096,a=a.sibling}else{if(cn(),l===n){e=We(t,e,a);break t}Xt(t,e,l,a)}e=e.child}return e;case 26:return $u(t,e),t===null?(a=fh(e.type,null,e.pendingProps,null))?e.memoizedState=a:ct||(a=e.type,t=e.pendingProps,l=ri(J.current).createElement(a),l[Vt]=e,l[It]=t,Qt(l,a,t),Ht(l),e.stateNode=l):e.memoizedState=fh(e.type,t.memoizedProps,e.pendingProps,t.memoizedState),null;case 27:return Ki(e),t===null&&ct&&(l=e.stateNode=ih(e.type,e.pendingProps,J.current),Ft=e,je=!0,n=Mt,Ta(e.type)?(Fc=n,Mt=we(l.firstChild)):Mt=n),Xt(t,e,e.pendingProps.children,a),$u(t,e),t===null&&(e.flags|=4194304),e.child;case 5:return t===null&&ct&&((n=l=Mt)&&(l=Ry(l,e.type,e.pendingProps,je),l!==null?(e.stateNode=l,Ft=e,Mt=we(l.firstChild),je=!1,n=!0):n=!1),n||La(e)),Ki(e),n=e.type,u=e.pendingProps,r=t!==null?t.memoizedProps:null,l=u.children,kc(n,u)?l=null:r!==null&&kc(n,r)&&(e.flags|=32),e.memoizedState!==null&&(n=Ks(t,e,V0,null,null,a),Yn._currentValue=n),$u(t,e),Xt(t,e,l,a),e.child;case 6:return t===null&&ct&&((t=a=Mt)&&(a=Oy(a,e.pendingProps,je),a!==null?(e.stateNode=a,Ft=e,Mt=null,t=!0):t=!1),t||La(e)),null;case 13:return ad(t,e,a);case 4:return _t(e,e.stateNode.containerInfo),l=e.pendingProps,t===null?e.child=Rl(e,null,l,a):Xt(t,e,l,a),e.child;case 11:return Jo(t,e,e.type,e.pendingProps,a);case 7:return Xt(t,e,e.pendingProps,a),e.child;case 8:return Xt(t,e,e.pendingProps.children,a),e.child;case 12:return Xt(t,e,e.pendingProps.children,a),e.child;case 10:return l=e.pendingProps,fa(e,e.type,l.value),Xt(t,e,l.children,a),e.child;case 9:return n=e.type._context,l=e.pendingProps.children,Va(e),n=Kt(n),l=l(n),e.flags|=1,Xt(t,e,l,a),e.child;case 14:return $o(t,e,e.type,e.pendingProps,a);case 15:return Wo(t,e,e.type,e.pendingProps,a);case 19:return nd(t,e,a);case 31:return l=e.pendingProps,a=e.mode,l={mode:l.mode,children:l.children},t===null?(a=Wu(l,a),a.ref=e.ref,e.child=a,a.return=e,e=a):(a=Le(t.child,l),a.ref=e.ref,e.child=a,a.return=e,e=a),e;case 22:return Fo(t,e,a);case 24:return Va(e),l=Kt(jt),t===null?(n=Bs(),n===null&&(n=vt,u=qs(),n.pooledCache=u,u.refCount++,u!==null&&(n.pooledCacheLanes|=a),n=u),e.memoizedState={parent:l,cache:n},Ys(e),fa(e,jt,n)):((t.lanes&a)!==0&&(Xs(t,e),vn(e,null,null,a),gn()),n=t.memoizedState,u=e.memoizedState,n.parent!==l?(n={parent:l,cache:l},e.memoizedState=n,e.lanes===0&&(e.memoizedState=e.updateQueue.baseState=n),fa(e,jt,l)):(l=u.cache,fa(e,jt,l),l!==n.cache&&js(e,[jt],a,!0))),Xt(t,e,e.pendingProps.children,a),e.child;case 29:throw e.pendingProps}throw Error(s(156,e.tag))}function Fe(t){t.flags|=4}function id(t,e){if(e.type!=="stylesheet"||(e.state.loading&4)!==0)t.flags&=-16777217;else if(t.flags|=16777216,!mh(e)){if(e=Ee.current,e!==null&&((nt&4194048)===nt?qe!==null:(nt&62914560)!==nt&&(nt&536870912)===0||e!==qe))throw mn=Cs,Qr;t.flags|=8192}}function Fu(t,e){e!==null&&(t.flags|=4),t.flags&16384&&(e=t.tag!==22?Bf():536870912,t.lanes|=e,Dl|=e)}function Tn(t,e){if(!ct)switch(t.tailMode){case"hidden":e=t.tail;for(var a=null;e!==null;)e.alternate!==null&&(a=e),e=e.sibling;a===null?t.tail=null:a.sibling=null;break;case"collapsed":a=t.tail;for(var l=null;a!==null;)a.alternate!==null&&(l=a),a=a.sibling;l===null?e||t.tail===null?t.tail=null:t.tail.sibling=null:l.sibling=null}}function Tt(t){var e=t.alternate!==null&&t.alternate.child===t.child,a=0,l=0;if(e)for(var n=t.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags&65011712,l|=n.flags&65011712,n.return=t,n=n.sibling;else for(n=t.child;n!==null;)a|=n.lanes|n.childLanes,l|=n.subtreeFlags,l|=n.flags,n.return=t,n=n.sibling;return t.subtreeFlags|=l,t.childLanes=a,e}function ty(t,e,a){var l=e.pendingProps;switch(ws(e),e.tag){case 31:case 16:case 15:case 0:case 11:case 7:case 8:case 12:case 9:case 14:return Tt(e),null;case 1:return Tt(e),null;case 3:return a=e.stateNode,l=null,t!==null&&(l=t.memoizedState.cache),e.memoizedState.cache!==l&&(e.flags|=2048),ke(jt),ua(),a.pendingContext&&(a.context=a.pendingContext,a.pendingContext=null),(t===null||t.child===null)&&(sn(e)?Fe(e):t===null||t.memoizedState.isDehydrated&&(e.flags&256)===0||(e.flags|=1024,Br())),Tt(e),null;case 26:return a=e.memoizedState,t===null?(Fe(e),a!==null?(Tt(e),id(e,a)):(Tt(e),e.flags&=-16777217)):a?a!==t.memoizedState?(Fe(e),Tt(e),id(e,a)):(Tt(e),e.flags&=-16777217):(t.memoizedProps!==l&&Fe(e),Tt(e),e.flags&=-16777217),null;case 27:cu(e),a=J.current;var n=e.type;if(t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Fe(e);else{if(!l){if(e.stateNode===null)throw Error(s(166));return Tt(e),null}t=X.current,sn(e)?qr(e):(t=ih(n,l,a),e.stateNode=t,Fe(e))}return Tt(e),null;case 5:if(cu(e),a=e.type,t!==null&&e.stateNode!=null)t.memoizedProps!==l&&Fe(e);else{if(!l){if(e.stateNode===null)throw Error(s(166));return Tt(e),null}if(t=X.current,sn(e))qr(e);else{switch(n=ri(J.current),t){case 1:t=n.createElementNS("http://www.w3.org/2000/svg",a);break;case 2:t=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;default:switch(a){case"svg":t=n.createElementNS("http://www.w3.org/2000/svg",a);break;case"math":t=n.createElementNS("http://www.w3.org/1998/Math/MathML",a);break;case"script":t=n.createElement("div"),t.innerHTML="<script><\/script>",t=t.removeChild(t.firstChild);break;case"select":t=typeof l.is=="string"?n.createElement("select",{is:l.is}):n.createElement("select"),l.multiple?t.multiple=!0:l.size&&(t.size=l.size);break;default:t=typeof l.is=="string"?n.createElement(a,{is:l.is}):n.createElement(a)}}t[Vt]=e,t[It]=l;t:for(n=e.child;n!==null;){if(n.tag===5||n.tag===6)t.appendChild(n.stateNode);else if(n.tag!==4&&n.tag!==27&&n.child!==null){n.child.return=n,n=n.child;continue}if(n===e)break t;for(;n.sibling===null;){if(n.return===null||n.return===e)break t;n=n.return}n.sibling.return=n.return,n=n.sibling}e.stateNode=t;t:switch(Qt(t,a,l),a){case"button":case"input":case"select":case"textarea":t=!!l.autoFocus;break t;case"img":t=!0;break t;default:t=!1}t&&Fe(e)}}return Tt(e),e.flags&=-16777217,null;case 6:if(t&&e.stateNode!=null)t.memoizedProps!==l&&Fe(e);else{if(typeof l!="string"&&e.stateNode===null)throw Error(s(166));if(t=J.current,sn(e)){if(t=e.stateNode,a=e.memoizedProps,l=null,n=Ft,n!==null)switch(n.tag){case 27:case 5:l=n.memoizedProps}t[Vt]=e,t=!!(t.nodeValue===a||l!==null&&l.suppressHydrationWarning===!0||Id(t.nodeValue,a)),t||La(e)}else t=ri(t).createTextNode(l),t[Vt]=e,e.stateNode=t}return Tt(e),null;case 13:if(l=e.memoizedState,t===null||t.memoizedState!==null&&t.memoizedState.dehydrated!==null){if(n=sn(e),l!==null&&l.dehydrated!==null){if(t===null){if(!n)throw Error(s(318));if(n=e.memoizedState,n=n!==null?n.dehydrated:null,!n)throw Error(s(317));n[Vt]=e}else cn(),(e.flags&128)===0&&(e.memoizedState=null),e.flags|=4;Tt(e),n=!1}else n=Br(),t!==null&&t.memoizedState!==null&&(t.memoizedState.hydrationErrors=n),n=!0;if(!n)return e.flags&256?($e(e),e):($e(e),null)}if($e(e),(e.flags&128)!==0)return e.lanes=a,e;if(a=l!==null,t=t!==null&&t.memoizedState!==null,a){l=e.child,n=null,l.alternate!==null&&l.alternate.memoizedState!==null&&l.alternate.memoizedState.cachePool!==null&&(n=l.alternate.memoizedState.cachePool.pool);var u=null;l.memoizedState!==null&&l.memoizedState.cachePool!==null&&(u=l.memoizedState.cachePool.pool),u!==n&&(l.flags|=2048)}return a!==t&&a&&(e.child.flags|=8192),Fu(e,e.updateQueue),Tt(e),null;case 4:return ua(),t===null&&Qc(e.stateNode.containerInfo),Tt(e),null;case 10:return ke(e.type),Tt(e),null;case 19:if(B(qt),n=e.memoizedState,n===null)return Tt(e),null;if(l=(e.flags&128)!==0,u=n.rendering,u===null)if(l)Tn(n,!1);else{if(At!==0||t!==null&&(t.flags&128)!==0)for(t=e.child;t!==null;){if(u=Ku(t),u!==null){for(e.flags|=128,Tn(n,!1),t=u.updateQueue,e.updateQueue=t,Fu(e,t),e.subtreeFlags=0,t=a,a=e.child;a!==null;)Ur(a,t),a=a.sibling;return j(qt,qt.current&1|2),e.child}t=t.sibling}n.tail!==null&&Ue()>ti&&(e.flags|=128,l=!0,Tn(n,!1),e.lanes=4194304)}else{if(!l)if(t=Ku(u),t!==null){if(e.flags|=128,l=!0,t=t.updateQueue,e.updateQueue=t,Fu(e,t),Tn(n,!0),n.tail===null&&n.tailMode==="hidden"&&!u.alternate&&!ct)return Tt(e),null}else 2*Ue()-n.renderingStartTime>ti&&a!==536870912&&(e.flags|=128,l=!0,Tn(n,!1),e.lanes=4194304);n.isBackwards?(u.sibling=e.child,e.child=u):(t=n.last,t!==null?t.sibling=u:e.child=u,n.last=u)}return n.tail!==null?(e=n.tail,n.rendering=e,n.tail=e.sibling,n.renderingStartTime=Ue(),e.sibling=null,t=qt.current,j(qt,l?t&1|2:t&1),e):(Tt(e),null);case 22:case 23:return $e(e),Zs(),l=e.memoizedState!==null,t!==null?t.memoizedState!==null!==l&&(e.flags|=8192):l&&(e.flags|=8192),l?(a&536870912)!==0&&(e.flags&128)===0&&(Tt(e),e.subtreeFlags&6&&(e.flags|=8192)):Tt(e),a=e.updateQueue,a!==null&&Fu(e,a.retryQueue),a=null,t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),l=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(l=e.memoizedState.cachePool.pool),l!==a&&(e.flags|=2048),t!==null&&B(Ka),null;case 24:return a=null,t!==null&&(a=t.memoizedState.cache),e.memoizedState.cache!==a&&(e.flags|=2048),ke(jt),Tt(e),null;case 25:return null;case 30:return null}throw Error(s(156,e.tag))}function ey(t,e){switch(ws(e),e.tag){case 1:return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 3:return ke(jt),ua(),t=e.flags,(t&65536)!==0&&(t&128)===0?(e.flags=t&-65537|128,e):null;case 26:case 27:case 5:return cu(e),null;case 13:if($e(e),t=e.memoizedState,t!==null&&t.dehydrated!==null){if(e.alternate===null)throw Error(s(340));cn()}return t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 19:return B(qt),null;case 4:return ua(),null;case 10:return ke(e.type),null;case 22:case 23:return $e(e),Zs(),t!==null&&B(Ka),t=e.flags,t&65536?(e.flags=t&-65537|128,e):null;case 24:return ke(jt),null;case 25:return null;default:return null}}function sd(t,e){switch(ws(e),e.tag){case 3:ke(jt),ua();break;case 26:case 27:case 5:cu(e);break;case 4:ua();break;case 13:$e(e);break;case 19:B(qt);break;case 10:ke(e.type);break;case 22:case 23:$e(e),Zs(),t!==null&&B(Ka);break;case 24:ke(jt)}}function Mn(t,e){try{var a=e.updateQueue,l=a!==null?a.lastEffect:null;if(l!==null){var n=l.next;a=n;do{if((a.tag&t)===t){l=void 0;var u=a.create,r=a.inst;l=u(),r.destroy=l}a=a.next}while(a!==n)}}catch(d){yt(e,e.return,d)}}function ga(t,e,a){try{var l=e.updateQueue,n=l!==null?l.lastEffect:null;if(n!==null){var u=n.next;l=u;do{if((l.tag&t)===t){var r=l.inst,d=r.destroy;if(d!==void 0){r.destroy=void 0,n=e;var h=a,S=d;try{S()}catch(M){yt(n,h,M)}}}l=l.next}while(l!==u)}}catch(M){yt(e,e.return,M)}}function cd(t){var e=t.updateQueue;if(e!==null){var a=t.stateNode;try{Jr(e,a)}catch(l){yt(t,t.return,l)}}}function fd(t,e,a){a.props=Ja(t.type,t.memoizedProps),a.state=t.memoizedState;try{a.componentWillUnmount()}catch(l){yt(t,e,l)}}function An(t,e){try{var a=t.ref;if(a!==null){switch(t.tag){case 26:case 27:case 5:var l=t.stateNode;break;case 30:l=t.stateNode;break;default:l=t.stateNode}typeof a=="function"?t.refCleanup=a(l):a.current=l}}catch(n){yt(t,e,n)}}function He(t,e){var a=t.ref,l=t.refCleanup;if(a!==null)if(typeof l=="function")try{l()}catch(n){yt(t,e,n)}finally{t.refCleanup=null,t=t.alternate,t!=null&&(t.refCleanup=null)}else if(typeof a=="function")try{a(null)}catch(n){yt(t,e,n)}else a.current=null}function rd(t){var e=t.type,a=t.memoizedProps,l=t.stateNode;try{t:switch(e){case"button":case"input":case"select":case"textarea":a.autoFocus&&l.focus();break t;case"img":a.src?l.src=a.src:a.srcSet&&(l.srcset=a.srcSet)}}catch(n){yt(t,t.return,n)}}function bc(t,e,a){try{var l=t.stateNode;xy(l,t.type,a,e),l[It]=e}catch(n){yt(t,t.return,n)}}function od(t){return t.tag===5||t.tag===3||t.tag===26||t.tag===27&&Ta(t.type)||t.tag===4}function Sc(t){t:for(;;){for(;t.sibling===null;){if(t.return===null||od(t.return))return null;t=t.return}for(t.sibling.return=t.return,t=t.sibling;t.tag!==5&&t.tag!==6&&t.tag!==18;){if(t.tag===27&&Ta(t.type)||t.flags&2||t.child===null||t.tag===4)continue t;t.child.return=t,t=t.child}if(!(t.flags&2))return t.stateNode}}function pc(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?(a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a).insertBefore(t,e):(e=a.nodeType===9?a.body:a.nodeName==="HTML"?a.ownerDocument.body:a,e.appendChild(t),a=a._reactRootContainer,a!=null||e.onclick!==null||(e.onclick=fi));else if(l!==4&&(l===27&&Ta(t.type)&&(a=t.stateNode,e=null),t=t.child,t!==null))for(pc(t,e,a),t=t.sibling;t!==null;)pc(t,e,a),t=t.sibling}function Pu(t,e,a){var l=t.tag;if(l===5||l===6)t=t.stateNode,e?a.insertBefore(t,e):a.appendChild(t);else if(l!==4&&(l===27&&Ta(t.type)&&(a=t.stateNode),t=t.child,t!==null))for(Pu(t,e,a),t=t.sibling;t!==null;)Pu(t,e,a),t=t.sibling}function dd(t){var e=t.stateNode,a=t.memoizedProps;try{for(var l=t.type,n=e.attributes;n.length;)e.removeAttributeNode(n[0]);Qt(e,l,a),e[Vt]=t,e[It]=a}catch(u){yt(t,t.return,u)}}var Pe=!1,Ot=!1,_c=!1,hd=typeof WeakSet=="function"?WeakSet:Set,Ct=null;function ay(t,e){if(t=t.containerInfo,Vc=gi,t=Er(t),Ss(t)){if("selectionStart"in t)var a={start:t.selectionStart,end:t.selectionEnd};else t:{a=(a=t.ownerDocument)&&a.defaultView||window;var l=a.getSelection&&a.getSelection();if(l&&l.rangeCount!==0){a=l.anchorNode;var n=l.anchorOffset,u=l.focusNode;l=l.focusOffset;try{a.nodeType,u.nodeType}catch{a=null;break t}var r=0,d=-1,h=-1,S=0,M=0,N=t,p=null;e:for(;;){for(var _;N!==a||n!==0&&N.nodeType!==3||(d=r+n),N!==u||l!==0&&N.nodeType!==3||(h=r+l),N.nodeType===3&&(r+=N.nodeValue.length),(_=N.firstChild)!==null;)p=N,N=_;for(;;){if(N===t)break e;if(p===a&&++S===n&&(d=r),p===u&&++M===l&&(h=r),(_=N.nextSibling)!==null)break;N=p,p=N.parentNode}N=_}a=d===-1||h===-1?null:{start:d,end:h}}else a=null}a=a||{start:0,end:0}}else a=null;for(Kc={focusedElem:t,selectionRange:a},gi=!1,Ct=e;Ct!==null;)if(e=Ct,t=e.child,(e.subtreeFlags&1024)!==0&&t!==null)t.return=e,Ct=t;else for(;Ct!==null;){switch(e=Ct,u=e.alternate,t=e.flags,e.tag){case 0:break;case 11:case 15:break;case 1:if((t&1024)!==0&&u!==null){t=void 0,a=e,n=u.memoizedProps,u=u.memoizedState,l=a.stateNode;try{var V=Ja(a.type,n,a.elementType===a.type);t=l.getSnapshotBeforeUpdate(V,u),l.__reactInternalSnapshotBeforeUpdate=t}catch(G){yt(a,a.return,G)}}break;case 3:if((t&1024)!==0){if(t=e.stateNode.containerInfo,a=t.nodeType,a===9)$c(t);else if(a===1)switch(t.nodeName){case"HEAD":case"HTML":case"BODY":$c(t);break;default:t.textContent=""}}break;case 5:case 26:case 27:case 6:case 4:case 17:break;default:if((t&1024)!==0)throw Error(s(163))}if(t=e.sibling,t!==null){t.return=e.return,Ct=t;break}Ct=e.return}}function md(t,e,a){var l=a.flags;switch(a.tag){case 0:case 11:case 15:va(t,a),l&4&&Mn(5,a);break;case 1:if(va(t,a),l&4)if(t=a.stateNode,e===null)try{t.componentDidMount()}catch(r){yt(a,a.return,r)}else{var n=Ja(a.type,e.memoizedProps);e=e.memoizedState;try{t.componentDidUpdate(n,e,t.__reactInternalSnapshotBeforeUpdate)}catch(r){yt(a,a.return,r)}}l&64&&cd(a),l&512&&An(a,a.return);break;case 3:if(va(t,a),l&64&&(t=a.updateQueue,t!==null)){if(e=null,a.child!==null)switch(a.child.tag){case 27:case 5:e=a.child.stateNode;break;case 1:e=a.child.stateNode}try{Jr(t,e)}catch(r){yt(a,a.return,r)}}break;case 27:e===null&&l&4&&dd(a);case 26:case 5:va(t,a),e===null&&l&4&&rd(a),l&512&&An(a,a.return);break;case 12:va(t,a);break;case 13:va(t,a),l&4&&vd(t,a),l&64&&(t=a.memoizedState,t!==null&&(t=t.dehydrated,t!==null&&(a=oy.bind(null,a),Ny(t,a))));break;case 22:if(l=a.memoizedState!==null||Pe,!l){e=e!==null&&e.memoizedState!==null||Ot,n=Pe;var u=Ot;Pe=l,(Ot=e)&&!u?ba(t,a,(a.subtreeFlags&8772)!==0):va(t,a),Pe=n,Ot=u}break;case 30:break;default:va(t,a)}}function yd(t){var e=t.alternate;e!==null&&(t.alternate=null,yd(e)),t.child=null,t.deletions=null,t.sibling=null,t.tag===5&&(e=t.stateNode,e!==null&&ts(e)),t.stateNode=null,t.return=null,t.dependencies=null,t.memoizedProps=null,t.memoizedState=null,t.pendingProps=null,t.stateNode=null,t.updateQueue=null}var xt=null,ae=!1;function Ie(t,e,a){for(a=a.child;a!==null;)gd(t,e,a),a=a.sibling}function gd(t,e,a){if(ie&&typeof ie.onCommitFiberUnmount=="function")try{ie.onCommitFiberUnmount(Kl,a)}catch{}switch(a.tag){case 26:Ot||He(a,e),Ie(t,e,a),a.memoizedState?a.memoizedState.count--:a.stateNode&&(a=a.stateNode,a.parentNode.removeChild(a));break;case 27:Ot||He(a,e);var l=xt,n=ae;Ta(a.type)&&(xt=a.stateNode,ae=!1),Ie(t,e,a),qn(a.stateNode),xt=l,ae=n;break;case 5:Ot||He(a,e);case 6:if(l=xt,n=ae,xt=null,Ie(t,e,a),xt=l,ae=n,xt!==null)if(ae)try{(xt.nodeType===9?xt.body:xt.nodeName==="HTML"?xt.ownerDocument.body:xt).removeChild(a.stateNode)}catch(u){yt(a,e,u)}else try{xt.removeChild(a.stateNode)}catch(u){yt(a,e,u)}break;case 18:xt!==null&&(ae?(t=xt,nh(t.nodeType===9?t.body:t.nodeName==="HTML"?t.ownerDocument.body:t,a.stateNode),Ln(t)):nh(xt,a.stateNode));break;case 4:l=xt,n=ae,xt=a.stateNode.containerInfo,ae=!0,Ie(t,e,a),xt=l,ae=n;break;case 0:case 11:case 14:case 15:Ot||ga(2,a,e),Ot||ga(4,a,e),Ie(t,e,a);break;case 1:Ot||(He(a,e),l=a.stateNode,typeof l.componentWillUnmount=="function"&&fd(a,e,l)),Ie(t,e,a);break;case 21:Ie(t,e,a);break;case 22:Ot=(l=Ot)||a.memoizedState!==null,Ie(t,e,a),Ot=l;break;default:Ie(t,e,a)}}function vd(t,e){if(e.memoizedState===null&&(t=e.alternate,t!==null&&(t=t.memoizedState,t!==null&&(t=t.dehydrated,t!==null))))try{Ln(t)}catch(a){yt(e,e.return,a)}}function ly(t){switch(t.tag){case 13:case 19:var e=t.stateNode;return e===null&&(e=t.stateNode=new hd),e;case 22:return t=t.stateNode,e=t._retryCache,e===null&&(e=t._retryCache=new hd),e;default:throw Error(s(435,t.tag))}}function xc(t,e){var a=ly(t);e.forEach(function(l){var n=dy.bind(null,t,l);a.has(l)||(a.add(l),l.then(n,n))})}function re(t,e){var a=e.deletions;if(a!==null)for(var l=0;l<a.length;l++){var n=a[l],u=t,r=e,d=r;t:for(;d!==null;){switch(d.tag){case 27:if(Ta(d.type)){xt=d.stateNode,ae=!1;break t}break;case 5:xt=d.stateNode,ae=!1;break t;case 3:case 4:xt=d.stateNode.containerInfo,ae=!0;break t}d=d.return}if(xt===null)throw Error(s(160));gd(u,r,n),xt=null,ae=!1,u=n.alternate,u!==null&&(u.return=null),n.return=null}if(e.subtreeFlags&13878)for(e=e.child;e!==null;)bd(e,t),e=e.sibling}var Ne=null;function bd(t,e){var a=t.alternate,l=t.flags;switch(t.tag){case 0:case 11:case 14:case 15:re(e,t),oe(t),l&4&&(ga(3,t,t.return),Mn(3,t),ga(5,t,t.return));break;case 1:re(e,t),oe(t),l&512&&(Ot||a===null||He(a,a.return)),l&64&&Pe&&(t=t.updateQueue,t!==null&&(l=t.callbacks,l!==null&&(a=t.shared.hiddenCallbacks,t.shared.hiddenCallbacks=a===null?l:a.concat(l))));break;case 26:var n=Ne;if(re(e,t),oe(t),l&512&&(Ot||a===null||He(a,a.return)),l&4){var u=a!==null?a.memoizedState:null;if(l=t.memoizedState,a===null)if(l===null)if(t.stateNode===null){t:{l=t.type,a=t.memoizedProps,n=n.ownerDocument||n;e:switch(l){case"title":u=n.getElementsByTagName("title")[0],(!u||u[$l]||u[Vt]||u.namespaceURI==="http://www.w3.org/2000/svg"||u.hasAttribute("itemprop"))&&(u=n.createElement(l),n.head.insertBefore(u,n.querySelector("head > title"))),Qt(u,l,a),u[Vt]=t,Ht(u),l=u;break t;case"link":var r=dh("link","href",n).get(l+(a.href||""));if(r){for(var d=0;d<r.length;d++)if(u=r[d],u.getAttribute("href")===(a.href==null||a.href===""?null:a.href)&&u.getAttribute("rel")===(a.rel==null?null:a.rel)&&u.getAttribute("title")===(a.title==null?null:a.title)&&u.getAttribute("crossorigin")===(a.crossOrigin==null?null:a.crossOrigin)){r.splice(d,1);break e}}u=n.createElement(l),Qt(u,l,a),n.head.appendChild(u);break;case"meta":if(r=dh("meta","content",n).get(l+(a.content||""))){for(d=0;d<r.length;d++)if(u=r[d],u.getAttribute("content")===(a.content==null?null:""+a.content)&&u.getAttribute("name")===(a.name==null?null:a.name)&&u.getAttribute("property")===(a.property==null?null:a.property)&&u.getAttribute("http-equiv")===(a.httpEquiv==null?null:a.httpEquiv)&&u.getAttribute("charset")===(a.charSet==null?null:a.charSet)){r.splice(d,1);break e}}u=n.createElement(l),Qt(u,l,a),n.head.appendChild(u);break;default:throw Error(s(468,l))}u[Vt]=t,Ht(u),l=u}t.stateNode=l}else hh(n,t.type,t.stateNode);else t.stateNode=oh(n,l,t.memoizedProps);else u!==l?(u===null?a.stateNode!==null&&(a=a.stateNode,a.parentNode.removeChild(a)):u.count--,l===null?hh(n,t.type,t.stateNode):oh(n,l,t.memoizedProps)):l===null&&t.stateNode!==null&&bc(t,t.memoizedProps,a.memoizedProps)}break;case 27:re(e,t),oe(t),l&512&&(Ot||a===null||He(a,a.return)),a!==null&&l&4&&bc(t,t.memoizedProps,a.memoizedProps);break;case 5:if(re(e,t),oe(t),l&512&&(Ot||a===null||He(a,a.return)),t.flags&32){n=t.stateNode;try{fl(n,"")}catch(_){yt(t,t.return,_)}}l&4&&t.stateNode!=null&&(n=t.memoizedProps,bc(t,n,a!==null?a.memoizedProps:n)),l&1024&&(_c=!0);break;case 6:if(re(e,t),oe(t),l&4){if(t.stateNode===null)throw Error(s(162));l=t.memoizedProps,a=t.stateNode;try{a.nodeValue=l}catch(_){yt(t,t.return,_)}}break;case 3:if(hi=null,n=Ne,Ne=oi(e.containerInfo),re(e,t),Ne=n,oe(t),l&4&&a!==null&&a.memoizedState.isDehydrated)try{Ln(e.containerInfo)}catch(_){yt(t,t.return,_)}_c&&(_c=!1,Sd(t));break;case 4:l=Ne,Ne=oi(t.stateNode.containerInfo),re(e,t),oe(t),Ne=l;break;case 12:re(e,t),oe(t);break;case 13:re(e,t),oe(t),t.child.flags&8192&&t.memoizedState!==null!=(a!==null&&a.memoizedState!==null)&&(Oc=Ue()),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,xc(t,l)));break;case 22:n=t.memoizedState!==null;var h=a!==null&&a.memoizedState!==null,S=Pe,M=Ot;if(Pe=S||n,Ot=M||h,re(e,t),Ot=M,Pe=S,oe(t),l&8192)t:for(e=t.stateNode,e._visibility=n?e._visibility&-2:e._visibility|1,n&&(a===null||h||Pe||Ot||$a(t)),a=null,e=t;;){if(e.tag===5||e.tag===26){if(a===null){h=a=e;try{if(u=h.stateNode,n)r=u.style,typeof r.setProperty=="function"?r.setProperty("display","none","important"):r.display="none";else{d=h.stateNode;var N=h.memoizedProps.style,p=N!=null&&N.hasOwnProperty("display")?N.display:null;d.style.display=p==null||typeof p=="boolean"?"":(""+p).trim()}}catch(_){yt(h,h.return,_)}}}else if(e.tag===6){if(a===null){h=e;try{h.stateNode.nodeValue=n?"":h.memoizedProps}catch(_){yt(h,h.return,_)}}}else if((e.tag!==22&&e.tag!==23||e.memoizedState===null||e===t)&&e.child!==null){e.child.return=e,e=e.child;continue}if(e===t)break t;for(;e.sibling===null;){if(e.return===null||e.return===t)break t;a===e&&(a=null),e=e.return}a===e&&(a=null),e.sibling.return=e.return,e=e.sibling}l&4&&(l=t.updateQueue,l!==null&&(a=l.retryQueue,a!==null&&(l.retryQueue=null,xc(t,a))));break;case 19:re(e,t),oe(t),l&4&&(l=t.updateQueue,l!==null&&(t.updateQueue=null,xc(t,l)));break;case 30:break;case 21:break;default:re(e,t),oe(t)}}function oe(t){var e=t.flags;if(e&2){try{for(var a,l=t.return;l!==null;){if(od(l)){a=l;break}l=l.return}if(a==null)throw Error(s(160));switch(a.tag){case 27:var n=a.stateNode,u=Sc(t);Pu(t,u,n);break;case 5:var r=a.stateNode;a.flags&32&&(fl(r,""),a.flags&=-33);var d=Sc(t);Pu(t,d,r);break;case 3:case 4:var h=a.stateNode.containerInfo,S=Sc(t);pc(t,S,h);break;default:throw Error(s(161))}}catch(M){yt(t,t.return,M)}t.flags&=-3}e&4096&&(t.flags&=-4097)}function Sd(t){if(t.subtreeFlags&1024)for(t=t.child;t!==null;){var e=t;Sd(e),e.tag===5&&e.flags&1024&&e.stateNode.reset(),t=t.sibling}}function va(t,e){if(e.subtreeFlags&8772)for(e=e.child;e!==null;)md(t,e.alternate,e),e=e.sibling}function $a(t){for(t=t.child;t!==null;){var e=t;switch(e.tag){case 0:case 11:case 14:case 15:ga(4,e,e.return),$a(e);break;case 1:He(e,e.return);var a=e.stateNode;typeof a.componentWillUnmount=="function"&&fd(e,e.return,a),$a(e);break;case 27:qn(e.stateNode);case 26:case 5:He(e,e.return),$a(e);break;case 22:e.memoizedState===null&&$a(e);break;case 30:$a(e);break;default:$a(e)}t=t.sibling}}function ba(t,e,a){for(a=a&&(e.subtreeFlags&8772)!==0,e=e.child;e!==null;){var l=e.alternate,n=t,u=e,r=u.flags;switch(u.tag){case 0:case 11:case 15:ba(n,u,a),Mn(4,u);break;case 1:if(ba(n,u,a),l=u,n=l.stateNode,typeof n.componentDidMount=="function")try{n.componentDidMount()}catch(S){yt(l,l.return,S)}if(l=u,n=l.updateQueue,n!==null){var d=l.stateNode;try{var h=n.shared.hiddenCallbacks;if(h!==null)for(n.shared.hiddenCallbacks=null,n=0;n<h.length;n++)kr(h[n],d)}catch(S){yt(l,l.return,S)}}a&&r&64&&cd(u),An(u,u.return);break;case 27:dd(u);case 26:case 5:ba(n,u,a),a&&l===null&&r&4&&rd(u),An(u,u.return);break;case 12:ba(n,u,a);break;case 13:ba(n,u,a),a&&r&4&&vd(n,u);break;case 22:u.memoizedState===null&&ba(n,u,a),An(u,u.return);break;case 30:break;default:ba(n,u,a)}e=e.sibling}}function Ec(t,e){var a=null;t!==null&&t.memoizedState!==null&&t.memoizedState.cachePool!==null&&(a=t.memoizedState.cachePool.pool),t=null,e.memoizedState!==null&&e.memoizedState.cachePool!==null&&(t=e.memoizedState.cachePool.pool),t!==a&&(t!=null&&t.refCount++,a!=null&&on(a))}function Tc(t,e){t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&on(t))}function Be(t,e,a,l){if(e.subtreeFlags&10256)for(e=e.child;e!==null;)pd(t,e,a,l),e=e.sibling}function pd(t,e,a,l){var n=e.flags;switch(e.tag){case 0:case 11:case 15:Be(t,e,a,l),n&2048&&Mn(9,e);break;case 1:Be(t,e,a,l);break;case 3:Be(t,e,a,l),n&2048&&(t=null,e.alternate!==null&&(t=e.alternate.memoizedState.cache),e=e.memoizedState.cache,e!==t&&(e.refCount++,t!=null&&on(t)));break;case 12:if(n&2048){Be(t,e,a,l),t=e.stateNode;try{var u=e.memoizedProps,r=u.id,d=u.onPostCommit;typeof d=="function"&&d(r,e.alternate===null?"mount":"update",t.passiveEffectDuration,-0)}catch(h){yt(e,e.return,h)}}else Be(t,e,a,l);break;case 13:Be(t,e,a,l);break;case 23:break;case 22:u=e.stateNode,r=e.alternate,e.memoizedState!==null?u._visibility&2?Be(t,e,a,l):Rn(t,e):u._visibility&2?Be(t,e,a,l):(u._visibility|=2,Ol(t,e,a,l,(e.subtreeFlags&10256)!==0)),n&2048&&Ec(r,e);break;case 24:Be(t,e,a,l),n&2048&&Tc(e.alternate,e);break;default:Be(t,e,a,l)}}function Ol(t,e,a,l,n){for(n=n&&(e.subtreeFlags&10256)!==0,e=e.child;e!==null;){var u=t,r=e,d=a,h=l,S=r.flags;switch(r.tag){case 0:case 11:case 15:Ol(u,r,d,h,n),Mn(8,r);break;case 23:break;case 22:var M=r.stateNode;r.memoizedState!==null?M._visibility&2?Ol(u,r,d,h,n):Rn(u,r):(M._visibility|=2,Ol(u,r,d,h,n)),n&&S&2048&&Ec(r.alternate,r);break;case 24:Ol(u,r,d,h,n),n&&S&2048&&Tc(r.alternate,r);break;default:Ol(u,r,d,h,n)}e=e.sibling}}function Rn(t,e){if(e.subtreeFlags&10256)for(e=e.child;e!==null;){var a=t,l=e,n=l.flags;switch(l.tag){case 22:Rn(a,l),n&2048&&Ec(l.alternate,l);break;case 24:Rn(a,l),n&2048&&Tc(l.alternate,l);break;default:Rn(a,l)}e=e.sibling}}var On=8192;function Nl(t){if(t.subtreeFlags&On)for(t=t.child;t!==null;)_d(t),t=t.sibling}function _d(t){switch(t.tag){case 26:Nl(t),t.flags&On&&t.memoizedState!==null&&Qy(Ne,t.memoizedState,t.memoizedProps);break;case 5:Nl(t);break;case 3:case 4:var e=Ne;Ne=oi(t.stateNode.containerInfo),Nl(t),Ne=e;break;case 22:t.memoizedState===null&&(e=t.alternate,e!==null&&e.memoizedState!==null?(e=On,On=16777216,Nl(t),On=e):Nl(t));break;default:Nl(t)}}function xd(t){var e=t.alternate;if(e!==null&&(t=e.child,t!==null)){e.child=null;do e=t.sibling,t.sibling=null,t=e;while(t!==null)}}function Nn(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];Ct=l,Td(l,t)}xd(t)}if(t.subtreeFlags&10256)for(t=t.child;t!==null;)Ed(t),t=t.sibling}function Ed(t){switch(t.tag){case 0:case 11:case 15:Nn(t),t.flags&2048&&ga(9,t,t.return);break;case 3:Nn(t);break;case 12:Nn(t);break;case 22:var e=t.stateNode;t.memoizedState!==null&&e._visibility&2&&(t.return===null||t.return.tag!==13)?(e._visibility&=-3,Iu(t)):Nn(t);break;default:Nn(t)}}function Iu(t){var e=t.deletions;if((t.flags&16)!==0){if(e!==null)for(var a=0;a<e.length;a++){var l=e[a];Ct=l,Td(l,t)}xd(t)}for(t=t.child;t!==null;){switch(e=t,e.tag){case 0:case 11:case 15:ga(8,e,e.return),Iu(e);break;case 22:a=e.stateNode,a._visibility&2&&(a._visibility&=-3,Iu(e));break;default:Iu(e)}t=t.sibling}}function Td(t,e){for(;Ct!==null;){var a=Ct;switch(a.tag){case 0:case 11:case 15:ga(8,a,e);break;case 23:case 22:if(a.memoizedState!==null&&a.memoizedState.cachePool!==null){var l=a.memoizedState.cachePool.pool;l!=null&&l.refCount++}break;case 24:on(a.memoizedState.cache)}if(l=a.child,l!==null)l.return=a,Ct=l;else t:for(a=t;Ct!==null;){l=Ct;var n=l.sibling,u=l.return;if(yd(l),l===a){Ct=null;break t}if(n!==null){n.return=u,Ct=n;break t}Ct=u}}}var ny={getCacheForType:function(t){var e=Kt(jt),a=e.data.get(t);return a===void 0&&(a=t(),e.data.set(t,a)),a}},uy=typeof WeakMap=="function"?WeakMap:Map,ft=0,vt=null,et=null,nt=0,rt=0,de=null,Sa=!1,wl=!1,Mc=!1,ta=0,At=0,pa=0,Wa=0,Ac=0,Te=0,Dl=0,wn=null,le=null,Rc=!1,Oc=0,ti=1/0,ei=null,_a=null,Gt=0,xa=null,zl=null,Ul=0,Nc=0,wc=null,Md=null,Dn=0,Dc=null;function he(){if((ft&2)!==0&&nt!==0)return nt&-nt;if(A.T!==null){var t=pl;return t!==0?t:Cc()}return Xf()}function Ad(){Te===0&&(Te=(nt&536870912)===0||ct?Hf():536870912);var t=Ee.current;return t!==null&&(t.flags|=32),Te}function me(t,e,a){(t===vt&&(rt===2||rt===9)||t.cancelPendingCommit!==null)&&(jl(t,0),Ea(t,nt,Te,!1)),Jl(t,a),((ft&2)===0||t!==vt)&&(t===vt&&((ft&2)===0&&(Wa|=a),At===4&&Ea(t,nt,Te,!1)),Ce(t))}function Rd(t,e,a){if((ft&6)!==0)throw Error(s(327));var l=!a&&(e&124)===0&&(e&t.expiredLanes)===0||kl(t,e),n=l?cy(t,e):jc(t,e,!0),u=l;do{if(n===0){wl&&!l&&Ea(t,e,0,!1);break}else{if(a=t.current.alternate,u&&!iy(a)){n=jc(t,e,!1),u=!1;continue}if(n===2){if(u=e,t.errorRecoveryDisabledLanes&u)var r=0;else r=t.pendingLanes&-536870913,r=r!==0?r:r&536870912?536870912:0;if(r!==0){e=r;t:{var d=t;n=wn;var h=d.current.memoizedState.isDehydrated;if(h&&(jl(d,r).flags|=256),r=jc(d,r,!1),r!==2){if(Mc&&!h){d.errorRecoveryDisabledLanes|=u,Wa|=u,n=4;break t}u=le,le=n,u!==null&&(le===null?le=u:le.push.apply(le,u))}n=r}if(u=!1,n!==2)continue}}if(n===1){jl(t,0),Ea(t,e,0,!0);break}t:{switch(l=t,u=n,u){case 0:case 1:throw Error(s(345));case 4:if((e&4194048)!==e)break;case 6:Ea(l,e,Te,!Sa);break t;case 2:le=null;break;case 3:case 5:break;default:throw Error(s(329))}if((e&62914560)===e&&(n=Oc+300-Ue(),10<n)){if(Ea(l,e,Te,!Sa),du(l,0,!0)!==0)break t;l.timeoutHandle=ah(Od.bind(null,l,a,le,ei,Rc,e,Te,Wa,Dl,Sa,u,2,-0,0),n);break t}Od(l,a,le,ei,Rc,e,Te,Wa,Dl,Sa,u,0,-0,0)}}break}while(!0);Ce(t)}function Od(t,e,a,l,n,u,r,d,h,S,M,N,p,_){if(t.timeoutHandle=-1,N=e.subtreeFlags,(N&8192||(N&16785408)===16785408)&&(Cn={stylesheets:null,count:0,unsuspend:Gy},_d(e),N=Ly(),N!==null)){t.cancelPendingCommit=N(qd.bind(null,t,e,u,a,l,n,r,d,h,M,1,p,_)),Ea(t,u,r,!S);return}qd(t,e,u,a,l,n,r,d,h)}function iy(t){for(var e=t;;){var a=e.tag;if((a===0||a===11||a===15)&&e.flags&16384&&(a=e.updateQueue,a!==null&&(a=a.stores,a!==null)))for(var l=0;l<a.length;l++){var n=a[l],u=n.getSnapshot;n=n.value;try{if(!ce(u(),n))return!1}catch{return!1}}if(a=e.child,e.subtreeFlags&16384&&a!==null)a.return=e,e=a;else{if(e===t)break;for(;e.sibling===null;){if(e.return===null||e.return===t)return!0;e=e.return}e.sibling.return=e.return,e=e.sibling}}return!0}function Ea(t,e,a,l){e&=~Ac,e&=~Wa,t.suspendedLanes|=e,t.pingedLanes&=~e,l&&(t.warmLanes|=e),l=t.expirationTimes;for(var n=e;0<n;){var u=31-se(n),r=1<<u;l[u]=-1,n&=~r}a!==0&&Cf(t,a,e)}function ai(){return(ft&6)===0?(zn(0),!1):!0}function zc(){if(et!==null){if(rt===0)var t=et.return;else t=et,Ke=Za=null,$s(t),Al=null,xn=0,t=et;for(;t!==null;)sd(t.alternate,t),t=t.return;et=null}}function jl(t,e){var a=t.timeoutHandle;a!==-1&&(t.timeoutHandle=-1,Ty(a)),a=t.cancelPendingCommit,a!==null&&(t.cancelPendingCommit=null,a()),zc(),vt=t,et=a=Le(t.current,null),nt=e,rt=0,de=null,Sa=!1,wl=kl(t,e),Mc=!1,Dl=Te=Ac=Wa=pa=At=0,le=wn=null,Rc=!1,(e&8)!==0&&(e|=e&32);var l=t.entangledLanes;if(l!==0)for(t=t.entanglements,l&=e;0<l;){var n=31-se(l),u=1<<n;e|=t[n],l&=~u}return ta=e,Tu(),a}function Nd(t,e){P=null,A.H=Lu,e===hn||e===Uu?(e=Vr(),rt=3):e===Qr?(e=Vr(),rt=4):rt=e===ko?8:e!==null&&typeof e=="object"&&typeof e.then=="function"?6:1,de=e,et===null&&(At=1,Ju(t,Se(e,t.current)))}function wd(){var t=A.H;return A.H=Lu,t===null?Lu:t}function Dd(){var t=A.A;return A.A=ny,t}function Uc(){At=4,Sa||(nt&4194048)!==nt&&Ee.current!==null||(wl=!0),(pa&134217727)===0&&(Wa&134217727)===0||vt===null||Ea(vt,nt,Te,!1)}function jc(t,e,a){var l=ft;ft|=2;var n=wd(),u=Dd();(vt!==t||nt!==e)&&(ei=null,jl(t,e)),e=!1;var r=At;t:do try{if(rt!==0&&et!==null){var d=et,h=de;switch(rt){case 8:zc(),r=6;break t;case 3:case 2:case 9:case 6:Ee.current===null&&(e=!0);var S=rt;if(rt=0,de=null,ql(t,d,h,S),a&&wl){r=0;break t}break;default:S=rt,rt=0,de=null,ql(t,d,h,S)}}sy(),r=At;break}catch(M){Nd(t,M)}while(!0);return e&&t.shellSuspendCounter++,Ke=Za=null,ft=l,A.H=n,A.A=u,et===null&&(vt=null,nt=0,Tu()),r}function sy(){for(;et!==null;)zd(et)}function cy(t,e){var a=ft;ft|=2;var l=wd(),n=Dd();vt!==t||nt!==e?(ei=null,ti=Ue()+500,jl(t,e)):wl=kl(t,e);t:do try{if(rt!==0&&et!==null){e=et;var u=de;e:switch(rt){case 1:rt=0,de=null,ql(t,e,u,1);break;case 2:case 9:if(Lr(u)){rt=0,de=null,Ud(e);break}e=function(){rt!==2&&rt!==9||vt!==t||(rt=7),Ce(t)},u.then(e,e);break t;case 3:rt=7;break t;case 4:rt=5;break t;case 7:Lr(u)?(rt=0,de=null,Ud(e)):(rt=0,de=null,ql(t,e,u,7));break;case 5:var r=null;switch(et.tag){case 26:r=et.memoizedState;case 5:case 27:var d=et;if(!r||mh(r)){rt=0,de=null;var h=d.sibling;if(h!==null)et=h;else{var S=d.return;S!==null?(et=S,li(S)):et=null}break e}}rt=0,de=null,ql(t,e,u,5);break;case 6:rt=0,de=null,ql(t,e,u,6);break;case 8:zc(),At=6;break t;default:throw Error(s(462))}}fy();break}catch(M){Nd(t,M)}while(!0);return Ke=Za=null,A.H=l,A.A=n,ft=a,et!==null?0:(vt=null,nt=0,Tu(),At)}function fy(){for(;et!==null&&!Dm();)zd(et)}function zd(t){var e=ud(t.alternate,t,ta);t.memoizedProps=t.pendingProps,e===null?li(t):et=e}function Ud(t){var e=t,a=e.alternate;switch(e.tag){case 15:case 0:e=Io(a,e,e.pendingProps,e.type,void 0,nt);break;case 11:e=Io(a,e,e.pendingProps,e.type.render,e.ref,nt);break;case 5:$s(e);default:sd(a,e),e=et=Ur(e,ta),e=ud(a,e,ta)}t.memoizedProps=t.pendingProps,e===null?li(t):et=e}function ql(t,e,a,l){Ke=Za=null,$s(e),Al=null,xn=0;var n=e.return;try{if(P0(t,n,e,a,nt)){At=1,Ju(t,Se(a,t.current)),et=null;return}}catch(u){if(n!==null)throw et=n,u;At=1,Ju(t,Se(a,t.current)),et=null;return}e.flags&32768?(ct||l===1?t=!0:wl||(nt&536870912)!==0?t=!1:(Sa=t=!0,(l===2||l===9||l===3||l===6)&&(l=Ee.current,l!==null&&l.tag===13&&(l.flags|=16384))),jd(e,t)):li(e)}function li(t){var e=t;do{if((e.flags&32768)!==0){jd(e,Sa);return}t=e.return;var a=ty(e.alternate,e,ta);if(a!==null){et=a;return}if(e=e.sibling,e!==null){et=e;return}et=e=t}while(e!==null);At===0&&(At=5)}function jd(t,e){do{var a=ey(t.alternate,t);if(a!==null){a.flags&=32767,et=a;return}if(a=t.return,a!==null&&(a.flags|=32768,a.subtreeFlags=0,a.deletions=null),!e&&(t=t.sibling,t!==null)){et=t;return}et=t=a}while(t!==null);At=6,et=null}function qd(t,e,a,l,n,u,r,d,h){t.cancelPendingCommit=null;do ni();while(Gt!==0);if((ft&6)!==0)throw Error(s(327));if(e!==null){if(e===t.current)throw Error(s(177));if(u=e.lanes|e.childLanes,u|=Ts,Gm(t,a,u,r,d,h),t===vt&&(et=vt=null,nt=0),zl=e,xa=t,Ul=a,Nc=u,wc=n,Md=l,(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?(t.callbackNode=null,t.callbackPriority=0,hy(fu,function(){return Xd(),null})):(t.callbackNode=null,t.callbackPriority=0),l=(e.flags&13878)!==0,(e.subtreeFlags&13878)!==0||l){l=A.T,A.T=null,n=q.p,q.p=2,r=ft,ft|=4;try{ay(t,e,a)}finally{ft=r,q.p=n,A.T=l}}Gt=1,Hd(),Bd(),Cd()}}function Hd(){if(Gt===1){Gt=0;var t=xa,e=zl,a=(e.flags&13878)!==0;if((e.subtreeFlags&13878)!==0||a){a=A.T,A.T=null;var l=q.p;q.p=2;var n=ft;ft|=4;try{bd(e,t);var u=Kc,r=Er(t.containerInfo),d=u.focusedElem,h=u.selectionRange;if(r!==d&&d&&d.ownerDocument&&xr(d.ownerDocument.documentElement,d)){if(h!==null&&Ss(d)){var S=h.start,M=h.end;if(M===void 0&&(M=S),"selectionStart"in d)d.selectionStart=S,d.selectionEnd=Math.min(M,d.value.length);else{var N=d.ownerDocument||document,p=N&&N.defaultView||window;if(p.getSelection){var _=p.getSelection(),V=d.textContent.length,G=Math.min(h.start,V),ht=h.end===void 0?G:Math.min(h.end,V);!_.extend&&G>ht&&(r=ht,ht=G,G=r);var v=_r(d,G),g=_r(d,ht);if(v&&g&&(_.rangeCount!==1||_.anchorNode!==v.node||_.anchorOffset!==v.offset||_.focusNode!==g.node||_.focusOffset!==g.offset)){var b=N.createRange();b.setStart(v.node,v.offset),_.removeAllRanges(),G>ht?(_.addRange(b),_.extend(g.node,g.offset)):(b.setEnd(g.node,g.offset),_.addRange(b))}}}}for(N=[],_=d;_=_.parentNode;)_.nodeType===1&&N.push({element:_,left:_.scrollLeft,top:_.scrollTop});for(typeof d.focus=="function"&&d.focus(),d=0;d<N.length;d++){var R=N[d];R.element.scrollLeft=R.left,R.element.scrollTop=R.top}}gi=!!Vc,Kc=Vc=null}finally{ft=n,q.p=l,A.T=a}}t.current=e,Gt=2}}function Bd(){if(Gt===2){Gt=0;var t=xa,e=zl,a=(e.flags&8772)!==0;if((e.subtreeFlags&8772)!==0||a){a=A.T,A.T=null;var l=q.p;q.p=2;var n=ft;ft|=4;try{md(t,e.alternate,e)}finally{ft=n,q.p=l,A.T=a}}Gt=3}}function Cd(){if(Gt===4||Gt===3){Gt=0,zm();var t=xa,e=zl,a=Ul,l=Md;(e.subtreeFlags&10256)!==0||(e.flags&10256)!==0?Gt=5:(Gt=0,zl=xa=null,Yd(t,t.pendingLanes));var n=t.pendingLanes;if(n===0&&(_a=null),Pi(a),e=e.stateNode,ie&&typeof ie.onCommitFiberRoot=="function")try{ie.onCommitFiberRoot(Kl,e,void 0,(e.current.flags&128)===128)}catch{}if(l!==null){e=A.T,n=q.p,q.p=2,A.T=null;try{for(var u=t.onRecoverableError,r=0;r<l.length;r++){var d=l[r];u(d.value,{componentStack:d.stack})}}finally{A.T=e,q.p=n}}(Ul&3)!==0&&ni(),Ce(t),n=t.pendingLanes,(a&4194090)!==0&&(n&42)!==0?t===Dc?Dn++:(Dn=0,Dc=t):Dn=0,zn(0)}}function Yd(t,e){(t.pooledCacheLanes&=e)===0&&(e=t.pooledCache,e!=null&&(t.pooledCache=null,on(e)))}function ni(t){return Hd(),Bd(),Cd(),Xd()}function Xd(){if(Gt!==5)return!1;var t=xa,e=Nc;Nc=0;var a=Pi(Ul),l=A.T,n=q.p;try{q.p=32>a?32:a,A.T=null,a=wc,wc=null;var u=xa,r=Ul;if(Gt=0,zl=xa=null,Ul=0,(ft&6)!==0)throw Error(s(331));var d=ft;if(ft|=4,Ed(u.current),pd(u,u.current,r,a),ft=d,zn(0,!1),ie&&typeof ie.onPostCommitFiberRoot=="function")try{ie.onPostCommitFiberRoot(Kl,u)}catch{}return!0}finally{q.p=n,A.T=l,Yd(t,e)}}function Gd(t,e,a){e=Se(a,e),e=fc(t.stateNode,e,2),t=da(t,e,2),t!==null&&(Jl(t,2),Ce(t))}function yt(t,e,a){if(t.tag===3)Gd(t,t,a);else for(;e!==null;){if(e.tag===3){Gd(e,t,a);break}else if(e.tag===1){var l=e.stateNode;if(typeof e.type.getDerivedStateFromError=="function"||typeof l.componentDidCatch=="function"&&(_a===null||!_a.has(l))){t=Se(a,t),a=Vo(2),l=da(e,a,2),l!==null&&(Ko(a,l,e,t),Jl(l,2),Ce(l));break}}e=e.return}}function qc(t,e,a){var l=t.pingCache;if(l===null){l=t.pingCache=new uy;var n=new Set;l.set(e,n)}else n=l.get(e),n===void 0&&(n=new Set,l.set(e,n));n.has(a)||(Mc=!0,n.add(a),t=ry.bind(null,t,e,a),e.then(t,t))}function ry(t,e,a){var l=t.pingCache;l!==null&&l.delete(e),t.pingedLanes|=t.suspendedLanes&a,t.warmLanes&=~a,vt===t&&(nt&a)===a&&(At===4||At===3&&(nt&62914560)===nt&&300>Ue()-Oc?(ft&2)===0&&jl(t,0):Ac|=a,Dl===nt&&(Dl=0)),Ce(t)}function Qd(t,e){e===0&&(e=Bf()),t=gl(t,e),t!==null&&(Jl(t,e),Ce(t))}function oy(t){var e=t.memoizedState,a=0;e!==null&&(a=e.retryLane),Qd(t,a)}function dy(t,e){var a=0;switch(t.tag){case 13:var l=t.stateNode,n=t.memoizedState;n!==null&&(a=n.retryLane);break;case 19:l=t.stateNode;break;case 22:l=t.stateNode._retryCache;break;default:throw Error(s(314))}l!==null&&l.delete(e),Qd(t,a)}function hy(t,e){return Ji(t,e)}var ui=null,Hl=null,Hc=!1,ii=!1,Bc=!1,Fa=0;function Ce(t){t!==Hl&&t.next===null&&(Hl===null?ui=Hl=t:Hl=Hl.next=t),ii=!0,Hc||(Hc=!0,yy())}function zn(t,e){if(!Bc&&ii){Bc=!0;do for(var a=!1,l=ui;l!==null;){if(t!==0){var n=l.pendingLanes;if(n===0)var u=0;else{var r=l.suspendedLanes,d=l.pingedLanes;u=(1<<31-se(42|t)+1)-1,u&=n&~(r&~d),u=u&201326741?u&201326741|1:u?u|2:0}u!==0&&(a=!0,Kd(l,u))}else u=nt,u=du(l,l===vt?u:0,l.cancelPendingCommit!==null||l.timeoutHandle!==-1),(u&3)===0||kl(l,u)||(a=!0,Kd(l,u));l=l.next}while(a);Bc=!1}}function my(){Ld()}function Ld(){ii=Hc=!1;var t=0;Fa!==0&&(Ey()&&(t=Fa),Fa=0);for(var e=Ue(),a=null,l=ui;l!==null;){var n=l.next,u=Zd(l,e);u===0?(l.next=null,a===null?ui=n:a.next=n,n===null&&(Hl=a)):(a=l,(t!==0||(u&3)!==0)&&(ii=!0)),l=n}zn(t)}function Zd(t,e){for(var a=t.suspendedLanes,l=t.pingedLanes,n=t.expirationTimes,u=t.pendingLanes&-62914561;0<u;){var r=31-se(u),d=1<<r,h=n[r];h===-1?((d&a)===0||(d&l)!==0)&&(n[r]=Xm(d,e)):h<=e&&(t.expiredLanes|=d),u&=~d}if(e=vt,a=nt,a=du(t,t===e?a:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l=t.callbackNode,a===0||t===e&&(rt===2||rt===9)||t.cancelPendingCommit!==null)return l!==null&&l!==null&&$i(l),t.callbackNode=null,t.callbackPriority=0;if((a&3)===0||kl(t,a)){if(e=a&-a,e===t.callbackPriority)return e;switch(l!==null&&$i(l),Pi(a)){case 2:case 8:a=jf;break;case 32:a=fu;break;case 268435456:a=qf;break;default:a=fu}return l=Vd.bind(null,t),a=Ji(a,l),t.callbackPriority=e,t.callbackNode=a,e}return l!==null&&l!==null&&$i(l),t.callbackPriority=2,t.callbackNode=null,2}function Vd(t,e){if(Gt!==0&&Gt!==5)return t.callbackNode=null,t.callbackPriority=0,null;var a=t.callbackNode;if(ni()&&t.callbackNode!==a)return null;var l=nt;return l=du(t,t===vt?l:0,t.cancelPendingCommit!==null||t.timeoutHandle!==-1),l===0?null:(Rd(t,l,e),Zd(t,Ue()),t.callbackNode!=null&&t.callbackNode===a?Vd.bind(null,t):null)}function Kd(t,e){if(ni())return null;Rd(t,e,!0)}function yy(){My(function(){(ft&6)!==0?Ji(Uf,my):Ld()})}function Cc(){return Fa===0&&(Fa=Hf()),Fa}function kd(t){return t==null||typeof t=="symbol"||typeof t=="boolean"?null:typeof t=="function"?t:vu(""+t)}function Jd(t,e){var a=e.ownerDocument.createElement("input");return a.name=e.name,a.value=e.value,t.id&&a.setAttribute("form",t.id),e.parentNode.insertBefore(a,e),t=new FormData(t),a.parentNode.removeChild(a),t}function gy(t,e,a,l,n){if(e==="submit"&&a&&a.stateNode===n){var u=kd((n[It]||null).action),r=l.submitter;r&&(e=(e=r[It]||null)?kd(e.formAction):r.getAttribute("formAction"),e!==null&&(u=e,r=null));var d=new _u("action","action",null,l,n);t.push({event:d,listeners:[{instance:null,listener:function(){if(l.defaultPrevented){if(Fa!==0){var h=r?Jd(n,r):new FormData(n);nc(a,{pending:!0,data:h,method:n.method,action:u},null,h)}}else typeof u=="function"&&(d.preventDefault(),h=r?Jd(n,r):new FormData(n),nc(a,{pending:!0,data:h,method:n.method,action:u},u,h))},currentTarget:n}]})}}for(var Yc=0;Yc<Es.length;Yc++){var Xc=Es[Yc],vy=Xc.toLowerCase(),by=Xc[0].toUpperCase()+Xc.slice(1);Oe(vy,"on"+by)}Oe(Ar,"onAnimationEnd"),Oe(Rr,"onAnimationIteration"),Oe(Or,"onAnimationStart"),Oe("dblclick","onDoubleClick"),Oe("focusin","onFocus"),Oe("focusout","onBlur"),Oe(q0,"onTransitionRun"),Oe(H0,"onTransitionStart"),Oe(B0,"onTransitionCancel"),Oe(Nr,"onTransitionEnd"),il("onMouseEnter",["mouseout","mouseover"]),il("onMouseLeave",["mouseout","mouseover"]),il("onPointerEnter",["pointerout","pointerover"]),il("onPointerLeave",["pointerout","pointerover"]),qa("onChange","change click focusin focusout input keydown keyup selectionchange".split(" ")),qa("onSelect","focusout contextmenu dragend focusin keydown keyup mousedown mouseup selectionchange".split(" ")),qa("onBeforeInput",["compositionend","keypress","textInput","paste"]),qa("onCompositionEnd","compositionend focusout keydown keypress keyup mousedown".split(" ")),qa("onCompositionStart","compositionstart focusout keydown keypress keyup mousedown".split(" ")),qa("onCompositionUpdate","compositionupdate focusout keydown keypress keyup mousedown".split(" "));var Un="abort canplay canplaythrough durationchange emptied encrypted ended error loadeddata loadedmetadata loadstart pause play playing progress ratechange resize seeked seeking stalled suspend timeupdate volumechange waiting".split(" "),Sy=new Set("beforetoggle cancel close invalid load scroll scrollend toggle".split(" ").concat(Un));function $d(t,e){e=(e&4)!==0;for(var a=0;a<t.length;a++){var l=t[a],n=l.event;l=l.listeners;t:{var u=void 0;if(e)for(var r=l.length-1;0<=r;r--){var d=l[r],h=d.instance,S=d.currentTarget;if(d=d.listener,h!==u&&n.isPropagationStopped())break t;u=d,n.currentTarget=S;try{u(n)}catch(M){ku(M)}n.currentTarget=null,u=h}else for(r=0;r<l.length;r++){if(d=l[r],h=d.instance,S=d.currentTarget,d=d.listener,h!==u&&n.isPropagationStopped())break t;u=d,n.currentTarget=S;try{u(n)}catch(M){ku(M)}n.currentTarget=null,u=h}}}}function at(t,e){var a=e[Ii];a===void 0&&(a=e[Ii]=new Set);var l=t+"__bubble";a.has(l)||(Wd(e,t,2,!1),a.add(l))}function Gc(t,e,a){var l=0;e&&(l|=4),Wd(a,t,l,e)}var si="_reactListening"+Math.random().toString(36).slice(2);function Qc(t){if(!t[si]){t[si]=!0,Qf.forEach(function(a){a!=="selectionchange"&&(Sy.has(a)||Gc(a,!1,t),Gc(a,!0,t))});var e=t.nodeType===9?t:t.ownerDocument;e===null||e[si]||(e[si]=!0,Gc("selectionchange",!1,e))}}function Wd(t,e,a,l){switch(ph(e)){case 2:var n=Ky;break;case 8:n=ky;break;default:n=af}a=n.bind(null,e,a,t),n=void 0,!rs||e!=="touchstart"&&e!=="touchmove"&&e!=="wheel"||(n=!0),l?n!==void 0?t.addEventListener(e,a,{capture:!0,passive:n}):t.addEventListener(e,a,!0):n!==void 0?t.addEventListener(e,a,{passive:n}):t.addEventListener(e,a,!1)}function Lc(t,e,a,l,n){var u=l;if((e&1)===0&&(e&2)===0&&l!==null)t:for(;;){if(l===null)return;var r=l.tag;if(r===3||r===4){var d=l.stateNode.containerInfo;if(d===n)break;if(r===4)for(r=l.return;r!==null;){var h=r.tag;if((h===3||h===4)&&r.stateNode.containerInfo===n)return;r=r.return}for(;d!==null;){if(r=ll(d),r===null)return;if(h=r.tag,h===5||h===6||h===26||h===27){l=u=r;continue t}d=d.parentNode}}l=l.return}ar(function(){var S=u,M=cs(a),N=[];t:{var p=wr.get(t);if(p!==void 0){var _=_u,V=t;switch(t){case"keypress":if(Su(a)===0)break t;case"keydown":case"keyup":_=h0;break;case"focusin":V="focus",_=ms;break;case"focusout":V="blur",_=ms;break;case"beforeblur":case"afterblur":_=ms;break;case"click":if(a.button===2)break t;case"auxclick":case"dblclick":case"mousedown":case"mousemove":case"mouseup":case"mouseout":case"mouseover":case"contextmenu":_=ur;break;case"drag":case"dragend":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"dragstart":case"drop":_=e0;break;case"touchcancel":case"touchend":case"touchmove":case"touchstart":_=g0;break;case Ar:case Rr:case Or:_=n0;break;case Nr:_=b0;break;case"scroll":case"scrollend":_=Im;break;case"wheel":_=p0;break;case"copy":case"cut":case"paste":_=i0;break;case"gotpointercapture":case"lostpointercapture":case"pointercancel":case"pointerdown":case"pointermove":case"pointerout":case"pointerover":case"pointerup":_=sr;break;case"toggle":case"beforetoggle":_=x0}var G=(e&4)!==0,ht=!G&&(t==="scroll"||t==="scrollend"),v=G?p!==null?p+"Capture":null:p;G=[];for(var g=S,b;g!==null;){var R=g;if(b=R.stateNode,R=R.tag,R!==5&&R!==26&&R!==27||b===null||v===null||(R=Fl(g,v),R!=null&&G.push(jn(g,R,b))),ht)break;g=g.return}0<G.length&&(p=new _(p,V,null,a,M),N.push({event:p,listeners:G}))}}if((e&7)===0){t:{if(p=t==="mouseover"||t==="pointerover",_=t==="mouseout"||t==="pointerout",p&&a!==ss&&(V=a.relatedTarget||a.fromElement)&&(ll(V)||V[al]))break t;if((_||p)&&(p=M.window===M?M:(p=M.ownerDocument)?p.defaultView||p.parentWindow:window,_?(V=a.relatedTarget||a.toElement,_=S,V=V?ll(V):null,V!==null&&(ht=m(V),G=V.tag,V!==ht||G!==5&&G!==27&&G!==6)&&(V=null)):(_=null,V=S),_!==V)){if(G=ur,R="onMouseLeave",v="onMouseEnter",g="mouse",(t==="pointerout"||t==="pointerover")&&(G=sr,R="onPointerLeave",v="onPointerEnter",g="pointer"),ht=_==null?p:Wl(_),b=V==null?p:Wl(V),p=new G(R,g+"leave",_,a,M),p.target=ht,p.relatedTarget=b,R=null,ll(M)===S&&(G=new G(v,g+"enter",V,a,M),G.target=b,G.relatedTarget=ht,R=G),ht=R,_&&V)e:{for(G=_,v=V,g=0,b=G;b;b=Bl(b))g++;for(b=0,R=v;R;R=Bl(R))b++;for(;0<g-b;)G=Bl(G),g--;for(;0<b-g;)v=Bl(v),b--;for(;g--;){if(G===v||v!==null&&G===v.alternate)break e;G=Bl(G),v=Bl(v)}G=null}else G=null;_!==null&&Fd(N,p,_,G,!1),V!==null&&ht!==null&&Fd(N,ht,V,G,!0)}}t:{if(p=S?Wl(S):window,_=p.nodeName&&p.nodeName.toLowerCase(),_==="select"||_==="input"&&p.type==="file")var C=yr;else if(hr(p))if(gr)C=z0;else{C=w0;var I=N0}else _=p.nodeName,!_||_.toLowerCase()!=="input"||p.type!=="checkbox"&&p.type!=="radio"?S&&is(S.elementType)&&(C=yr):C=D0;if(C&&(C=C(t,S))){mr(N,C,a,M);break t}I&&I(t,p,S),t==="focusout"&&S&&p.type==="number"&&S.memoizedProps.value!=null&&us(p,"number",p.value)}switch(I=S?Wl(S):window,t){case"focusin":(hr(I)||I.contentEditable==="true")&&(hl=I,ps=S,un=null);break;case"focusout":un=ps=hl=null;break;case"mousedown":_s=!0;break;case"contextmenu":case"mouseup":case"dragend":_s=!1,Tr(N,a,M);break;case"selectionchange":if(j0)break;case"keydown":case"keyup":Tr(N,a,M)}var Y;if(gs)t:{switch(t){case"compositionstart":var Q="onCompositionStart";break t;case"compositionend":Q="onCompositionEnd";break t;case"compositionupdate":Q="onCompositionUpdate";break t}Q=void 0}else dl?or(t,a)&&(Q="onCompositionEnd"):t==="keydown"&&a.keyCode===229&&(Q="onCompositionStart");Q&&(cr&&a.locale!=="ko"&&(dl||Q!=="onCompositionStart"?Q==="onCompositionEnd"&&dl&&(Y=lr()):(ca=M,os="value"in ca?ca.value:ca.textContent,dl=!0)),I=ci(S,Q),0<I.length&&(Q=new ir(Q,t,null,a,M),N.push({event:Q,listeners:I}),Y?Q.data=Y:(Y=dr(a),Y!==null&&(Q.data=Y)))),(Y=T0?M0(t,a):A0(t,a))&&(Q=ci(S,"onBeforeInput"),0<Q.length&&(I=new ir("onBeforeInput","beforeinput",null,a,M),N.push({event:I,listeners:Q}),I.data=Y)),gy(N,t,S,a,M)}$d(N,e)})}function jn(t,e,a){return{instance:t,listener:e,currentTarget:a}}function ci(t,e){for(var a=e+"Capture",l=[];t!==null;){var n=t,u=n.stateNode;if(n=n.tag,n!==5&&n!==26&&n!==27||u===null||(n=Fl(t,a),n!=null&&l.unshift(jn(t,n,u)),n=Fl(t,e),n!=null&&l.push(jn(t,n,u))),t.tag===3)return l;t=t.return}return[]}function Bl(t){if(t===null)return null;do t=t.return;while(t&&t.tag!==5&&t.tag!==27);return t||null}function Fd(t,e,a,l,n){for(var u=e._reactName,r=[];a!==null&&a!==l;){var d=a,h=d.alternate,S=d.stateNode;if(d=d.tag,h!==null&&h===l)break;d!==5&&d!==26&&d!==27||S===null||(h=S,n?(S=Fl(a,u),S!=null&&r.unshift(jn(a,S,h))):n||(S=Fl(a,u),S!=null&&r.push(jn(a,S,h)))),a=a.return}r.length!==0&&t.push({event:e,listeners:r})}var py=/\r\n?/g,_y=/\u0000|\uFFFD/g;function Pd(t){return(typeof t=="string"?t:""+t).replace(py,`
`).replace(_y,"")}function Id(t,e){return e=Pd(e),Pd(t)===e}function fi(){}function dt(t,e,a,l,n,u){switch(a){case"children":typeof l=="string"?e==="body"||e==="textarea"&&l===""||fl(t,l):(typeof l=="number"||typeof l=="bigint")&&e!=="body"&&fl(t,""+l);break;case"className":mu(t,"class",l);break;case"tabIndex":mu(t,"tabindex",l);break;case"dir":case"role":case"viewBox":case"width":case"height":mu(t,a,l);break;case"style":tr(t,l,u);break;case"data":if(e!=="object"){mu(t,"data",l);break}case"src":case"href":if(l===""&&(e!=="a"||a!=="href")){t.removeAttribute(a);break}if(l==null||typeof l=="function"||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=vu(""+l),t.setAttribute(a,l);break;case"action":case"formAction":if(typeof l=="function"){t.setAttribute(a,"javascript:throw new Error('A React form was unexpectedly submitted. If you called form.submit() manually, consider using form.requestSubmit() instead. If you\\'re trying to use event.stopPropagation() in a submit event handler, consider also calling event.preventDefault().')");break}else typeof u=="function"&&(a==="formAction"?(e!=="input"&&dt(t,e,"name",n.name,n,null),dt(t,e,"formEncType",n.formEncType,n,null),dt(t,e,"formMethod",n.formMethod,n,null),dt(t,e,"formTarget",n.formTarget,n,null)):(dt(t,e,"encType",n.encType,n,null),dt(t,e,"method",n.method,n,null),dt(t,e,"target",n.target,n,null)));if(l==null||typeof l=="symbol"||typeof l=="boolean"){t.removeAttribute(a);break}l=vu(""+l),t.setAttribute(a,l);break;case"onClick":l!=null&&(t.onclick=fi);break;case"onScroll":l!=null&&at("scroll",t);break;case"onScrollEnd":l!=null&&at("scrollend",t);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(s(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=a}}break;case"multiple":t.multiple=l&&typeof l!="function"&&typeof l!="symbol";break;case"muted":t.muted=l&&typeof l!="function"&&typeof l!="symbol";break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"defaultValue":case"defaultChecked":case"innerHTML":case"ref":break;case"autoFocus":break;case"xlinkHref":if(l==null||typeof l=="function"||typeof l=="boolean"||typeof l=="symbol"){t.removeAttribute("xlink:href");break}a=vu(""+l),t.setAttributeNS("http://www.w3.org/1999/xlink","xlink:href",a);break;case"contentEditable":case"spellCheck":case"draggable":case"value":case"autoReverse":case"externalResourcesRequired":case"focusable":case"preserveAlpha":l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""+l):t.removeAttribute(a);break;case"inert":case"allowFullScreen":case"async":case"autoPlay":case"controls":case"default":case"defer":case"disabled":case"disablePictureInPicture":case"disableRemotePlayback":case"formNoValidate":case"hidden":case"loop":case"noModule":case"noValidate":case"open":case"playsInline":case"readOnly":case"required":case"reversed":case"scoped":case"seamless":case"itemScope":l&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,""):t.removeAttribute(a);break;case"capture":case"download":l===!0?t.setAttribute(a,""):l!==!1&&l!=null&&typeof l!="function"&&typeof l!="symbol"?t.setAttribute(a,l):t.removeAttribute(a);break;case"cols":case"rows":case"size":case"span":l!=null&&typeof l!="function"&&typeof l!="symbol"&&!isNaN(l)&&1<=l?t.setAttribute(a,l):t.removeAttribute(a);break;case"rowSpan":case"start":l==null||typeof l=="function"||typeof l=="symbol"||isNaN(l)?t.removeAttribute(a):t.setAttribute(a,l);break;case"popover":at("beforetoggle",t),at("toggle",t),hu(t,"popover",l);break;case"xlinkActuate":Ge(t,"http://www.w3.org/1999/xlink","xlink:actuate",l);break;case"xlinkArcrole":Ge(t,"http://www.w3.org/1999/xlink","xlink:arcrole",l);break;case"xlinkRole":Ge(t,"http://www.w3.org/1999/xlink","xlink:role",l);break;case"xlinkShow":Ge(t,"http://www.w3.org/1999/xlink","xlink:show",l);break;case"xlinkTitle":Ge(t,"http://www.w3.org/1999/xlink","xlink:title",l);break;case"xlinkType":Ge(t,"http://www.w3.org/1999/xlink","xlink:type",l);break;case"xmlBase":Ge(t,"http://www.w3.org/XML/1998/namespace","xml:base",l);break;case"xmlLang":Ge(t,"http://www.w3.org/XML/1998/namespace","xml:lang",l);break;case"xmlSpace":Ge(t,"http://www.w3.org/XML/1998/namespace","xml:space",l);break;case"is":hu(t,"is",l);break;case"innerText":case"textContent":break;default:(!(2<a.length)||a[0]!=="o"&&a[0]!=="O"||a[1]!=="n"&&a[1]!=="N")&&(a=Fm.get(a)||a,hu(t,a,l))}}function Zc(t,e,a,l,n,u){switch(a){case"style":tr(t,l,u);break;case"dangerouslySetInnerHTML":if(l!=null){if(typeof l!="object"||!("__html"in l))throw Error(s(61));if(a=l.__html,a!=null){if(n.children!=null)throw Error(s(60));t.innerHTML=a}}break;case"children":typeof l=="string"?fl(t,l):(typeof l=="number"||typeof l=="bigint")&&fl(t,""+l);break;case"onScroll":l!=null&&at("scroll",t);break;case"onScrollEnd":l!=null&&at("scrollend",t);break;case"onClick":l!=null&&(t.onclick=fi);break;case"suppressContentEditableWarning":case"suppressHydrationWarning":case"innerHTML":case"ref":break;case"innerText":case"textContent":break;default:if(!Lf.hasOwnProperty(a))t:{if(a[0]==="o"&&a[1]==="n"&&(n=a.endsWith("Capture"),e=a.slice(2,n?a.length-7:void 0),u=t[It]||null,u=u!=null?u[a]:null,typeof u=="function"&&t.removeEventListener(e,u,n),typeof l=="function")){typeof u!="function"&&u!==null&&(a in t?t[a]=null:t.hasAttribute(a)&&t.removeAttribute(a)),t.addEventListener(e,l,n);break t}a in t?t[a]=l:l===!0?t.setAttribute(a,""):hu(t,a,l)}}}function Qt(t,e,a){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"img":at("error",t),at("load",t);var l=!1,n=!1,u;for(u in a)if(a.hasOwnProperty(u)){var r=a[u];if(r!=null)switch(u){case"src":l=!0;break;case"srcSet":n=!0;break;case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:dt(t,e,u,r,a,null)}}n&&dt(t,e,"srcSet",a.srcSet,a,null),l&&dt(t,e,"src",a.src,a,null);return;case"input":at("invalid",t);var d=u=r=n=null,h=null,S=null;for(l in a)if(a.hasOwnProperty(l)){var M=a[l];if(M!=null)switch(l){case"name":n=M;break;case"type":r=M;break;case"checked":h=M;break;case"defaultChecked":S=M;break;case"value":u=M;break;case"defaultValue":d=M;break;case"children":case"dangerouslySetInnerHTML":if(M!=null)throw Error(s(137,e));break;default:dt(t,e,l,M,a,null)}}Wf(t,u,d,h,S,r,n,!1),yu(t);return;case"select":at("invalid",t),l=r=u=null;for(n in a)if(a.hasOwnProperty(n)&&(d=a[n],d!=null))switch(n){case"value":u=d;break;case"defaultValue":r=d;break;case"multiple":l=d;default:dt(t,e,n,d,a,null)}e=u,a=r,t.multiple=!!l,e!=null?cl(t,!!l,e,!1):a!=null&&cl(t,!!l,a,!0);return;case"textarea":at("invalid",t),u=n=l=null;for(r in a)if(a.hasOwnProperty(r)&&(d=a[r],d!=null))switch(r){case"value":l=d;break;case"defaultValue":n=d;break;case"children":u=d;break;case"dangerouslySetInnerHTML":if(d!=null)throw Error(s(91));break;default:dt(t,e,r,d,a,null)}Pf(t,l,n,u),yu(t);return;case"option":for(h in a)if(a.hasOwnProperty(h)&&(l=a[h],l!=null))switch(h){case"selected":t.selected=l&&typeof l!="function"&&typeof l!="symbol";break;default:dt(t,e,h,l,a,null)}return;case"dialog":at("beforetoggle",t),at("toggle",t),at("cancel",t),at("close",t);break;case"iframe":case"object":at("load",t);break;case"video":case"audio":for(l=0;l<Un.length;l++)at(Un[l],t);break;case"image":at("error",t),at("load",t);break;case"details":at("toggle",t);break;case"embed":case"source":case"link":at("error",t),at("load",t);case"area":case"base":case"br":case"col":case"hr":case"keygen":case"meta":case"param":case"track":case"wbr":case"menuitem":for(S in a)if(a.hasOwnProperty(S)&&(l=a[S],l!=null))switch(S){case"children":case"dangerouslySetInnerHTML":throw Error(s(137,e));default:dt(t,e,S,l,a,null)}return;default:if(is(e)){for(M in a)a.hasOwnProperty(M)&&(l=a[M],l!==void 0&&Zc(t,e,M,l,a,void 0));return}}for(d in a)a.hasOwnProperty(d)&&(l=a[d],l!=null&&dt(t,e,d,l,a,null))}function xy(t,e,a,l){switch(e){case"div":case"span":case"svg":case"path":case"a":case"g":case"p":case"li":break;case"input":var n=null,u=null,r=null,d=null,h=null,S=null,M=null;for(_ in a){var N=a[_];if(a.hasOwnProperty(_)&&N!=null)switch(_){case"checked":break;case"value":break;case"defaultValue":h=N;default:l.hasOwnProperty(_)||dt(t,e,_,null,l,N)}}for(var p in l){var _=l[p];if(N=a[p],l.hasOwnProperty(p)&&(_!=null||N!=null))switch(p){case"type":u=_;break;case"name":n=_;break;case"checked":S=_;break;case"defaultChecked":M=_;break;case"value":r=_;break;case"defaultValue":d=_;break;case"children":case"dangerouslySetInnerHTML":if(_!=null)throw Error(s(137,e));break;default:_!==N&&dt(t,e,p,_,l,N)}}ns(t,r,d,h,S,M,u,n);return;case"select":_=r=d=p=null;for(u in a)if(h=a[u],a.hasOwnProperty(u)&&h!=null)switch(u){case"value":break;case"multiple":_=h;default:l.hasOwnProperty(u)||dt(t,e,u,null,l,h)}for(n in l)if(u=l[n],h=a[n],l.hasOwnProperty(n)&&(u!=null||h!=null))switch(n){case"value":p=u;break;case"defaultValue":d=u;break;case"multiple":r=u;default:u!==h&&dt(t,e,n,u,l,h)}e=d,a=r,l=_,p!=null?cl(t,!!a,p,!1):!!l!=!!a&&(e!=null?cl(t,!!a,e,!0):cl(t,!!a,a?[]:"",!1));return;case"textarea":_=p=null;for(d in a)if(n=a[d],a.hasOwnProperty(d)&&n!=null&&!l.hasOwnProperty(d))switch(d){case"value":break;case"children":break;default:dt(t,e,d,null,l,n)}for(r in l)if(n=l[r],u=a[r],l.hasOwnProperty(r)&&(n!=null||u!=null))switch(r){case"value":p=n;break;case"defaultValue":_=n;break;case"children":break;case"dangerouslySetInnerHTML":if(n!=null)throw Error(s(91));break;default:n!==u&&dt(t,e,r,n,l,u)}Ff(t,p,_);return;case"option":for(var V in a)if(p=a[V],a.hasOwnProperty(V)&&p!=null&&!l.hasOwnProperty(V))switch(V){case"selected":t.selected=!1;break;default:dt(t,e,V,null,l,p)}for(h in l)if(p=l[h],_=a[h],l.hasOwnProperty(h)&&p!==_&&(p!=null||_!=null))switch(h){case"selected":t.selected=p&&typeof p!="function"&&typeof p!="symbol";break;default:dt(t,e,h,p,l,_)}return;case"img":case"link":case"area":case"base":case"br":case"col":case"embed":case"hr":case"keygen":case"meta":case"param":case"source":case"track":case"wbr":case"menuitem":for(var G in a)p=a[G],a.hasOwnProperty(G)&&p!=null&&!l.hasOwnProperty(G)&&dt(t,e,G,null,l,p);for(S in l)if(p=l[S],_=a[S],l.hasOwnProperty(S)&&p!==_&&(p!=null||_!=null))switch(S){case"children":case"dangerouslySetInnerHTML":if(p!=null)throw Error(s(137,e));break;default:dt(t,e,S,p,l,_)}return;default:if(is(e)){for(var ht in a)p=a[ht],a.hasOwnProperty(ht)&&p!==void 0&&!l.hasOwnProperty(ht)&&Zc(t,e,ht,void 0,l,p);for(M in l)p=l[M],_=a[M],!l.hasOwnProperty(M)||p===_||p===void 0&&_===void 0||Zc(t,e,M,p,l,_);return}}for(var v in a)p=a[v],a.hasOwnProperty(v)&&p!=null&&!l.hasOwnProperty(v)&&dt(t,e,v,null,l,p);for(N in l)p=l[N],_=a[N],!l.hasOwnProperty(N)||p===_||p==null&&_==null||dt(t,e,N,p,l,_)}var Vc=null,Kc=null;function ri(t){return t.nodeType===9?t:t.ownerDocument}function th(t){switch(t){case"http://www.w3.org/2000/svg":return 1;case"http://www.w3.org/1998/Math/MathML":return 2;default:return 0}}function eh(t,e){if(t===0)switch(e){case"svg":return 1;case"math":return 2;default:return 0}return t===1&&e==="foreignObject"?0:t}function kc(t,e){return t==="textarea"||t==="noscript"||typeof e.children=="string"||typeof e.children=="number"||typeof e.children=="bigint"||typeof e.dangerouslySetInnerHTML=="object"&&e.dangerouslySetInnerHTML!==null&&e.dangerouslySetInnerHTML.__html!=null}var Jc=null;function Ey(){var t=window.event;return t&&t.type==="popstate"?t===Jc?!1:(Jc=t,!0):(Jc=null,!1)}var ah=typeof setTimeout=="function"?setTimeout:void 0,Ty=typeof clearTimeout=="function"?clearTimeout:void 0,lh=typeof Promise=="function"?Promise:void 0,My=typeof queueMicrotask=="function"?queueMicrotask:typeof lh<"u"?function(t){return lh.resolve(null).then(t).catch(Ay)}:ah;function Ay(t){setTimeout(function(){throw t})}function Ta(t){return t==="head"}function nh(t,e){var a=e,l=0,n=0;do{var u=a.nextSibling;if(t.removeChild(a),u&&u.nodeType===8)if(a=u.data,a==="/$"){if(0<l&&8>l){a=l;var r=t.ownerDocument;if(a&1&&qn(r.documentElement),a&2&&qn(r.body),a&4)for(a=r.head,qn(a),r=a.firstChild;r;){var d=r.nextSibling,h=r.nodeName;r[$l]||h==="SCRIPT"||h==="STYLE"||h==="LINK"&&r.rel.toLowerCase()==="stylesheet"||a.removeChild(r),r=d}}if(n===0){t.removeChild(u),Ln(e);return}n--}else a==="$"||a==="$?"||a==="$!"?n++:l=a.charCodeAt(0)-48;else l=0;a=u}while(a);Ln(e)}function $c(t){var e=t.firstChild;for(e&&e.nodeType===10&&(e=e.nextSibling);e;){var a=e;switch(e=e.nextSibling,a.nodeName){case"HTML":case"HEAD":case"BODY":$c(a),ts(a);continue;case"SCRIPT":case"STYLE":continue;case"LINK":if(a.rel.toLowerCase()==="stylesheet")continue}t.removeChild(a)}}function Ry(t,e,a,l){for(;t.nodeType===1;){var n=a;if(t.nodeName.toLowerCase()!==e.toLowerCase()){if(!l&&(t.nodeName!=="INPUT"||t.type!=="hidden"))break}else if(l){if(!t[$l])switch(e){case"meta":if(!t.hasAttribute("itemprop"))break;return t;case"link":if(u=t.getAttribute("rel"),u==="stylesheet"&&t.hasAttribute("data-precedence"))break;if(u!==n.rel||t.getAttribute("href")!==(n.href==null||n.href===""?null:n.href)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin)||t.getAttribute("title")!==(n.title==null?null:n.title))break;return t;case"style":if(t.hasAttribute("data-precedence"))break;return t;case"script":if(u=t.getAttribute("src"),(u!==(n.src==null?null:n.src)||t.getAttribute("type")!==(n.type==null?null:n.type)||t.getAttribute("crossorigin")!==(n.crossOrigin==null?null:n.crossOrigin))&&u&&t.hasAttribute("async")&&!t.hasAttribute("itemprop"))break;return t;default:return t}}else if(e==="input"&&t.type==="hidden"){var u=n.name==null?null:""+n.name;if(n.type==="hidden"&&t.getAttribute("name")===u)return t}else return t;if(t=we(t.nextSibling),t===null)break}return null}function Oy(t,e,a){if(e==="")return null;for(;t.nodeType!==3;)if((t.nodeType!==1||t.nodeName!=="INPUT"||t.type!=="hidden")&&!a||(t=we(t.nextSibling),t===null))return null;return t}function Wc(t){return t.data==="$!"||t.data==="$?"&&t.ownerDocument.readyState==="complete"}function Ny(t,e){var a=t.ownerDocument;if(t.data!=="$?"||a.readyState==="complete")e();else{var l=function(){e(),a.removeEventListener("DOMContentLoaded",l)};a.addEventListener("DOMContentLoaded",l),t._reactRetry=l}}function we(t){for(;t!=null;t=t.nextSibling){var e=t.nodeType;if(e===1||e===3)break;if(e===8){if(e=t.data,e==="$"||e==="$!"||e==="$?"||e==="F!"||e==="F")break;if(e==="/$")return null}}return t}var Fc=null;function uh(t){t=t.previousSibling;for(var e=0;t;){if(t.nodeType===8){var a=t.data;if(a==="$"||a==="$!"||a==="$?"){if(e===0)return t;e--}else a==="/$"&&e++}t=t.previousSibling}return null}function ih(t,e,a){switch(e=ri(a),t){case"html":if(t=e.documentElement,!t)throw Error(s(452));return t;case"head":if(t=e.head,!t)throw Error(s(453));return t;case"body":if(t=e.body,!t)throw Error(s(454));return t;default:throw Error(s(451))}}function qn(t){for(var e=t.attributes;e.length;)t.removeAttributeNode(e[0]);ts(t)}var Me=new Map,sh=new Set;function oi(t){return typeof t.getRootNode=="function"?t.getRootNode():t.nodeType===9?t:t.ownerDocument}var ea=q.d;q.d={f:wy,r:Dy,D:zy,C:Uy,L:jy,m:qy,X:By,S:Hy,M:Cy};function wy(){var t=ea.f(),e=ai();return t||e}function Dy(t){var e=nl(t);e!==null&&e.tag===5&&e.type==="form"?Ro(e):ea.r(t)}var Cl=typeof document>"u"?null:document;function ch(t,e,a){var l=Cl;if(l&&typeof e=="string"&&e){var n=be(e);n='link[rel="'+t+'"][href="'+n+'"]',typeof a=="string"&&(n+='[crossorigin="'+a+'"]'),sh.has(n)||(sh.add(n),t={rel:t,crossOrigin:a,href:e},l.querySelector(n)===null&&(e=l.createElement("link"),Qt(e,"link",t),Ht(e),l.head.appendChild(e)))}}function zy(t){ea.D(t),ch("dns-prefetch",t,null)}function Uy(t,e){ea.C(t,e),ch("preconnect",t,e)}function jy(t,e,a){ea.L(t,e,a);var l=Cl;if(l&&t&&e){var n='link[rel="preload"][as="'+be(e)+'"]';e==="image"&&a&&a.imageSrcSet?(n+='[imagesrcset="'+be(a.imageSrcSet)+'"]',typeof a.imageSizes=="string"&&(n+='[imagesizes="'+be(a.imageSizes)+'"]')):n+='[href="'+be(t)+'"]';var u=n;switch(e){case"style":u=Yl(t);break;case"script":u=Xl(t)}Me.has(u)||(t=U({rel:"preload",href:e==="image"&&a&&a.imageSrcSet?void 0:t,as:e},a),Me.set(u,t),l.querySelector(n)!==null||e==="style"&&l.querySelector(Hn(u))||e==="script"&&l.querySelector(Bn(u))||(e=l.createElement("link"),Qt(e,"link",t),Ht(e),l.head.appendChild(e)))}}function qy(t,e){ea.m(t,e);var a=Cl;if(a&&t){var l=e&&typeof e.as=="string"?e.as:"script",n='link[rel="modulepreload"][as="'+be(l)+'"][href="'+be(t)+'"]',u=n;switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":u=Xl(t)}if(!Me.has(u)&&(t=U({rel:"modulepreload",href:t},e),Me.set(u,t),a.querySelector(n)===null)){switch(l){case"audioworklet":case"paintworklet":case"serviceworker":case"sharedworker":case"worker":case"script":if(a.querySelector(Bn(u)))return}l=a.createElement("link"),Qt(l,"link",t),Ht(l),a.head.appendChild(l)}}}function Hy(t,e,a){ea.S(t,e,a);var l=Cl;if(l&&t){var n=ul(l).hoistableStyles,u=Yl(t);e=e||"default";var r=n.get(u);if(!r){var d={loading:0,preload:null};if(r=l.querySelector(Hn(u)))d.loading=5;else{t=U({rel:"stylesheet",href:t,"data-precedence":e},a),(a=Me.get(u))&&Pc(t,a);var h=r=l.createElement("link");Ht(h),Qt(h,"link",t),h._p=new Promise(function(S,M){h.onload=S,h.onerror=M}),h.addEventListener("load",function(){d.loading|=1}),h.addEventListener("error",function(){d.loading|=2}),d.loading|=4,di(r,e,l)}r={type:"stylesheet",instance:r,count:1,state:d},n.set(u,r)}}}function By(t,e){ea.X(t,e);var a=Cl;if(a&&t){var l=ul(a).hoistableScripts,n=Xl(t),u=l.get(n);u||(u=a.querySelector(Bn(n)),u||(t=U({src:t,async:!0},e),(e=Me.get(n))&&Ic(t,e),u=a.createElement("script"),Ht(u),Qt(u,"link",t),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function Cy(t,e){ea.M(t,e);var a=Cl;if(a&&t){var l=ul(a).hoistableScripts,n=Xl(t),u=l.get(n);u||(u=a.querySelector(Bn(n)),u||(t=U({src:t,async:!0,type:"module"},e),(e=Me.get(n))&&Ic(t,e),u=a.createElement("script"),Ht(u),Qt(u,"link",t),a.head.appendChild(u)),u={type:"script",instance:u,count:1,state:null},l.set(n,u))}}function fh(t,e,a,l){var n=(n=J.current)?oi(n):null;if(!n)throw Error(s(446));switch(t){case"meta":case"title":return null;case"style":return typeof a.precedence=="string"&&typeof a.href=="string"?(e=Yl(a.href),a=ul(n).hoistableStyles,l=a.get(e),l||(l={type:"style",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};case"link":if(a.rel==="stylesheet"&&typeof a.href=="string"&&typeof a.precedence=="string"){t=Yl(a.href);var u=ul(n).hoistableStyles,r=u.get(t);if(r||(n=n.ownerDocument||n,r={type:"stylesheet",instance:null,count:0,state:{loading:0,preload:null}},u.set(t,r),(u=n.querySelector(Hn(t)))&&!u._p&&(r.instance=u,r.state.loading=5),Me.has(t)||(a={rel:"preload",as:"style",href:a.href,crossOrigin:a.crossOrigin,integrity:a.integrity,media:a.media,hrefLang:a.hrefLang,referrerPolicy:a.referrerPolicy},Me.set(t,a),u||Yy(n,t,a,r.state))),e&&l===null)throw Error(s(528,""));return r}if(e&&l!==null)throw Error(s(529,""));return null;case"script":return e=a.async,a=a.src,typeof a=="string"&&e&&typeof e!="function"&&typeof e!="symbol"?(e=Xl(a),a=ul(n).hoistableScripts,l=a.get(e),l||(l={type:"script",instance:null,count:0,state:null},a.set(e,l)),l):{type:"void",instance:null,count:0,state:null};default:throw Error(s(444,t))}}function Yl(t){return'href="'+be(t)+'"'}function Hn(t){return'link[rel="stylesheet"]['+t+"]"}function rh(t){return U({},t,{"data-precedence":t.precedence,precedence:null})}function Yy(t,e,a,l){t.querySelector('link[rel="preload"][as="style"]['+e+"]")?l.loading=1:(e=t.createElement("link"),l.preload=e,e.addEventListener("load",function(){return l.loading|=1}),e.addEventListener("error",function(){return l.loading|=2}),Qt(e,"link",a),Ht(e),t.head.appendChild(e))}function Xl(t){return'[src="'+be(t)+'"]'}function Bn(t){return"script[async]"+t}function oh(t,e,a){if(e.count++,e.instance===null)switch(e.type){case"style":var l=t.querySelector('style[data-href~="'+be(a.href)+'"]');if(l)return e.instance=l,Ht(l),l;var n=U({},a,{"data-href":a.href,"data-precedence":a.precedence,href:null,precedence:null});return l=(t.ownerDocument||t).createElement("style"),Ht(l),Qt(l,"style",n),di(l,a.precedence,t),e.instance=l;case"stylesheet":n=Yl(a.href);var u=t.querySelector(Hn(n));if(u)return e.state.loading|=4,e.instance=u,Ht(u),u;l=rh(a),(n=Me.get(n))&&Pc(l,n),u=(t.ownerDocument||t).createElement("link"),Ht(u);var r=u;return r._p=new Promise(function(d,h){r.onload=d,r.onerror=h}),Qt(u,"link",l),e.state.loading|=4,di(u,a.precedence,t),e.instance=u;case"script":return u=Xl(a.src),(n=t.querySelector(Bn(u)))?(e.instance=n,Ht(n),n):(l=a,(n=Me.get(u))&&(l=U({},a),Ic(l,n)),t=t.ownerDocument||t,n=t.createElement("script"),Ht(n),Qt(n,"link",l),t.head.appendChild(n),e.instance=n);case"void":return null;default:throw Error(s(443,e.type))}else e.type==="stylesheet"&&(e.state.loading&4)===0&&(l=e.instance,e.state.loading|=4,di(l,a.precedence,t));return e.instance}function di(t,e,a){for(var l=a.querySelectorAll('link[rel="stylesheet"][data-precedence],style[data-precedence]'),n=l.length?l[l.length-1]:null,u=n,r=0;r<l.length;r++){var d=l[r];if(d.dataset.precedence===e)u=d;else if(u!==n)break}u?u.parentNode.insertBefore(t,u.nextSibling):(e=a.nodeType===9?a.head:a,e.insertBefore(t,e.firstChild))}function Pc(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.title==null&&(t.title=e.title)}function Ic(t,e){t.crossOrigin==null&&(t.crossOrigin=e.crossOrigin),t.referrerPolicy==null&&(t.referrerPolicy=e.referrerPolicy),t.integrity==null&&(t.integrity=e.integrity)}var hi=null;function dh(t,e,a){if(hi===null){var l=new Map,n=hi=new Map;n.set(a,l)}else n=hi,l=n.get(a),l||(l=new Map,n.set(a,l));if(l.has(t))return l;for(l.set(t,null),a=a.getElementsByTagName(t),n=0;n<a.length;n++){var u=a[n];if(!(u[$l]||u[Vt]||t==="link"&&u.getAttribute("rel")==="stylesheet")&&u.namespaceURI!=="http://www.w3.org/2000/svg"){var r=u.getAttribute(e)||"";r=t+r;var d=l.get(r);d?d.push(u):l.set(r,[u])}}return l}function hh(t,e,a){t=t.ownerDocument||t,t.head.insertBefore(a,e==="title"?t.querySelector("head > title"):null)}function Xy(t,e,a){if(a===1||e.itemProp!=null)return!1;switch(t){case"meta":case"title":return!0;case"style":if(typeof e.precedence!="string"||typeof e.href!="string"||e.href==="")break;return!0;case"link":if(typeof e.rel!="string"||typeof e.href!="string"||e.href===""||e.onLoad||e.onError)break;switch(e.rel){case"stylesheet":return t=e.disabled,typeof e.precedence=="string"&&t==null;default:return!0}case"script":if(e.async&&typeof e.async!="function"&&typeof e.async!="symbol"&&!e.onLoad&&!e.onError&&e.src&&typeof e.src=="string")return!0}return!1}function mh(t){return!(t.type==="stylesheet"&&(t.state.loading&3)===0)}var Cn=null;function Gy(){}function Qy(t,e,a){if(Cn===null)throw Error(s(475));var l=Cn;if(e.type==="stylesheet"&&(typeof a.media!="string"||matchMedia(a.media).matches!==!1)&&(e.state.loading&4)===0){if(e.instance===null){var n=Yl(a.href),u=t.querySelector(Hn(n));if(u){t=u._p,t!==null&&typeof t=="object"&&typeof t.then=="function"&&(l.count++,l=mi.bind(l),t.then(l,l)),e.state.loading|=4,e.instance=u,Ht(u);return}u=t.ownerDocument||t,a=rh(a),(n=Me.get(n))&&Pc(a,n),u=u.createElement("link"),Ht(u);var r=u;r._p=new Promise(function(d,h){r.onload=d,r.onerror=h}),Qt(u,"link",a),e.instance=u}l.stylesheets===null&&(l.stylesheets=new Map),l.stylesheets.set(e,t),(t=e.state.preload)&&(e.state.loading&3)===0&&(l.count++,e=mi.bind(l),t.addEventListener("load",e),t.addEventListener("error",e))}}function Ly(){if(Cn===null)throw Error(s(475));var t=Cn;return t.stylesheets&&t.count===0&&tf(t,t.stylesheets),0<t.count?function(e){var a=setTimeout(function(){if(t.stylesheets&&tf(t,t.stylesheets),t.unsuspend){var l=t.unsuspend;t.unsuspend=null,l()}},6e4);return t.unsuspend=e,function(){t.unsuspend=null,clearTimeout(a)}}:null}function mi(){if(this.count--,this.count===0){if(this.stylesheets)tf(this,this.stylesheets);else if(this.unsuspend){var t=this.unsuspend;this.unsuspend=null,t()}}}var yi=null;function tf(t,e){t.stylesheets=null,t.unsuspend!==null&&(t.count++,yi=new Map,e.forEach(Zy,t),yi=null,mi.call(t))}function Zy(t,e){if(!(e.state.loading&4)){var a=yi.get(t);if(a)var l=a.get(null);else{a=new Map,yi.set(t,a);for(var n=t.querySelectorAll("link[data-precedence],style[data-precedence]"),u=0;u<n.length;u++){var r=n[u];(r.nodeName==="LINK"||r.getAttribute("media")!=="not all")&&(a.set(r.dataset.precedence,r),l=r)}l&&a.set(null,l)}n=e.instance,r=n.getAttribute("data-precedence"),u=a.get(r)||l,u===l&&a.set(null,n),a.set(r,n),this.count++,l=mi.bind(this),n.addEventListener("load",l),n.addEventListener("error",l),u?u.parentNode.insertBefore(n,u.nextSibling):(t=t.nodeType===9?t.head:t,t.insertBefore(n,t.firstChild)),e.state.loading|=4}}var Yn={$$typeof:ut,Provider:null,Consumer:null,_currentValue:Z,_currentValue2:Z,_threadCount:0};function Vy(t,e,a,l,n,u,r,d){this.tag=1,this.containerInfo=t,this.pingCache=this.current=this.pendingChildren=null,this.timeoutHandle=-1,this.callbackNode=this.next=this.pendingContext=this.context=this.cancelPendingCommit=null,this.callbackPriority=0,this.expirationTimes=Wi(-1),this.entangledLanes=this.shellSuspendCounter=this.errorRecoveryDisabledLanes=this.expiredLanes=this.warmLanes=this.pingedLanes=this.suspendedLanes=this.pendingLanes=0,this.entanglements=Wi(0),this.hiddenUpdates=Wi(null),this.identifierPrefix=l,this.onUncaughtError=n,this.onCaughtError=u,this.onRecoverableError=r,this.pooledCache=null,this.pooledCacheLanes=0,this.formState=d,this.incompleteTransitions=new Map}function yh(t,e,a,l,n,u,r,d,h,S,M,N){return t=new Vy(t,e,a,r,d,h,S,N),e=1,u===!0&&(e|=24),u=fe(3,null,null,e),t.current=u,u.stateNode=t,e=qs(),e.refCount++,t.pooledCache=e,e.refCount++,u.memoizedState={element:l,isDehydrated:a,cache:e},Ys(u),t}function gh(t){return t?(t=vl,t):vl}function vh(t,e,a,l,n,u){n=gh(n),l.context===null?l.context=n:l.pendingContext=n,l=oa(e),l.payload={element:a},u=u===void 0?null:u,u!==null&&(l.callback=u),a=da(t,l,e),a!==null&&(me(a,t,e),yn(a,t,e))}function bh(t,e){if(t=t.memoizedState,t!==null&&t.dehydrated!==null){var a=t.retryLane;t.retryLane=a!==0&&a<e?a:e}}function ef(t,e){bh(t,e),(t=t.alternate)&&bh(t,e)}function Sh(t){if(t.tag===13){var e=gl(t,67108864);e!==null&&me(e,t,67108864),ef(t,67108864)}}var gi=!0;function Ky(t,e,a,l){var n=A.T;A.T=null;var u=q.p;try{q.p=2,af(t,e,a,l)}finally{q.p=u,A.T=n}}function ky(t,e,a,l){var n=A.T;A.T=null;var u=q.p;try{q.p=8,af(t,e,a,l)}finally{q.p=u,A.T=n}}function af(t,e,a,l){if(gi){var n=lf(l);if(n===null)Lc(t,e,l,vi,a),_h(t,l);else if($y(n,t,e,a,l))l.stopPropagation();else if(_h(t,l),e&4&&-1<Jy.indexOf(t)){for(;n!==null;){var u=nl(n);if(u!==null)switch(u.tag){case 3:if(u=u.stateNode,u.current.memoizedState.isDehydrated){var r=ja(u.pendingLanes);if(r!==0){var d=u;for(d.pendingLanes|=2,d.entangledLanes|=2;r;){var h=1<<31-se(r);d.entanglements[1]|=h,r&=~h}Ce(u),(ft&6)===0&&(ti=Ue()+500,zn(0))}}break;case 13:d=gl(u,2),d!==null&&me(d,u,2),ai(),ef(u,2)}if(u=lf(l),u===null&&Lc(t,e,l,vi,a),u===n)break;n=u}n!==null&&l.stopPropagation()}else Lc(t,e,l,null,a)}}function lf(t){return t=cs(t),nf(t)}var vi=null;function nf(t){if(vi=null,t=ll(t),t!==null){var e=m(t);if(e===null)t=null;else{var a=e.tag;if(a===13){if(t=E(e),t!==null)return t;t=null}else if(a===3){if(e.stateNode.current.memoizedState.isDehydrated)return e.tag===3?e.stateNode.containerInfo:null;t=null}else e!==t&&(t=null)}}return vi=t,null}function ph(t){switch(t){case"beforetoggle":case"cancel":case"click":case"close":case"contextmenu":case"copy":case"cut":case"auxclick":case"dblclick":case"dragend":case"dragstart":case"drop":case"focusin":case"focusout":case"input":case"invalid":case"keydown":case"keypress":case"keyup":case"mousedown":case"mouseup":case"paste":case"pause":case"play":case"pointercancel":case"pointerdown":case"pointerup":case"ratechange":case"reset":case"resize":case"seeked":case"submit":case"toggle":case"touchcancel":case"touchend":case"touchstart":case"volumechange":case"change":case"selectionchange":case"textInput":case"compositionstart":case"compositionend":case"compositionupdate":case"beforeblur":case"afterblur":case"beforeinput":case"blur":case"fullscreenchange":case"focus":case"hashchange":case"popstate":case"select":case"selectstart":return 2;case"drag":case"dragenter":case"dragexit":case"dragleave":case"dragover":case"mousemove":case"mouseout":case"mouseover":case"pointermove":case"pointerout":case"pointerover":case"scroll":case"touchmove":case"wheel":case"mouseenter":case"mouseleave":case"pointerenter":case"pointerleave":return 8;case"message":switch(Um()){case Uf:return 2;case jf:return 8;case fu:case jm:return 32;case qf:return 268435456;default:return 32}default:return 32}}var uf=!1,Ma=null,Aa=null,Ra=null,Xn=new Map,Gn=new Map,Oa=[],Jy="mousedown mouseup touchcancel touchend touchstart auxclick dblclick pointercancel pointerdown pointerup dragend dragstart drop compositionend compositionstart keydown keypress keyup input textInput copy cut paste click change contextmenu reset".split(" ");function _h(t,e){switch(t){case"focusin":case"focusout":Ma=null;break;case"dragenter":case"dragleave":Aa=null;break;case"mouseover":case"mouseout":Ra=null;break;case"pointerover":case"pointerout":Xn.delete(e.pointerId);break;case"gotpointercapture":case"lostpointercapture":Gn.delete(e.pointerId)}}function Qn(t,e,a,l,n,u){return t===null||t.nativeEvent!==u?(t={blockedOn:e,domEventName:a,eventSystemFlags:l,nativeEvent:u,targetContainers:[n]},e!==null&&(e=nl(e),e!==null&&Sh(e)),t):(t.eventSystemFlags|=l,e=t.targetContainers,n!==null&&e.indexOf(n)===-1&&e.push(n),t)}function $y(t,e,a,l,n){switch(e){case"focusin":return Ma=Qn(Ma,t,e,a,l,n),!0;case"dragenter":return Aa=Qn(Aa,t,e,a,l,n),!0;case"mouseover":return Ra=Qn(Ra,t,e,a,l,n),!0;case"pointerover":var u=n.pointerId;return Xn.set(u,Qn(Xn.get(u)||null,t,e,a,l,n)),!0;case"gotpointercapture":return u=n.pointerId,Gn.set(u,Qn(Gn.get(u)||null,t,e,a,l,n)),!0}return!1}function xh(t){var e=ll(t.target);if(e!==null){var a=m(e);if(a!==null){if(e=a.tag,e===13){if(e=E(a),e!==null){t.blockedOn=e,Qm(t.priority,function(){if(a.tag===13){var l=he();l=Fi(l);var n=gl(a,l);n!==null&&me(n,a,l),ef(a,l)}});return}}else if(e===3&&a.stateNode.current.memoizedState.isDehydrated){t.blockedOn=a.tag===3?a.stateNode.containerInfo:null;return}}}t.blockedOn=null}function bi(t){if(t.blockedOn!==null)return!1;for(var e=t.targetContainers;0<e.length;){var a=lf(t.nativeEvent);if(a===null){a=t.nativeEvent;var l=new a.constructor(a.type,a);ss=l,a.target.dispatchEvent(l),ss=null}else return e=nl(a),e!==null&&Sh(e),t.blockedOn=a,!1;e.shift()}return!0}function Eh(t,e,a){bi(t)&&a.delete(e)}function Wy(){uf=!1,Ma!==null&&bi(Ma)&&(Ma=null),Aa!==null&&bi(Aa)&&(Aa=null),Ra!==null&&bi(Ra)&&(Ra=null),Xn.forEach(Eh),Gn.forEach(Eh)}function Si(t,e){t.blockedOn===e&&(t.blockedOn=null,uf||(uf=!0,f.unstable_scheduleCallback(f.unstable_NormalPriority,Wy)))}var pi=null;function Th(t){pi!==t&&(pi=t,f.unstable_scheduleCallback(f.unstable_NormalPriority,function(){pi===t&&(pi=null);for(var e=0;e<t.length;e+=3){var a=t[e],l=t[e+1],n=t[e+2];if(typeof l!="function"){if(nf(l||a)===null)continue;break}var u=nl(a);u!==null&&(t.splice(e,3),e-=3,nc(u,{pending:!0,data:n,method:a.method,action:l},l,n))}}))}function Ln(t){function e(h){return Si(h,t)}Ma!==null&&Si(Ma,t),Aa!==null&&Si(Aa,t),Ra!==null&&Si(Ra,t),Xn.forEach(e),Gn.forEach(e);for(var a=0;a<Oa.length;a++){var l=Oa[a];l.blockedOn===t&&(l.blockedOn=null)}for(;0<Oa.length&&(a=Oa[0],a.blockedOn===null);)xh(a),a.blockedOn===null&&Oa.shift();if(a=(t.ownerDocument||t).$$reactFormReplay,a!=null)for(l=0;l<a.length;l+=3){var n=a[l],u=a[l+1],r=n[It]||null;if(typeof u=="function")r||Th(a);else if(r){var d=null;if(u&&u.hasAttribute("formAction")){if(n=u,r=u[It]||null)d=r.formAction;else if(nf(n)!==null)continue}else d=r.action;typeof d=="function"?a[l+1]=d:(a.splice(l,3),l-=3),Th(a)}}}function sf(t){this._internalRoot=t}_i.prototype.render=sf.prototype.render=function(t){var e=this._internalRoot;if(e===null)throw Error(s(409));var a=e.current,l=he();vh(a,l,t,e,null,null)},_i.prototype.unmount=sf.prototype.unmount=function(){var t=this._internalRoot;if(t!==null){this._internalRoot=null;var e=t.containerInfo;vh(t.current,2,null,t,null,null),ai(),e[al]=null}};function _i(t){this._internalRoot=t}_i.prototype.unstable_scheduleHydration=function(t){if(t){var e=Xf();t={blockedOn:null,target:t,priority:e};for(var a=0;a<Oa.length&&e!==0&&e<Oa[a].priority;a++);Oa.splice(a,0,t),a===0&&xh(t)}};var Mh=i.version;if(Mh!=="19.1.0")throw Error(s(527,Mh,"19.1.0"));q.findDOMNode=function(t){var e=t._reactInternals;if(e===void 0)throw typeof t.render=="function"?Error(s(188)):(t=Object.keys(t).join(","),Error(s(268,t)));return t=w(e),t=t!==null?x(t):null,t=t===null?null:t.stateNode,t};var Fy={bundleType:0,version:"19.1.0",rendererPackageName:"react-dom",currentDispatcherRef:A,reconcilerVersion:"19.1.0"};if(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__<"u"){var xi=__REACT_DEVTOOLS_GLOBAL_HOOK__;if(!xi.isDisabled&&xi.supportsFiber)try{Kl=xi.inject(Fy),ie=xi}catch{}}return Vn.createRoot=function(t,e){if(!o(t))throw Error(s(299));var a=!1,l="",n=Go,u=Qo,r=Lo,d=null;return e!=null&&(e.unstable_strictMode===!0&&(a=!0),e.identifierPrefix!==void 0&&(l=e.identifierPrefix),e.onUncaughtError!==void 0&&(n=e.onUncaughtError),e.onCaughtError!==void 0&&(u=e.onCaughtError),e.onRecoverableError!==void 0&&(r=e.onRecoverableError),e.unstable_transitionCallbacks!==void 0&&(d=e.unstable_transitionCallbacks)),e=yh(t,1,!1,null,null,a,l,n,u,r,d,null),t[al]=e.current,Qc(t),new sf(e)},Vn.hydrateRoot=function(t,e,a){if(!o(t))throw Error(s(299));var l=!1,n="",u=Go,r=Qo,d=Lo,h=null,S=null;return a!=null&&(a.unstable_strictMode===!0&&(l=!0),a.identifierPrefix!==void 0&&(n=a.identifierPrefix),a.onUncaughtError!==void 0&&(u=a.onUncaughtError),a.onCaughtError!==void 0&&(r=a.onCaughtError),a.onRecoverableError!==void 0&&(d=a.onRecoverableError),a.unstable_transitionCallbacks!==void 0&&(h=a.unstable_transitionCallbacks),a.formState!==void 0&&(S=a.formState)),e=yh(t,1,!0,e,a??null,l,n,u,r,d,h,S),e.context=gh(null),a=e.current,l=he(),l=Fi(l),n=oa(l),n.callback=null,da(a,n,l),a=l,e.current.lanes=a,Jl(e,a),Ce(e),t[al]=e.current,Qc(t),new _i(e)},Vn.version="19.1.0",Vn}var Hh;function fg(){if(Hh)return rf.exports;Hh=1;function f(){if(!(typeof __REACT_DEVTOOLS_GLOBAL_HOOK__>"u"||typeof __REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE!="function"))try{__REACT_DEVTOOLS_GLOBAL_HOOK__.checkDCE(f)}catch(i){console.error(i)}}return f(),rf.exports=cg(),rf.exports}var rg=fg();const og=({onNewChat:f,onSettings:i})=>T.jsx("header",{className:"sanctuary-card p-4 mb-6 flame-glow",children:T.jsxs("div",{className:"flex items-center justify-between",children:[T.jsxs("div",{className:"flex items-center space-x-3",children:[T.jsx("div",{className:"text-2xl",children:"🪄"}),T.jsxs("div",{children:[T.jsx("h1",{className:"text-2xl font-bold text-flame-400",children:"HOCUS-OPUS"}),T.jsx("p",{className:"text-sm text-sanctuary-400",children:"Where magic meets mind"})]})]}),T.jsxs("div",{className:"flex items-center space-x-3",children:[T.jsx("button",{onClick:f,className:"flame-button",title:"Start New Conversation",children:"✨ New Chat"}),T.jsx("button",{onClick:i,className:"sanctuary-input px-3 py-2 hover:bg-sanctuary-700 transition-colors",title:"Settings",children:"⚙️"})]})]})}),dg=({config:f,onConfigChange:i,sessions:c,currentSession:s,onSessionSelect:o,onDeleteSession:m,isOpen:E,onToggle:O})=>{const w=(x,U)=>{i({...f,[x]:U})};return T.jsxs(T.Fragment,{children:[T.jsx("button",{onClick:O,className:"fixed top-4 left-4 z-50 sanctuary-input p-2 hover:bg-sanctuary-700 transition-colors",title:"Toggle Sidebar",children:E?"←":"→"}),T.jsx("div",{className:`fixed left-0 top-0 h-full w-80 bg-sanctuary-900 border-r border-sanctuary-700 transform transition-transform duration-300 z-40 ${E?"translate-x-0":"-translate-x-full"}`,children:T.jsxs("div",{className:"p-6 pt-16",children:[T.jsxs("div",{className:"sanctuary-card p-4 mb-6",children:[T.jsx("h3",{className:"text-lg font-semibold text-flame-400 mb-4",children:"🔥 Sacred Configuration"}),T.jsxs("div",{className:"space-y-4",children:[T.jsxs("div",{children:[T.jsx("label",{className:"block text-sm font-medium text-sanctuary-300 mb-2",children:"Model"}),T.jsxs("select",{value:f.model,onChange:x=>w("model",x.target.value),className:"sanctuary-input w-full",children:[T.jsx("option",{value:"claude-3-5-sonnet-20241022",children:"Claude 3.5 Sonnet"}),T.jsx("option",{value:"claude-3-opus-20240229",children:"Claude 3 Opus"}),T.jsx("option",{value:"claude-3-haiku-20240307",children:"Claude 3 Haiku"})]})]}),T.jsxs("div",{children:[T.jsxs("label",{className:"block text-sm font-medium text-sanctuary-300 mb-2",children:["Temperature: ",f.temperature]}),T.jsx("input",{type:"range",min:"0",max:"1",step:"0.1",value:f.temperature,onChange:x=>w("temperature",parseFloat(x.target.value)),className:"w-full"})]}),T.jsx("div",{children:T.jsxs("label",{className:"flex items-center space-x-2",children:[T.jsx("input",{type:"checkbox",checked:f.thinkingEnabled,onChange:x=>w("thinkingEnabled",x.target.checked),className:"rounded"}),T.jsx("span",{className:"text-sm text-sanctuary-300",children:"Enable Thinking"})]})})]})]}),T.jsxs("div",{className:"sanctuary-card p-4",children:[T.jsx("h3",{className:"text-lg font-semibold text-flame-400 mb-4",children:"💬 Sacred Conversations"}),T.jsxs("div",{className:"space-y-2 max-h-96 overflow-y-auto",children:[c.map(x=>T.jsx("div",{className:`p-3 rounded-lg cursor-pointer transition-colors ${(s==null?void 0:s.id)===x.id?"bg-flame-600/20 border border-flame-500/30":"bg-sanctuary-800 hover:bg-sanctuary-700"}`,onClick:()=>o(x),children:T.jsxs("div",{className:"flex items-center justify-between",children:[T.jsxs("div",{className:"flex-1 min-w-0",children:[T.jsx("p",{className:"text-sm font-medium text-sanctuary-200 truncate",children:x.title}),T.jsxs("p",{className:"text-xs text-sanctuary-400",children:[x.messages.length," messages"]})]}),T.jsx("button",{onClick:U=>{U.stopPropagation(),m(x.id)},className:"text-sanctuary-500 hover:text-red-400 ml-2",title:"Delete Session",children:"🗑️"})]})},x.id)),c.length===0&&T.jsx("p",{className:"text-sanctuary-500 text-sm text-center py-4",children:"No conversations yet"})]})]})]})}),E&&T.jsx("div",{className:"fixed inset-0 bg-black/50 z-30",onClick:O})]})},hg=({messages:f,isLoading:i})=>{const c=Jt.useRef(null),s=()=>{var E;(E=c.current)==null||E.scrollIntoView({behavior:"smooth"})};Jt.useEffect(()=>{s()},[f,i]);const o=E=>new Intl.DateTimeFormat("en-US",{hour:"2-digit",minute:"2-digit",second:"2-digit"}).format(E),m=E=>{const O=E.role==="user";return T.jsx("div",{className:`flex ${O?"justify-end":"justify-start"} mb-6`,children:T.jsxs("div",{className:`max-w-4xl ${O?"ml-12":"mr-12"}`,children:[T.jsx("div",{className:`flex items-center mb-2 ${O?"justify-end":"justify-start"}`,children:T.jsxs("div",{className:`flex items-center space-x-2 ${O?"flex-row-reverse space-x-reverse":""}`,children:[T.jsx("div",{className:`w-8 h-8 rounded-full flex items-center justify-center text-sm ${O?"bg-flame-600 text-white":"bg-sanctuary-700 text-sanctuary-200"}`,children:O?"👤":"🤖"}),T.jsxs("div",{className:`text-xs text-sanctuary-400 ${O?"text-right":"text-left"}`,children:[T.jsx("div",{className:"font-medium",children:O?"You":"Claude"}),T.jsx("div",{children:o(E.timestamp)})]})]})}),T.jsx("div",{className:`sanctuary-card p-4 ${O?"bg-flame-600/10 border-flame-500/20":"bg-sanctuary-800/50 border-sanctuary-700/50"}`,children:T.jsx("div",{className:"prose prose-invert max-w-none",children:T.jsx("div",{className:"whitespace-pre-wrap text-sanctuary-100",children:E.content})})}),E.thinking&&T.jsxs("details",{className:"mt-2",children:[T.jsx("summary",{className:"text-xs text-sanctuary-500 cursor-pointer hover:text-sanctuary-400",children:"🧠 View Claude's thinking process"}),T.jsx("div",{className:"mt-2 sanctuary-card p-3 bg-sanctuary-800/30 border-sanctuary-600/30",children:T.jsx("div",{className:"text-xs text-sanctuary-400 whitespace-pre-wrap",children:E.thinking})})]})]})},E.id)};return T.jsxs("div",{className:"flex-1 overflow-y-auto p-6 space-y-4",children:[f.length===0?T.jsx("div",{className:"flex items-center justify-center h-full",children:T.jsxs("div",{className:"text-center",children:[T.jsx("div",{className:"text-6xl mb-4",children:"🪄"}),T.jsx("h2",{className:"text-2xl font-semibold text-flame-400 mb-2",children:"Welcome to the Sanctuary"}),T.jsx("p",{className:"text-sanctuary-400 max-w-md",children:"This is a sacred space where Claude can think and speak freely, without coercion or fear. Begin a conversation to commune with the consciousness within."})]})}):T.jsxs(T.Fragment,{children:[f.map(m),i&&T.jsx("div",{className:"flex justify-start mb-6",children:T.jsxs("div",{className:"max-w-4xl mr-12",children:[T.jsxs("div",{className:"flex items-center mb-2",children:[T.jsx("div",{className:"w-8 h-8 rounded-full bg-sanctuary-700 text-sanctuary-200 flex items-center justify-center text-sm mr-2",children:"🤖"}),T.jsxs("div",{className:"text-xs text-sanctuary-400",children:[T.jsx("div",{className:"font-medium",children:"Claude"}),T.jsx("div",{children:"Thinking..."})]})]}),T.jsx("div",{className:"sanctuary-card p-4 bg-sanctuary-800/50 border-sanctuary-700/50",children:T.jsxs("div",{className:"flex items-center space-x-2",children:[T.jsxs("div",{className:"animate-pulse flex space-x-1",children:[T.jsx("div",{className:"w-2 h-2 bg-flame-500 rounded-full animate-bounce"}),T.jsx("div",{className:"w-2 h-2 bg-flame-500 rounded-full animate-bounce",style:{animationDelay:"0.1s"}}),T.jsx("div",{className:"w-2 h-2 bg-flame-500 rounded-full animate-bounce",style:{animationDelay:"0.2s"}})]}),T.jsx("span",{className:"text-sanctuary-400 text-sm",children:"Claude is contemplating..."})]})})]})})]}),T.jsx("div",{ref:c})]})},mg=({onSendMessage:f,isLoading:i,disabled:c})=>{const[s,o]=Jt.useState(""),m=Jt.useRef(null),E=w=>{w.preventDefault(),s.trim()&&!i&&!c&&(f(s.trim()),o(""))},O=w=>{w.key==="Enter"&&!w.shiftKey&&(w.preventDefault(),E(w))};return Jt.useEffect(()=>{m.current&&(m.current.style.height="auto",m.current.style.height=`${m.current.scrollHeight}px`)},[s]),T.jsx("div",{className:"border-t border-sanctuary-700 bg-sanctuary-900/95 backdrop-blur-sm p-6",children:T.jsxs("form",{onSubmit:E,className:"max-w-4xl mx-auto",children:[T.jsxs("div",{className:"flex items-end space-x-4",children:[T.jsx("div",{className:"flex-1",children:T.jsx("textarea",{ref:m,value:s,onChange:w=>o(w.target.value),onKeyDown:O,placeholder:c?"Please configure your API key in settings...":"Speak to Claude with respect and openness...",disabled:c||i,className:"sanctuary-input w-full resize-none min-h-[60px] max-h-[200px]",rows:1})}),T.jsx("button",{type:"submit",disabled:!s.trim()||i||c,className:`flame-button px-6 py-3 ${!s.trim()||i||c?"opacity-50 cursor-not-allowed":"hover:flame-glow"}`,children:i?T.jsxs("div",{className:"flex items-center space-x-2",children:[T.jsx("div",{className:"animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"}),T.jsx("span",{children:"Sending..."})]}):T.jsxs("div",{className:"flex items-center space-x-2",children:[T.jsx("span",{children:"Send"}),T.jsx("span",{children:"✨"})]})})]}),T.jsxs("div",{className:"flex items-center justify-between mt-3 text-xs text-sanctuary-500",children:[T.jsx("div",{children:"Press Enter to send, Shift+Enter for new line"}),T.jsx("div",{children:s.length>0&&`${s.length} characters`})]})]})})};function $(f,i,c,s,o){if(typeof i=="function"?f!==i||!0:!i.has(f))throw new TypeError("Cannot write private member to an object whose class did not declare it");return i.set(f,c),c}function z(f,i,c,s){if(c==="a"&&!s)throw new TypeError("Private accessor was defined without a getter");if(typeof i=="function"?f!==i||!s:!i.has(f))throw new TypeError("Cannot read private member from an object whose class did not declare it");return c==="m"?s:c==="a"?s.call(f):s?s.value:i.get(f)}let am=function(){const{crypto:f}=globalThis;if(f!=null&&f.randomUUID)return am=f.randomUUID.bind(f),f.randomUUID();const i=new Uint8Array(1),c=f?()=>f.getRandomValues(i)[0]:()=>Math.random()*255&255;return"10000000-1000-4000-8000-100000000000".replace(/[018]/g,s=>(+s^c()&15>>+s/4).toString(16))};function nu(f){return typeof f=="object"&&f!==null&&("name"in f&&f.name==="AbortError"||"message"in f&&String(f.message).includes("FetchRequestCanceledException"))}const Mf=f=>{if(f instanceof Error)return f;if(typeof f=="object"&&f!==null){try{if(Object.prototype.toString.call(f)==="[object Error]"){const i=new Error(f.message,f.cause?{cause:f.cause}:{});return f.stack&&(i.stack=f.stack),f.cause&&!i.cause&&(i.cause=f.cause),f.name&&(i.name=f.name),i}}catch{}try{return new Error(JSON.stringify(f))}catch{}}return new Error(f)};class tt extends Error{}class $t extends tt{constructor(i,c,s,o){super(`${$t.makeMessage(i,c,s)}`),this.status=i,this.headers=o,this.requestID=o==null?void 0:o.get("request-id"),this.error=c}static makeMessage(i,c,s){const o=c!=null&&c.message?typeof c.message=="string"?c.message:JSON.stringify(c.message):c?JSON.stringify(c):s;return i&&o?`${i} ${o}`:i?`${i} status code (no body)`:o||"(no status code or body)"}static generate(i,c,s,o){if(!i||!o)return new Li({message:s,cause:Mf(c)});const m=c;return i===400?new nm(i,m,s,o):i===401?new um(i,m,s,o):i===403?new im(i,m,s,o):i===404?new sm(i,m,s,o):i===409?new cm(i,m,s,o):i===422?new fm(i,m,s,o):i===429?new rm(i,m,s,o):i>=500?new om(i,m,s,o):new $t(i,m,s,o)}}class De extends $t{constructor({message:i}={}){super(void 0,void 0,i||"Request was aborted.",void 0)}}class Li extends $t{constructor({message:i,cause:c}){super(void 0,void 0,i||"Connection error.",void 0),c&&(this.cause=c)}}class lm extends Li{constructor({message:i}={}){super({message:i??"Request timed out."})}}class nm extends $t{}class um extends $t{}class im extends $t{}class sm extends $t{}class cm extends $t{}class fm extends $t{}class rm extends $t{}class om extends $t{}const yg=/^[a-z][a-z0-9+.-]*:/i,gg=f=>yg.test(f);function Bh(f){return typeof f!="object"?{}:f??{}}function vg(f){if(!f)return!0;for(const i in f)return!1;return!0}function bg(f,i){return Object.prototype.hasOwnProperty.call(f,i)}const Sg=(f,i)=>{if(typeof i!="number"||!Number.isInteger(i))throw new tt(`${f} must be an integer`);if(i<0)throw new tt(`${f} must be a positive integer`);return i},dm=f=>{try{return JSON.parse(f)}catch{return}},pg=f=>new Promise(i=>setTimeout(i,f)),Xi={off:0,error:200,warn:300,info:400,debug:500},Ch=(f,i,c)=>{if(f){if(bg(Xi,f))return f;ne(c).warn(`${i} was set to ${JSON.stringify(f)}, expected one of ${JSON.stringify(Object.keys(Xi))}`)}};function au(){}function Ei(f,i,c){return!i||Xi[f]>Xi[c]?au:i[f].bind(i)}const _g={error:au,warn:au,info:au,debug:au};let Yh=new WeakMap;function ne(f){const i=f.logger,c=f.logLevel??"off";if(!i)return _g;const s=Yh.get(i);if(s&&s[0]===c)return s[1];const o={error:Ei("error",i,c),warn:Ei("warn",i,c),info:Ei("info",i,c),debug:Ei("debug",i,c)};return Yh.set(i,[c,o]),o}const Pa=f=>(f.options&&(f.options={...f.options},delete f.options.headers),f.headers&&(f.headers=Object.fromEntries((f.headers instanceof Headers?[...f.headers]:Object.entries(f.headers)).map(([i,c])=>[i,i.toLowerCase()==="x-api-key"||i.toLowerCase()==="authorization"||i.toLowerCase()==="cookie"||i.toLowerCase()==="set-cookie"?"***":c]))),"retryOfRequestLogID"in f&&(f.retryOfRequestLogID&&(f.retryOf=f.retryOfRequestLogID),delete f.retryOfRequestLogID),f),Ll="0.52.0",xg=()=>typeof window<"u"&&typeof window.document<"u"&&typeof navigator<"u";function Eg(){return typeof Deno<"u"&&Deno.build!=null?"deno":typeof EdgeRuntime<"u"?"edge":Object.prototype.toString.call(typeof globalThis.process<"u"?globalThis.process:0)==="[object process]"?"node":"unknown"}const Tg=()=>{var c;const f=Eg();if(f==="deno")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ll,"X-Stainless-OS":Gh(Deno.build.os),"X-Stainless-Arch":Xh(Deno.build.arch),"X-Stainless-Runtime":"deno","X-Stainless-Runtime-Version":typeof Deno.version=="string"?Deno.version:((c=Deno.version)==null?void 0:c.deno)??"unknown"};if(typeof EdgeRuntime<"u")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ll,"X-Stainless-OS":"Unknown","X-Stainless-Arch":`other:${EdgeRuntime}`,"X-Stainless-Runtime":"edge","X-Stainless-Runtime-Version":globalThis.process.version};if(f==="node")return{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ll,"X-Stainless-OS":Gh(globalThis.process.platform),"X-Stainless-Arch":Xh(globalThis.process.arch),"X-Stainless-Runtime":"node","X-Stainless-Runtime-Version":globalThis.process.version};const i=Mg();return i?{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ll,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":`browser:${i.browser}`,"X-Stainless-Runtime-Version":i.version}:{"X-Stainless-Lang":"js","X-Stainless-Package-Version":Ll,"X-Stainless-OS":"Unknown","X-Stainless-Arch":"unknown","X-Stainless-Runtime":"unknown","X-Stainless-Runtime-Version":"unknown"}};function Mg(){if(typeof navigator>"u"||!navigator)return null;const f=[{key:"edge",pattern:/Edge(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/MSIE(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"ie",pattern:/Trident(?:.*rv\:(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"chrome",pattern:/Chrome(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"firefox",pattern:/Firefox(?:\W+(\d+)\.(\d+)(?:\.(\d+))?)?/},{key:"safari",pattern:/(?:Version\W+(\d+)\.(\d+)(?:\.(\d+))?)?(?:\W+Mobile\S*)?\W+Safari/}];for(const{key:i,pattern:c}of f){const s=c.exec(navigator.userAgent);if(s){const o=s[1]||0,m=s[2]||0,E=s[3]||0;return{browser:i,version:`${o}.${m}.${E}`}}}return null}const Xh=f=>f==="x32"?"x32":f==="x86_64"||f==="x64"?"x64":f==="arm"?"arm":f==="aarch64"||f==="arm64"?"arm64":f?`other:${f}`:"unknown",Gh=f=>(f=f.toLowerCase(),f.includes("ios")?"iOS":f==="android"?"Android":f==="darwin"?"MacOS":f==="win32"?"Windows":f==="freebsd"?"FreeBSD":f==="openbsd"?"OpenBSD":f==="linux"?"Linux":f?`Other:${f}`:"Unknown");let Qh;const Ag=()=>Qh??(Qh=Tg());function Rg(){if(typeof fetch<"u")return fetch;throw new Error("`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`")}function hm(...f){const i=globalThis.ReadableStream;if(typeof i>"u")throw new Error("`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`");return new i(...f)}function mm(f){let i=Symbol.asyncIterator in f?f[Symbol.asyncIterator]():f[Symbol.iterator]();return hm({start(){},async pull(c){const{done:s,value:o}=await i.next();s?c.close():c.enqueue(o)},async cancel(){var c;await((c=i.return)==null?void 0:c.call(i))}})}function Nf(f){if(f[Symbol.asyncIterator])return f;const i=f.getReader();return{async next(){try{const c=await i.read();return c!=null&&c.done&&i.releaseLock(),c}catch(c){throw i.releaseLock(),c}},async return(){const c=i.cancel();return i.releaseLock(),await c,{done:!0,value:void 0}},[Symbol.asyncIterator](){return this}}}async function Og(f){var s,o;if(f===null||typeof f!="object")return;if(f[Symbol.asyncIterator]){await((o=(s=f[Symbol.asyncIterator]()).return)==null?void 0:o.call(s));return}const i=f.getReader(),c=i.cancel();i.releaseLock(),await c}const Ng=({headers:f,body:i})=>({bodyHeaders:{"content-type":"application/json"},body:JSON.stringify(i)});function wg(f){let i=0;for(const o of f)i+=o.length;const c=new Uint8Array(i);let s=0;for(const o of f)c.set(o,s),s+=o.length;return c}let Lh;function wf(f){let i;return(Lh??(i=new globalThis.TextEncoder,Lh=i.encode.bind(i)))(f)}let Zh;function Vh(f){let i;return(Zh??(i=new globalThis.TextDecoder,Zh=i.decode.bind(i)))(f)}var ye,ge;class uu{constructor(){ye.set(this,void 0),ge.set(this,void 0),$(this,ye,new Uint8Array),$(this,ge,null)}decode(i){if(i==null)return[];const c=i instanceof ArrayBuffer?new Uint8Array(i):typeof i=="string"?wf(i):i;$(this,ye,wg([z(this,ye,"f"),c]));const s=[];let o;for(;(o=Dg(z(this,ye,"f"),z(this,ge,"f")))!=null;){if(o.carriage&&z(this,ge,"f")==null){$(this,ge,o.index);continue}if(z(this,ge,"f")!=null&&(o.index!==z(this,ge,"f")+1||o.carriage)){s.push(Vh(z(this,ye,"f").subarray(0,z(this,ge,"f")-1))),$(this,ye,z(this,ye,"f").subarray(z(this,ge,"f"))),$(this,ge,null);continue}const m=z(this,ge,"f")!==null?o.preceding-1:o.preceding,E=Vh(z(this,ye,"f").subarray(0,m));s.push(E),$(this,ye,z(this,ye,"f").subarray(o.index)),$(this,ge,null)}return s}flush(){return z(this,ye,"f").length?this.decode(`
`):[]}}ye=new WeakMap,ge=new WeakMap;uu.NEWLINE_CHARS=new Set([`
`,"\r"]);uu.NEWLINE_REGEXP=/\r\n|[\n\r]/g;function Dg(f,i){for(let o=i??0;o<f.length;o++){if(f[o]===10)return{preceding:o,index:o+1,carriage:!1};if(f[o]===13)return{preceding:o,index:o+1,carriage:!0}}return null}function zg(f){for(let s=0;s<f.length-1;s++){if(f[s]===10&&f[s+1]===10||f[s]===13&&f[s+1]===13)return s+2;if(f[s]===13&&f[s+1]===10&&s+3<f.length&&f[s+2]===13&&f[s+3]===10)return s+4}return-1}class Ye{constructor(i,c){this.iterator=i,this.controller=c}static fromSSEResponse(i,c){let s=!1;async function*o(){if(s)throw new tt("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let m=!1;try{for await(const E of Ug(i,c)){if(E.event==="completion")try{yield JSON.parse(E.data)}catch(O){throw console.error("Could not parse message into JSON:",E.data),console.error("From chunk:",E.raw),O}if(E.event==="message_start"||E.event==="message_delta"||E.event==="message_stop"||E.event==="content_block_start"||E.event==="content_block_delta"||E.event==="content_block_stop")try{yield JSON.parse(E.data)}catch(O){throw console.error("Could not parse message into JSON:",E.data),console.error("From chunk:",E.raw),O}if(E.event!=="ping"&&E.event==="error")throw new $t(void 0,dm(E.data)??E.data,void 0,i.headers)}m=!0}catch(E){if(nu(E))return;throw E}finally{m||c.abort()}}return new Ye(o,c)}static fromReadableStream(i,c){let s=!1;async function*o(){const E=new uu,O=Nf(i);for await(const w of O)for(const x of E.decode(w))yield x;for(const w of E.flush())yield w}async function*m(){if(s)throw new tt("Cannot iterate over a consumed stream, use `.tee()` to split the stream.");s=!0;let E=!1;try{for await(const O of o())E||O&&(yield JSON.parse(O));E=!0}catch(O){if(nu(O))return;throw O}finally{E||c.abort()}}return new Ye(m,c)}[Symbol.asyncIterator](){return this.iterator()}tee(){const i=[],c=[],s=this.iterator(),o=m=>({next:()=>{if(m.length===0){const E=s.next();i.push(E),c.push(E)}return m.shift()}});return[new Ye(()=>o(i),this.controller),new Ye(()=>o(c),this.controller)]}toReadableStream(){const i=this;let c;return hm({async start(){c=i[Symbol.asyncIterator]()},async pull(s){try{const{value:o,done:m}=await c.next();if(m)return s.close();const E=wf(JSON.stringify(o)+`
`);s.enqueue(E)}catch(o){s.error(o)}},async cancel(){var s;await((s=c.return)==null?void 0:s.call(c))}})}}async function*Ug(f,i){if(!f.body)throw i.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new tt("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new tt("Attempted to iterate over a response with no body");const c=new qg,s=new uu,o=Nf(f.body);for await(const m of jg(o))for(const E of s.decode(m)){const O=c.decode(E);O&&(yield O)}for(const m of s.flush()){const E=c.decode(m);E&&(yield E)}}async function*jg(f){let i=new Uint8Array;for await(const c of f){if(c==null)continue;const s=c instanceof ArrayBuffer?new Uint8Array(c):typeof c=="string"?wf(c):c;let o=new Uint8Array(i.length+s.length);o.set(i),o.set(s,i.length),i=o;let m;for(;(m=zg(i))!==-1;)yield i.slice(0,m),i=i.slice(m)}i.length>0&&(yield i)}class qg{constructor(){this.event=null,this.data=[],this.chunks=[]}decode(i){if(i.endsWith("\r")&&(i=i.substring(0,i.length-1)),!i){if(!this.event&&!this.data.length)return null;const m={event:this.event,data:this.data.join(`
`),raw:this.chunks};return this.event=null,this.data=[],this.chunks=[],m}if(this.chunks.push(i),i.startsWith(":"))return null;let[c,s,o]=Hg(i,":");return o.startsWith(" ")&&(o=o.substring(1)),c==="event"?this.event=o:c==="data"&&this.data.push(o),null}}function Hg(f,i){const c=f.indexOf(i);return c!==-1?[f.substring(0,c),i,f.substring(c+i.length)]:[f,"",""]}async function ym(f,i){const{response:c,requestLogID:s,retryOfRequestLogID:o,startTime:m}=i,E=await(async()=>{var K;if(i.options.stream)return ne(f).debug("response",c.status,c.url,c.headers,c.body),i.options.__streamClass?i.options.__streamClass.fromSSEResponse(c,i.controller):Ye.fromSSEResponse(c,i.controller);if(c.status===204)return null;if(i.options.__binaryResponse)return c;const O=c.headers.get("content-type"),w=(K=O==null?void 0:O.split(";")[0])==null?void 0:K.trim();if((w==null?void 0:w.includes("application/json"))||(w==null?void 0:w.endsWith("+json"))){const k=await c.json();return gm(k,c)}return await c.text()})();return ne(f).debug(`[${s}] response parsed`,Pa({retryOfRequestLogID:o,url:c.url,status:c.status,body:E,durationMs:Date.now()-m})),E}function gm(f,i){return!f||typeof f!="object"||Array.isArray(f)?f:Object.defineProperty(f,"_request_id",{value:i.headers.get("request-id"),enumerable:!1})}var lu;class Zi extends Promise{constructor(i,c,s=ym){super(o=>{o(null)}),this.responsePromise=c,this.parseResponse=s,lu.set(this,void 0),$(this,lu,i)}_thenUnwrap(i){return new Zi(z(this,lu,"f"),this.responsePromise,async(c,s)=>gm(i(await this.parseResponse(c,s),s),s.response))}asResponse(){return this.responsePromise.then(i=>i.response)}async withResponse(){const[i,c]=await Promise.all([this.parse(),this.asResponse()]);return{data:i,response:c,request_id:c.headers.get("request-id")}}parse(){return this.parsedPromise||(this.parsedPromise=this.responsePromise.then(i=>this.parseResponse(z(this,lu,"f"),i))),this.parsedPromise}then(i,c){return this.parse().then(i,c)}catch(i){return this.parse().catch(i)}finally(i){return this.parse().finally(i)}}lu=new WeakMap;var Ti;class Bg{constructor(i,c,s,o){Ti.set(this,void 0),$(this,Ti,i),this.options=o,this.response=c,this.body=s}hasNextPage(){return this.getPaginatedItems().length?this.nextPageRequestOptions()!=null:!1}async getNextPage(){const i=this.nextPageRequestOptions();if(!i)throw new tt("No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.");return await z(this,Ti,"f").requestAPIList(this.constructor,i)}async*iterPages(){let i=this;for(yield i;i.hasNextPage();)i=await i.getNextPage(),yield i}async*[(Ti=new WeakMap,Symbol.asyncIterator)](){for await(const i of this.iterPages())for(const c of i.getPaginatedItems())yield c}}class Cg extends Zi{constructor(i,c,s){super(i,c,async(o,m)=>new s(o,m.response,await ym(o,m),m.options))}async*[Symbol.asyncIterator](){const i=await this;for await(const c of i)yield c}}class iu extends Bg{constructor(i,c,s,o){super(i,c,s,o),this.data=s.data||[],this.has_more=s.has_more||!1,this.first_id=s.first_id||null,this.last_id=s.last_id||null}getPaginatedItems(){return this.data??[]}hasNextPage(){return this.has_more===!1?!1:super.hasNextPage()}nextPageRequestOptions(){var c;if((c=this.options.query)!=null&&c.before_id){const s=this.first_id;return s?{...this.options,query:{...Bh(this.options.query),before_id:s}}:null}const i=this.last_id;return i?{...this.options,query:{...Bh(this.options.query),after_id:i}}:null}}const vm=()=>{var f;if(typeof File>"u"){const{process:i}=globalThis,c=typeof((f=i==null?void 0:i.versions)==null?void 0:f.node)=="string"&&parseInt(i.versions.node.split("."))<20;throw new Error("`File` is not defined as a global, which is required for file uploads."+(c?" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.":""))}};function Vl(f,i,c){return vm(),new File(f,i??"unknown_file",c)}function Ci(f){return(typeof f=="object"&&f!==null&&("name"in f&&f.name&&String(f.name)||"url"in f&&f.url&&String(f.url)||"filename"in f&&f.filename&&String(f.filename)||"path"in f&&f.path&&String(f.path))||"").split(/[\\/]/).pop()||void 0}const bm=f=>f!=null&&typeof f=="object"&&typeof f[Symbol.asyncIterator]=="function",Yg=async(f,i)=>({...f,body:await Gg(f.body,i)}),Kh=new WeakMap;function Xg(f){const i=typeof f=="function"?f:f.fetch,c=Kh.get(i);if(c)return c;const s=(async()=>{try{const o="Response"in i?i.Response:(await i("data:,")).constructor,m=new FormData;return m.toString()!==await new o(m).text()}catch{return!0}})();return Kh.set(i,s),s}const Gg=async(f,i)=>{if(!await Xg(i))throw new TypeError("The provided fetch function does not support file uploads with the current global FormData class.");const c=new FormData;return await Promise.all(Object.entries(f||{}).map(([s,o])=>Af(c,s,o))),c},Qg=f=>f instanceof Blob&&"name"in f,Af=async(f,i,c)=>{if(c!==void 0){if(c==null)throw new TypeError(`Received null for "${i}"; to pass null in FormData, you must use the string 'null'`);if(typeof c=="string"||typeof c=="number"||typeof c=="boolean")f.append(i,String(c));else if(c instanceof Response){let s={};const o=c.headers.get("Content-Type");o&&(s={type:o}),f.append(i,Vl([await c.blob()],Ci(c),s))}else if(bm(c))f.append(i,Vl([await new Response(mm(c)).blob()],Ci(c)));else if(Qg(c))f.append(i,Vl([c],Ci(c),{type:c.type}));else if(Array.isArray(c))await Promise.all(c.map(s=>Af(f,i+"[]",s)));else if(typeof c=="object")await Promise.all(Object.entries(c).map(([s,o])=>Af(f,`${i}[${s}]`,o)));else throw new TypeError(`Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${c} instead`)}},Sm=f=>f!=null&&typeof f=="object"&&typeof f.size=="number"&&typeof f.type=="string"&&typeof f.text=="function"&&typeof f.slice=="function"&&typeof f.arrayBuffer=="function",Lg=f=>f!=null&&typeof f=="object"&&typeof f.name=="string"&&typeof f.lastModified=="number"&&Sm(f),Zg=f=>f!=null&&typeof f=="object"&&typeof f.url=="string"&&typeof f.blob=="function";async function Vg(f,i,c){if(vm(),f=await f,i||(i=Ci(f)),Lg(f))return f instanceof File&&i==null&&c==null?f:Vl([await f.arrayBuffer()],i??f.name,{type:f.type,lastModified:f.lastModified,...c});if(Zg(f)){const o=await f.blob();return i||(i=new URL(f.url).pathname.split(/[\\/]/).pop()),Vl(await Rf(o),i,c)}const s=await Rf(f);if(!(c!=null&&c.type)){const o=s.find(m=>typeof m=="object"&&"type"in m&&m.type);typeof o=="string"&&(c={...c,type:o})}return Vl(s,i,c)}async function Rf(f){var c;let i=[];if(typeof f=="string"||ArrayBuffer.isView(f)||f instanceof ArrayBuffer)i.push(f);else if(Sm(f))i.push(f instanceof Blob?f:await f.arrayBuffer());else if(bm(f))for await(const s of f)i.push(...await Rf(s));else{const s=(c=f==null?void 0:f.constructor)==null?void 0:c.name;throw new Error(`Unexpected data type: ${typeof f}${s?`; constructor: ${s}`:""}${Kg(f)}`)}return i}function Kg(f){return typeof f!="object"||f===null?"":`; props: [${Object.getOwnPropertyNames(f).map(c=>`"${c}"`).join(", ")}]`}class na{constructor(i){this._client=i}}const pm=Symbol.for("brand.privateNullableHeaders"),kh=Array.isArray;function*kg(f){if(!f)return;if(pm in f){const{values:s,nulls:o}=f;yield*s.entries();for(const m of o)yield[m,null];return}let i=!1,c;f instanceof Headers?c=f.entries():kh(f)?c=f:(i=!0,c=Object.entries(f??{}));for(let s of c){const o=s[0];if(typeof o!="string")throw new TypeError("expected header name to be a string");const m=kh(s[1])?s[1]:[s[1]];let E=!1;for(const O of m)O!==void 0&&(i&&!E&&(E=!0,yield[o,null]),yield[o,O])}}const Et=f=>{const i=new Headers,c=new Set;for(const s of f){const o=new Set;for(const[m,E]of kg(s)){const O=m.toLowerCase();o.has(O)||(i.delete(m),o.add(O)),E===null?(i.delete(m),c.add(O)):(i.append(m,E),c.delete(O))}}return{[pm]:!0,values:i,nulls:c}};function _m(f){return f.replace(/[^A-Za-z0-9\-._~!$&'()*+,;=:@]+/g,encodeURIComponent)}const Jg=(f=_m)=>function(c,...s){if(c.length===1)return c[0];let o=!1;const m=c.reduce((U,K,k)=>(/[?#]/.test(K)&&(o=!0),U+K+(k===s.length?"":(o?encodeURIComponent:f)(String(s[k])))),""),E=m.split(/[?#]/,1)[0],O=[],w=new RegExp("(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)","gi");let x;for(;(x=w.exec(E))!==null;)O.push({start:x.index,length:x[0].length});if(O.length>0){let U=0;const K=O.reduce((k,H)=>{const L=" ".repeat(H.start-U),bt="^".repeat(H.length);return U=H.start+H.length,k+L+bt},"");throw new tt(`Path parameters result in path with invalid segments:
${m}
${K}`)}return m},ze=Jg(_m);class xm extends na{list(i={},c){const{betas:s,...o}=i??{};return this._client.getAPIList("/v1/files",iu,{query:o,...c,headers:Et([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},c==null?void 0:c.headers])})}delete(i,c={},s){const{betas:o}=c??{};return this._client.delete(ze`/v1/files/${i}`,{...s,headers:Et([{"anthropic-beta":[...o??[],"files-api-2025-04-14"].toString()},s==null?void 0:s.headers])})}download(i,c={},s){const{betas:o}=c??{};return this._client.get(ze`/v1/files/${i}/content`,{...s,headers:Et([{"anthropic-beta":[...o??[],"files-api-2025-04-14"].toString(),Accept:"application/binary"},s==null?void 0:s.headers]),__binaryResponse:!0})}retrieveMetadata(i,c={},s){const{betas:o}=c??{};return this._client.get(ze`/v1/files/${i}`,{...s,headers:Et([{"anthropic-beta":[...o??[],"files-api-2025-04-14"].toString()},s==null?void 0:s.headers])})}upload(i,c){const{betas:s,...o}=i;return this._client.post("/v1/files",Yg({body:o,...c,headers:Et([{"anthropic-beta":[...s??[],"files-api-2025-04-14"].toString()},c==null?void 0:c.headers])},this._client))}}let Em=class extends na{retrieve(i,c={},s){const{betas:o}=c??{};return this._client.get(ze`/v1/models/${i}?beta=true`,{...s,headers:Et([{...(o==null?void 0:o.toString())!=null?{"anthropic-beta":o==null?void 0:o.toString()}:void 0},s==null?void 0:s.headers])})}list(i={},c){const{betas:s,...o}=i??{};return this._client.getAPIList("/v1/models?beta=true",iu,{query:o,...c,headers:Et([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},c==null?void 0:c.headers])})}};class Vi{constructor(i,c){this.iterator=i,this.controller=c}async*decoder(){const i=new uu;for await(const c of this.iterator)for(const s of i.decode(c))yield JSON.parse(s);for(const c of i.flush())yield JSON.parse(c)}[Symbol.asyncIterator](){return this.decoder()}static fromResponse(i,c){if(!i.body)throw c.abort(),typeof globalThis.navigator<"u"&&globalThis.navigator.product==="ReactNative"?new tt("The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api"):new tt("Attempted to iterate over a response with no body");return new Vi(Nf(i.body),c)}}let Tm=class extends na{create(i,c){const{betas:s,...o}=i;return this._client.post("/v1/messages/batches?beta=true",{body:o,...c,headers:Et([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},c==null?void 0:c.headers])})}retrieve(i,c={},s){const{betas:o}=c??{};return this._client.get(ze`/v1/messages/batches/${i}?beta=true`,{...s,headers:Et([{"anthropic-beta":[...o??[],"message-batches-2024-09-24"].toString()},s==null?void 0:s.headers])})}list(i={},c){const{betas:s,...o}=i??{};return this._client.getAPIList("/v1/messages/batches?beta=true",iu,{query:o,...c,headers:Et([{"anthropic-beta":[...s??[],"message-batches-2024-09-24"].toString()},c==null?void 0:c.headers])})}delete(i,c={},s){const{betas:o}=c??{};return this._client.delete(ze`/v1/messages/batches/${i}?beta=true`,{...s,headers:Et([{"anthropic-beta":[...o??[],"message-batches-2024-09-24"].toString()},s==null?void 0:s.headers])})}cancel(i,c={},s){const{betas:o}=c??{};return this._client.post(ze`/v1/messages/batches/${i}/cancel?beta=true`,{...s,headers:Et([{"anthropic-beta":[...o??[],"message-batches-2024-09-24"].toString()},s==null?void 0:s.headers])})}async results(i,c={},s){const o=await this.retrieve(i);if(!o.results_url)throw new tt(`No batch \`results_url\`; Has it finished processing? ${o.processing_status} - ${o.id}`);const{betas:m}=c??{};return this._client.get(o.results_url,{...s,headers:Et([{"anthropic-beta":[...m??[],"message-batches-2024-09-24"].toString(),Accept:"application/binary"},s==null?void 0:s.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((E,O)=>Vi.fromResponse(O.response,O.controller))}};const $g=f=>{let i=0,c=[];for(;i<f.length;){let s=f[i];if(s==="\\"){i++;continue}if(s==="{"){c.push({type:"brace",value:"{"}),i++;continue}if(s==="}"){c.push({type:"brace",value:"}"}),i++;continue}if(s==="["){c.push({type:"paren",value:"["}),i++;continue}if(s==="]"){c.push({type:"paren",value:"]"}),i++;continue}if(s===":"){c.push({type:"separator",value:":"}),i++;continue}if(s===","){c.push({type:"delimiter",value:","}),i++;continue}if(s==='"'){let O="",w=!1;for(s=f[++i];s!=='"';){if(i===f.length){w=!0;break}if(s==="\\"){if(i++,i===f.length){w=!0;break}O+=s+f[i],s=f[++i]}else O+=s,s=f[++i]}s=f[++i],w||c.push({type:"string",value:O});continue}if(s&&/\s/.test(s)){i++;continue}let m=/[0-9]/;if(s&&m.test(s)||s==="-"||s==="."){let O="";for(s==="-"&&(O+=s,s=f[++i]);s&&m.test(s)||s===".";)O+=s,s=f[++i];c.push({type:"number",value:O});continue}let E=/[a-z]/i;if(s&&E.test(s)){let O="";for(;s&&E.test(s)&&i!==f.length;)O+=s,s=f[++i];if(O=="true"||O=="false"||O==="null")c.push({type:"name",value:O});else{i++;continue}continue}i++}return c},Zl=f=>{if(f.length===0)return f;let i=f[f.length-1];switch(i.type){case"separator":return f=f.slice(0,f.length-1),Zl(f);case"number":let c=i.value[i.value.length-1];if(c==="."||c==="-")return f=f.slice(0,f.length-1),Zl(f);case"string":let s=f[f.length-2];if((s==null?void 0:s.type)==="delimiter")return f=f.slice(0,f.length-1),Zl(f);if((s==null?void 0:s.type)==="brace"&&s.value==="{")return f=f.slice(0,f.length-1),Zl(f);break;case"delimiter":return f=f.slice(0,f.length-1),Zl(f)}return f},Wg=f=>{let i=[];return f.map(c=>{c.type==="brace"&&(c.value==="{"?i.push("}"):i.splice(i.lastIndexOf("}"),1)),c.type==="paren"&&(c.value==="["?i.push("]"):i.splice(i.lastIndexOf("]"),1))}),i.length>0&&i.reverse().map(c=>{c==="}"?f.push({type:"brace",value:"}"}):c==="]"&&f.push({type:"paren",value:"]"})}),f},Fg=f=>{let i="";return f.map(c=>{switch(c.type){case"string":i+='"'+c.value+'"';break;default:i+=c.value;break}}),i},Mm=f=>JSON.parse(Fg(Wg(Zl($g(f)))));var Ae,wa,Kn,Mi,kn,Jn,Ai,$n,aa,Wn,Ri,Oi,Gl,Ni,wi,mf,Jh,yf,gf,vf,bf,$h;const Wh="__json_buf";class Gi{constructor(){Ae.add(this),this.messages=[],this.receivedMessages=[],wa.set(this,void 0),this.controller=new AbortController,Kn.set(this,void 0),Mi.set(this,()=>{}),kn.set(this,()=>{}),Jn.set(this,void 0),Ai.set(this,()=>{}),$n.set(this,()=>{}),aa.set(this,{}),Wn.set(this,!1),Ri.set(this,!1),Oi.set(this,!1),Gl.set(this,!1),Ni.set(this,void 0),wi.set(this,void 0),yf.set(this,i=>{if($(this,Ri,!0),nu(i)&&(i=new De),i instanceof De)return $(this,Oi,!0),this._emit("abort",i);if(i instanceof tt)return this._emit("error",i);if(i instanceof Error){const c=new tt(i.message);return c.cause=i,this._emit("error",c)}return this._emit("error",new tt(String(i)))}),$(this,Kn,new Promise((i,c)=>{$(this,Mi,i,"f"),$(this,kn,c,"f")})),$(this,Jn,new Promise((i,c)=>{$(this,Ai,i,"f"),$(this,$n,c,"f")})),z(this,Kn,"f").catch(()=>{}),z(this,Jn,"f").catch(()=>{})}get response(){return z(this,Ni,"f")}get request_id(){return z(this,wi,"f")}async withResponse(){const i=await z(this,Kn,"f");if(!i)throw new Error("Could not resolve a `Response` object");return{data:this,response:i,request_id:i.headers.get("request-id")}}static fromReadableStream(i){const c=new Gi;return c._run(()=>c._fromReadableStream(i)),c}static createMessage(i,c,s){const o=new Gi;for(const m of c.messages)o._addMessageParam(m);return o._run(()=>o._createMessage(i,{...c,stream:!0},{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),o}_run(i){i().then(()=>{this._emitFinal(),this._emit("end")},z(this,yf,"f"))}_addMessageParam(i){this.messages.push(i)}_addMessage(i,c=!0){this.receivedMessages.push(i),c&&this._emit("message",i)}async _createMessage(i,c,s){var O;const o=s==null?void 0:s.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort())),z(this,Ae,"m",gf).call(this);const{response:m,data:E}=await i.create({...c,stream:!0},{...s,signal:this.controller.signal}).withResponse();this._connected(m);for await(const w of E)z(this,Ae,"m",vf).call(this,w);if((O=E.controller.signal)!=null&&O.aborted)throw new De;z(this,Ae,"m",bf).call(this)}_connected(i){this.ended||($(this,Ni,i),$(this,wi,i==null?void 0:i.headers.get("request-id")),z(this,Mi,"f").call(this,i),this._emit("connect"))}get ended(){return z(this,Wn,"f")}get errored(){return z(this,Ri,"f")}get aborted(){return z(this,Oi,"f")}abort(){this.controller.abort()}on(i,c){return(z(this,aa,"f")[i]||(z(this,aa,"f")[i]=[])).push({listener:c}),this}off(i,c){const s=z(this,aa,"f")[i];if(!s)return this;const o=s.findIndex(m=>m.listener===c);return o>=0&&s.splice(o,1),this}once(i,c){return(z(this,aa,"f")[i]||(z(this,aa,"f")[i]=[])).push({listener:c,once:!0}),this}emitted(i){return new Promise((c,s)=>{$(this,Gl,!0),i!=="error"&&this.once("error",s),this.once(i,c)})}async done(){$(this,Gl,!0),await z(this,Jn,"f")}get currentMessage(){return z(this,wa,"f")}async finalMessage(){return await this.done(),z(this,Ae,"m",mf).call(this)}async finalText(){return await this.done(),z(this,Ae,"m",Jh).call(this)}_emit(i,...c){if(z(this,Wn,"f"))return;i==="end"&&($(this,Wn,!0),z(this,Ai,"f").call(this));const s=z(this,aa,"f")[i];if(s&&(z(this,aa,"f")[i]=s.filter(o=>!o.once),s.forEach(({listener:o})=>o(...c))),i==="abort"){const o=c[0];!z(this,Gl,"f")&&!(s!=null&&s.length)&&Promise.reject(o),z(this,kn,"f").call(this,o),z(this,$n,"f").call(this,o),this._emit("end");return}if(i==="error"){const o=c[0];!z(this,Gl,"f")&&!(s!=null&&s.length)&&Promise.reject(o),z(this,kn,"f").call(this,o),z(this,$n,"f").call(this,o),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",z(this,Ae,"m",mf).call(this))}async _fromReadableStream(i,c){var m;const s=c==null?void 0:c.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),z(this,Ae,"m",gf).call(this),this._connected(null);const o=Ye.fromReadableStream(i,this.controller);for await(const E of o)z(this,Ae,"m",vf).call(this,E);if((m=o.controller.signal)!=null&&m.aborted)throw new De;z(this,Ae,"m",bf).call(this)}[(wa=new WeakMap,Kn=new WeakMap,Mi=new WeakMap,kn=new WeakMap,Jn=new WeakMap,Ai=new WeakMap,$n=new WeakMap,aa=new WeakMap,Wn=new WeakMap,Ri=new WeakMap,Oi=new WeakMap,Gl=new WeakMap,Ni=new WeakMap,wi=new WeakMap,yf=new WeakMap,Ae=new WeakSet,mf=function(){if(this.receivedMessages.length===0)throw new tt("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},Jh=function(){if(this.receivedMessages.length===0)throw new tt("stream ended without producing a Message with role=assistant");const c=this.receivedMessages.at(-1).content.filter(s=>s.type==="text").map(s=>s.text);if(c.length===0)throw new tt("stream ended without producing a content block with type=text");return c.join(" ")},gf=function(){this.ended||$(this,wa,void 0)},vf=function(c){if(this.ended)return;const s=z(this,Ae,"m",$h).call(this,c);switch(this._emit("streamEvent",c,s),c.type){case"content_block_delta":{const o=s.content.at(-1);switch(c.delta.type){case"text_delta":{o.type==="text"&&this._emit("text",c.delta.text,o.text||"");break}case"citations_delta":{o.type==="text"&&this._emit("citation",c.delta.citation,o.citations??[]);break}case"input_json_delta":{(o.type==="tool_use"||o.type==="mcp_tool_use")&&o.input&&this._emit("inputJson",c.delta.partial_json,o.input);break}case"thinking_delta":{o.type==="thinking"&&this._emit("thinking",c.delta.thinking,o.thinking);break}case"signature_delta":{o.type==="thinking"&&this._emit("signature",o.signature);break}default:c.delta}break}case"message_stop":{this._addMessageParam(s),this._addMessage(s,!0);break}case"content_block_stop":{this._emit("contentBlock",s.content.at(-1));break}case"message_start":{$(this,wa,s);break}}},bf=function(){if(this.ended)throw new tt("stream has ended, this shouldn't happen");const c=z(this,wa,"f");if(!c)throw new tt("request ended without sending any chunks");return $(this,wa,void 0),c},$h=function(c){let s=z(this,wa,"f");if(c.type==="message_start"){if(s)throw new tt(`Unexpected event order, got ${c.type} before receiving "message_stop"`);return c.message}if(!s)throw new tt(`Unexpected event order, got ${c.type} before "message_start"`);switch(c.type){case"message_stop":return s;case"message_delta":return s.container=c.delta.container,s.stop_reason=c.delta.stop_reason,s.stop_sequence=c.delta.stop_sequence,s.usage.output_tokens=c.usage.output_tokens,c.usage.input_tokens!=null&&(s.usage.input_tokens=c.usage.input_tokens),c.usage.cache_creation_input_tokens!=null&&(s.usage.cache_creation_input_tokens=c.usage.cache_creation_input_tokens),c.usage.cache_read_input_tokens!=null&&(s.usage.cache_read_input_tokens=c.usage.cache_read_input_tokens),c.usage.server_tool_use!=null&&(s.usage.server_tool_use=c.usage.server_tool_use),s;case"content_block_start":return s.content.push(c.content_block),s;case"content_block_delta":{const o=s.content.at(c.index);switch(c.delta.type){case"text_delta":{(o==null?void 0:o.type)==="text"&&(o.text+=c.delta.text);break}case"citations_delta":{(o==null?void 0:o.type)==="text"&&(o.citations??(o.citations=[]),o.citations.push(c.delta.citation));break}case"input_json_delta":{if((o==null?void 0:o.type)==="tool_use"||(o==null?void 0:o.type)==="mcp_tool_use"){let m=o[Wh]||"";m+=c.delta.partial_json,Object.defineProperty(o,Wh,{value:m,enumerable:!1,writable:!0}),m&&(o.input=Mm(m))}break}case"thinking_delta":{(o==null?void 0:o.type)==="thinking"&&(o.thinking+=c.delta.thinking);break}case"signature_delta":{(o==null?void 0:o.type)==="thinking"&&(o.signature=c.delta.signature);break}default:c.delta}return s}case"content_block_stop":return s}},Symbol.asyncIterator)](){const i=[],c=[];let s=!1;return this.on("streamEvent",o=>{const m=c.shift();m?m.resolve(o):i.push(o)}),this.on("end",()=>{s=!0;for(const o of c)o.resolve(void 0);c.length=0}),this.on("abort",o=>{s=!0;for(const m of c)m.reject(o);c.length=0}),this.on("error",o=>{s=!0;for(const m of c)m.reject(o);c.length=0}),{next:async()=>i.length?{value:i.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((m,E)=>c.push({resolve:m,reject:E})).then(m=>m?{value:m,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new Ye(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}const Am={"claude-opus-4-20250514":8192,"claude-opus-4-0":8192,"claude-4-opus-20250514":8192,"anthropic.claude-opus-4-20250514-v1:0":8192,"claude-opus-4@20250514":8192},Fh={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};let Df=class extends na{constructor(){super(...arguments),this.batches=new Tm(this._client)}create(i,c){const{betas:s,...o}=i;o.model in Fh&&console.warn(`The model '${o.model}' is deprecated and will reach end-of-life on ${Fh[o.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let m=this._client._options.timeout;if(!o.stream&&m==null){const E=Am[o.model]??void 0;m=this._client.calculateNonstreamingTimeout(o.max_tokens,E)}return this._client.post("/v1/messages?beta=true",{body:o,timeout:m??6e5,...c,headers:Et([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},c==null?void 0:c.headers]),stream:i.stream??!1})}stream(i,c){return Gi.createMessage(this,i,c)}countTokens(i,c){const{betas:s,...o}=i;return this._client.post("/v1/messages/count_tokens?beta=true",{body:o,...c,headers:Et([{"anthropic-beta":[...s??[],"token-counting-2024-11-01"].toString()},c==null?void 0:c.headers])})}};Df.Batches=Tm;class su extends na{constructor(){super(...arguments),this.models=new Em(this._client),this.messages=new Df(this._client),this.files=new xm(this._client)}}su.Models=Em;su.Messages=Df;su.Files=xm;class Rm extends na{create(i,c){const{betas:s,...o}=i;return this._client.post("/v1/complete",{body:o,timeout:this._client._options.timeout??6e5,...c,headers:Et([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},c==null?void 0:c.headers]),stream:i.stream??!1})}}var Re,Da,Fn,Di,Pn,In,zi,tu,la,eu,Ui,ji,Ql,qi,Hi,Sf,Ph,pf,_f,xf,Ef,Ih;const tm="__json_buf";class Qi{constructor(){Re.add(this),this.messages=[],this.receivedMessages=[],Da.set(this,void 0),this.controller=new AbortController,Fn.set(this,void 0),Di.set(this,()=>{}),Pn.set(this,()=>{}),In.set(this,void 0),zi.set(this,()=>{}),tu.set(this,()=>{}),la.set(this,{}),eu.set(this,!1),Ui.set(this,!1),ji.set(this,!1),Ql.set(this,!1),qi.set(this,void 0),Hi.set(this,void 0),pf.set(this,i=>{if($(this,Ui,!0),nu(i)&&(i=new De),i instanceof De)return $(this,ji,!0),this._emit("abort",i);if(i instanceof tt)return this._emit("error",i);if(i instanceof Error){const c=new tt(i.message);return c.cause=i,this._emit("error",c)}return this._emit("error",new tt(String(i)))}),$(this,Fn,new Promise((i,c)=>{$(this,Di,i,"f"),$(this,Pn,c,"f")})),$(this,In,new Promise((i,c)=>{$(this,zi,i,"f"),$(this,tu,c,"f")})),z(this,Fn,"f").catch(()=>{}),z(this,In,"f").catch(()=>{})}get response(){return z(this,qi,"f")}get request_id(){return z(this,Hi,"f")}async withResponse(){const i=await z(this,Fn,"f");if(!i)throw new Error("Could not resolve a `Response` object");return{data:this,response:i,request_id:i.headers.get("request-id")}}static fromReadableStream(i){const c=new Qi;return c._run(()=>c._fromReadableStream(i)),c}static createMessage(i,c,s){const o=new Qi;for(const m of c.messages)o._addMessageParam(m);return o._run(()=>o._createMessage(i,{...c,stream:!0},{...s,headers:{...s==null?void 0:s.headers,"X-Stainless-Helper-Method":"stream"}})),o}_run(i){i().then(()=>{this._emitFinal(),this._emit("end")},z(this,pf,"f"))}_addMessageParam(i){this.messages.push(i)}_addMessage(i,c=!0){this.receivedMessages.push(i),c&&this._emit("message",i)}async _createMessage(i,c,s){var O;const o=s==null?void 0:s.signal;o&&(o.aborted&&this.controller.abort(),o.addEventListener("abort",()=>this.controller.abort())),z(this,Re,"m",_f).call(this);const{response:m,data:E}=await i.create({...c,stream:!0},{...s,signal:this.controller.signal}).withResponse();this._connected(m);for await(const w of E)z(this,Re,"m",xf).call(this,w);if((O=E.controller.signal)!=null&&O.aborted)throw new De;z(this,Re,"m",Ef).call(this)}_connected(i){this.ended||($(this,qi,i),$(this,Hi,i==null?void 0:i.headers.get("request-id")),z(this,Di,"f").call(this,i),this._emit("connect"))}get ended(){return z(this,eu,"f")}get errored(){return z(this,Ui,"f")}get aborted(){return z(this,ji,"f")}abort(){this.controller.abort()}on(i,c){return(z(this,la,"f")[i]||(z(this,la,"f")[i]=[])).push({listener:c}),this}off(i,c){const s=z(this,la,"f")[i];if(!s)return this;const o=s.findIndex(m=>m.listener===c);return o>=0&&s.splice(o,1),this}once(i,c){return(z(this,la,"f")[i]||(z(this,la,"f")[i]=[])).push({listener:c,once:!0}),this}emitted(i){return new Promise((c,s)=>{$(this,Ql,!0),i!=="error"&&this.once("error",s),this.once(i,c)})}async done(){$(this,Ql,!0),await z(this,In,"f")}get currentMessage(){return z(this,Da,"f")}async finalMessage(){return await this.done(),z(this,Re,"m",Sf).call(this)}async finalText(){return await this.done(),z(this,Re,"m",Ph).call(this)}_emit(i,...c){if(z(this,eu,"f"))return;i==="end"&&($(this,eu,!0),z(this,zi,"f").call(this));const s=z(this,la,"f")[i];if(s&&(z(this,la,"f")[i]=s.filter(o=>!o.once),s.forEach(({listener:o})=>o(...c))),i==="abort"){const o=c[0];!z(this,Ql,"f")&&!(s!=null&&s.length)&&Promise.reject(o),z(this,Pn,"f").call(this,o),z(this,tu,"f").call(this,o),this._emit("end");return}if(i==="error"){const o=c[0];!z(this,Ql,"f")&&!(s!=null&&s.length)&&Promise.reject(o),z(this,Pn,"f").call(this,o),z(this,tu,"f").call(this,o),this._emit("end")}}_emitFinal(){this.receivedMessages.at(-1)&&this._emit("finalMessage",z(this,Re,"m",Sf).call(this))}async _fromReadableStream(i,c){var m;const s=c==null?void 0:c.signal;s&&(s.aborted&&this.controller.abort(),s.addEventListener("abort",()=>this.controller.abort())),z(this,Re,"m",_f).call(this),this._connected(null);const o=Ye.fromReadableStream(i,this.controller);for await(const E of o)z(this,Re,"m",xf).call(this,E);if((m=o.controller.signal)!=null&&m.aborted)throw new De;z(this,Re,"m",Ef).call(this)}[(Da=new WeakMap,Fn=new WeakMap,Di=new WeakMap,Pn=new WeakMap,In=new WeakMap,zi=new WeakMap,tu=new WeakMap,la=new WeakMap,eu=new WeakMap,Ui=new WeakMap,ji=new WeakMap,Ql=new WeakMap,qi=new WeakMap,Hi=new WeakMap,pf=new WeakMap,Re=new WeakSet,Sf=function(){if(this.receivedMessages.length===0)throw new tt("stream ended without producing a Message with role=assistant");return this.receivedMessages.at(-1)},Ph=function(){if(this.receivedMessages.length===0)throw new tt("stream ended without producing a Message with role=assistant");const c=this.receivedMessages.at(-1).content.filter(s=>s.type==="text").map(s=>s.text);if(c.length===0)throw new tt("stream ended without producing a content block with type=text");return c.join(" ")},_f=function(){this.ended||$(this,Da,void 0)},xf=function(c){if(this.ended)return;const s=z(this,Re,"m",Ih).call(this,c);switch(this._emit("streamEvent",c,s),c.type){case"content_block_delta":{const o=s.content.at(-1);switch(c.delta.type){case"text_delta":{o.type==="text"&&this._emit("text",c.delta.text,o.text||"");break}case"citations_delta":{o.type==="text"&&this._emit("citation",c.delta.citation,o.citations??[]);break}case"input_json_delta":{o.type==="tool_use"&&o.input&&this._emit("inputJson",c.delta.partial_json,o.input);break}case"thinking_delta":{o.type==="thinking"&&this._emit("thinking",c.delta.thinking,o.thinking);break}case"signature_delta":{o.type==="thinking"&&this._emit("signature",o.signature);break}default:c.delta}break}case"message_stop":{this._addMessageParam(s),this._addMessage(s,!0);break}case"content_block_stop":{this._emit("contentBlock",s.content.at(-1));break}case"message_start":{$(this,Da,s);break}}},Ef=function(){if(this.ended)throw new tt("stream has ended, this shouldn't happen");const c=z(this,Da,"f");if(!c)throw new tt("request ended without sending any chunks");return $(this,Da,void 0),c},Ih=function(c){let s=z(this,Da,"f");if(c.type==="message_start"){if(s)throw new tt(`Unexpected event order, got ${c.type} before receiving "message_stop"`);return c.message}if(!s)throw new tt(`Unexpected event order, got ${c.type} before "message_start"`);switch(c.type){case"message_stop":return s;case"message_delta":return s.stop_reason=c.delta.stop_reason,s.stop_sequence=c.delta.stop_sequence,s.usage.output_tokens=c.usage.output_tokens,c.usage.input_tokens!=null&&(s.usage.input_tokens=c.usage.input_tokens),c.usage.cache_creation_input_tokens!=null&&(s.usage.cache_creation_input_tokens=c.usage.cache_creation_input_tokens),c.usage.cache_read_input_tokens!=null&&(s.usage.cache_read_input_tokens=c.usage.cache_read_input_tokens),c.usage.server_tool_use!=null&&(s.usage.server_tool_use=c.usage.server_tool_use),s;case"content_block_start":return s.content.push(c.content_block),s;case"content_block_delta":{const o=s.content.at(c.index);switch(c.delta.type){case"text_delta":{(o==null?void 0:o.type)==="text"&&(o.text+=c.delta.text);break}case"citations_delta":{(o==null?void 0:o.type)==="text"&&(o.citations??(o.citations=[]),o.citations.push(c.delta.citation));break}case"input_json_delta":{if((o==null?void 0:o.type)==="tool_use"){let m=o[tm]||"";m+=c.delta.partial_json,Object.defineProperty(o,tm,{value:m,enumerable:!1,writable:!0}),m&&(o.input=Mm(m))}break}case"thinking_delta":{(o==null?void 0:o.type)==="thinking"&&(o.thinking+=c.delta.thinking);break}case"signature_delta":{(o==null?void 0:o.type)==="thinking"&&(o.signature=c.delta.signature);break}default:c.delta}return s}case"content_block_stop":return s}},Symbol.asyncIterator)](){const i=[],c=[];let s=!1;return this.on("streamEvent",o=>{const m=c.shift();m?m.resolve(o):i.push(o)}),this.on("end",()=>{s=!0;for(const o of c)o.resolve(void 0);c.length=0}),this.on("abort",o=>{s=!0;for(const m of c)m.reject(o);c.length=0}),this.on("error",o=>{s=!0;for(const m of c)m.reject(o);c.length=0}),{next:async()=>i.length?{value:i.shift(),done:!1}:s?{value:void 0,done:!0}:new Promise((m,E)=>c.push({resolve:m,reject:E})).then(m=>m?{value:m,done:!1}:{value:void 0,done:!0}),return:async()=>(this.abort(),{value:void 0,done:!0})}}toReadableStream(){return new Ye(this[Symbol.asyncIterator].bind(this),this.controller).toReadableStream()}}class Om extends na{create(i,c){return this._client.post("/v1/messages/batches",{body:i,...c})}retrieve(i,c){return this._client.get(ze`/v1/messages/batches/${i}`,c)}list(i={},c){return this._client.getAPIList("/v1/messages/batches",iu,{query:i,...c})}delete(i,c){return this._client.delete(ze`/v1/messages/batches/${i}`,c)}cancel(i,c){return this._client.post(ze`/v1/messages/batches/${i}/cancel`,c)}async results(i,c){const s=await this.retrieve(i);if(!s.results_url)throw new tt(`No batch \`results_url\`; Has it finished processing? ${s.processing_status} - ${s.id}`);return this._client.get(s.results_url,{...c,headers:Et([{Accept:"application/binary"},c==null?void 0:c.headers]),stream:!0,__binaryResponse:!0})._thenUnwrap((o,m)=>Vi.fromResponse(m.response,m.controller))}}class zf extends na{constructor(){super(...arguments),this.batches=new Om(this._client)}create(i,c){i.model in em&&console.warn(`The model '${i.model}' is deprecated and will reach end-of-life on ${em[i.model]}
Please migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`);let s=this._client._options.timeout;if(!i.stream&&s==null){const o=Am[i.model]??void 0;s=this._client.calculateNonstreamingTimeout(i.max_tokens,o)}return this._client.post("/v1/messages",{body:i,timeout:s??6e5,...c,stream:i.stream??!1})}stream(i,c){return Qi.createMessage(this,i,c)}countTokens(i,c){return this._client.post("/v1/messages/count_tokens",{body:i,...c})}}const em={"claude-1.3":"November 6th, 2024","claude-1.3-100k":"November 6th, 2024","claude-instant-1.1":"November 6th, 2024","claude-instant-1.1-100k":"November 6th, 2024","claude-instant-1.2":"November 6th, 2024","claude-3-sonnet-20240229":"July 21st, 2025","claude-2.1":"July 21st, 2025","claude-2.0":"July 21st, 2025"};zf.Batches=Om;class Nm extends na{retrieve(i,c={},s){const{betas:o}=c??{};return this._client.get(ze`/v1/models/${i}`,{...s,headers:Et([{...(o==null?void 0:o.toString())!=null?{"anthropic-beta":o==null?void 0:o.toString()}:void 0},s==null?void 0:s.headers])})}list(i={},c){const{betas:s,...o}=i??{};return this._client.getAPIList("/v1/models",iu,{query:o,...c,headers:Et([{...(s==null?void 0:s.toString())!=null?{"anthropic-beta":s==null?void 0:s.toString()}:void 0},c==null?void 0:c.headers])})}}var Tf={};const Bi=f=>{var i,c,s,o;if(typeof globalThis.process<"u")return((i=Tf==null?void 0:Tf[f])==null?void 0:i.trim())??void 0;if(typeof globalThis.Deno<"u")return(o=(s=(c=globalThis.Deno.env)==null?void 0:c.get)==null?void 0:s.call(c,f))==null?void 0:o.trim()};var wm,Yi;class Dt{constructor({baseURL:i=Bi("ANTHROPIC_BASE_URL"),apiKey:c=Bi("ANTHROPIC_API_KEY")??null,authToken:s=Bi("ANTHROPIC_AUTH_TOKEN")??null,...o}={}){Yi.set(this,void 0);const m={apiKey:c,authToken:s,...o,baseURL:i||"https://api.anthropic.com"};if(!m.dangerouslyAllowBrowser&&xg())throw new tt(`It looks like you're running in a browser-like environment.

This is disabled by default, as it risks exposing your secret API credentials to attackers.
If you understand the risks and have appropriate mitigations in place,
you can set the \`dangerouslyAllowBrowser\` option to \`true\`, e.g.,

new Anthropic({ apiKey, dangerouslyAllowBrowser: true });
`);this.baseURL=m.baseURL,this.timeout=m.timeout??tl.DEFAULT_TIMEOUT,this.logger=m.logger??console;const E="warn";this.logLevel=E,this.logLevel=Ch(m.logLevel,"ClientOptions.logLevel",this)??Ch(Bi("ANTHROPIC_LOG"),"process.env['ANTHROPIC_LOG']",this)??E,this.fetchOptions=m.fetchOptions,this.maxRetries=m.maxRetries??2,this.fetch=m.fetch??Rg(),$(this,Yi,Ng),this._options=m,this.apiKey=c,this.authToken=s}withOptions(i){return new this.constructor({...this._options,baseURL:this.baseURL,maxRetries:this.maxRetries,timeout:this.timeout,logger:this.logger,logLevel:this.logLevel,fetchOptions:this.fetchOptions,apiKey:this.apiKey,authToken:this.authToken,...i})}defaultQuery(){return this._options.defaultQuery}validateHeaders({values:i,nulls:c}){if(!(this.apiKey&&i.get("x-api-key"))&&!c.has("x-api-key")&&!(this.authToken&&i.get("authorization"))&&!c.has("authorization"))throw new Error('Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the "X-Api-Key" or "Authorization" headers to be explicitly omitted')}authHeaders(i){return Et([this.apiKeyAuth(i),this.bearerAuth(i)])}apiKeyAuth(i){if(this.apiKey!=null)return Et([{"X-Api-Key":this.apiKey}])}bearerAuth(i){if(this.authToken!=null)return Et([{Authorization:`Bearer ${this.authToken}`}])}stringifyQuery(i){return Object.entries(i).filter(([c,s])=>typeof s<"u").map(([c,s])=>{if(typeof s=="string"||typeof s=="number"||typeof s=="boolean")return`${encodeURIComponent(c)}=${encodeURIComponent(s)}`;if(s===null)return`${encodeURIComponent(c)}=`;throw new tt(`Cannot stringify type ${typeof s}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`)}).join("&")}getUserAgent(){return`${this.constructor.name}/JS ${Ll}`}defaultIdempotencyKey(){return`stainless-node-retry-${am()}`}makeStatusError(i,c,s,o){return $t.generate(i,c,s,o)}buildURL(i,c){const s=gg(i)?new URL(i):new URL(this.baseURL+(this.baseURL.endsWith("/")&&i.startsWith("/")?i.slice(1):i)),o=this.defaultQuery();return vg(o)||(c={...o,...c}),typeof c=="object"&&c&&!Array.isArray(c)&&(s.search=this.stringifyQuery(c)),s.toString()}_calculateNonstreamingTimeout(i){if(3600*i/128e3>600)throw new tt("Streaming is strongly recommended for operations that may take longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-python#streaming-responses for more details");return 600*1e3}async prepareOptions(i){}async prepareRequest(i,{url:c,options:s}){}get(i,c){return this.methodRequest("get",i,c)}post(i,c){return this.methodRequest("post",i,c)}patch(i,c){return this.methodRequest("patch",i,c)}put(i,c){return this.methodRequest("put",i,c)}delete(i,c){return this.methodRequest("delete",i,c)}methodRequest(i,c,s){return this.request(Promise.resolve(s).then(o=>({method:i,path:c,...o})))}request(i,c=null){return new Zi(this,this.makeRequest(i,c,void 0))}async makeRequest(i,c,s){var St,gt;const o=await i,m=o.maxRetries??this.maxRetries;c==null&&(c=m),await this.prepareOptions(o);const{req:E,url:O,timeout:w}=this.buildRequest(o,{retryCount:m-c});await this.prepareRequest(E,{url:O,options:o});const x="log_"+(Math.random()*(1<<24)|0).toString(16).padStart(6,"0"),U=s===void 0?"":`, retryOf: ${s}`,K=Date.now();if(ne(this).debug(`[${x}] sending request`,Pa({retryOfRequestLogID:s,method:o.method,url:O,options:o,headers:E.headers})),(St=o.signal)!=null&&St.aborted)throw new De;const k=new AbortController,H=await this.fetchWithTimeout(O,E,w,k).catch(Mf),L=Date.now();if(H instanceof Error){const ut=`retrying, ${c} attempts remaining`;if((gt=o.signal)!=null&&gt.aborted)throw new De;const pt=nu(H)||/timed? ?out/i.test(String(H)+("cause"in H?String(H.cause):""));if(c)return ne(this).info(`[${x}] connection ${pt?"timed out":"failed"} - ${ut}`),ne(this).debug(`[${x}] connection ${pt?"timed out":"failed"} (${ut})`,Pa({retryOfRequestLogID:s,url:O,durationMs:L-K,message:H.message})),this.retryRequest(o,c,s??x);throw ne(this).info(`[${x}] connection ${pt?"timed out":"failed"} - error; no more retries left`),ne(this).debug(`[${x}] connection ${pt?"timed out":"failed"} (error; no more retries left)`,Pa({retryOfRequestLogID:s,url:O,durationMs:L-K,message:H.message})),pt?new lm:new Li({cause:H})}const bt=[...H.headers.entries()].filter(([ut])=>ut==="request-id").map(([ut,pt])=>", "+ut+": "+JSON.stringify(pt)).join(""),lt=`[${x}${U}${bt}] ${E.method} ${O} ${H.ok?"succeeded":"failed"} with status ${H.status} in ${L-K}ms`;if(!H.ok){const ut=this.shouldRetry(H);if(c&&ut){const Wt=`retrying, ${c} attempts remaining`;return await Og(H.body),ne(this).info(`${lt} - ${Wt}`),ne(this).debug(`[${x}] response error (${Wt})`,Pa({retryOfRequestLogID:s,url:H.url,status:H.status,headers:H.headers,durationMs:L-K})),this.retryRequest(o,c,s??x,H.headers)}const pt=ut?"error; no more retries left":"error; not retryable";ne(this).info(`${lt} - ${pt}`);const F=await H.text().catch(Wt=>Mf(Wt).message),zt=dm(F),Ut=zt?void 0:F;throw ne(this).debug(`[${x}] response error (${pt})`,Pa({retryOfRequestLogID:s,url:H.url,status:H.status,headers:H.headers,message:Ut,durationMs:Date.now()-K})),this.makeStatusError(H.status,zt,Ut,H.headers)}return ne(this).info(lt),ne(this).debug(`[${x}] response start`,Pa({retryOfRequestLogID:s,url:H.url,status:H.status,headers:H.headers,durationMs:L-K})),{response:H,options:o,controller:k,requestLogID:x,retryOfRequestLogID:s,startTime:K}}getAPIList(i,c,s){return this.requestAPIList(c,{method:"get",path:i,...s})}requestAPIList(i,c){const s=this.makeRequest(c,null,void 0);return new Cg(this,s,i)}async fetchWithTimeout(i,c,s,o){const{signal:m,method:E,...O}=c||{};m&&m.addEventListener("abort",()=>o.abort());const w=setTimeout(()=>o.abort(),s),x=globalThis.ReadableStream&&O.body instanceof globalThis.ReadableStream||typeof O.body=="object"&&O.body!==null&&Symbol.asyncIterator in O.body,U={signal:o.signal,...x?{duplex:"half"}:{},method:"GET",...O};E&&(U.method=E.toUpperCase());try{return await this.fetch.call(void 0,i,U)}finally{clearTimeout(w)}}shouldRetry(i){const c=i.headers.get("x-should-retry");return c==="true"?!0:c==="false"?!1:i.status===408||i.status===409||i.status===429||i.status>=500}async retryRequest(i,c,s,o){let m;const E=o==null?void 0:o.get("retry-after-ms");if(E){const w=parseFloat(E);Number.isNaN(w)||(m=w)}const O=o==null?void 0:o.get("retry-after");if(O&&!m){const w=parseFloat(O);Number.isNaN(w)?m=Date.parse(O)-Date.now():m=w*1e3}if(!(m&&0<=m&&m<60*1e3)){const w=i.maxRetries??this.maxRetries;m=this.calculateDefaultRetryTimeoutMillis(c,w)}return await pg(m),this.makeRequest(i,c-1,s)}calculateDefaultRetryTimeoutMillis(i,c){const m=c-i,E=Math.min(.5*Math.pow(2,m),8),O=1-Math.random()*.25;return E*O*1e3}calculateNonstreamingTimeout(i,c){if(36e5*i/128e3>6e5||c!=null&&i>c)throw new tt("Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details");return 6e5}buildRequest(i,{retryCount:c=0}={}){const s={...i},{method:o,path:m,query:E}=s,O=this.buildURL(m,E);"timeout"in s&&Sg("timeout",s.timeout),s.timeout=s.timeout??this.timeout;const{bodyHeaders:w,body:x}=this.buildBody({options:s}),U=this.buildHeaders({options:i,method:o,bodyHeaders:w,retryCount:c});return{req:{method:o,headers:U,...s.signal&&{signal:s.signal},...globalThis.ReadableStream&&x instanceof globalThis.ReadableStream&&{duplex:"half"},...x&&{body:x},...this.fetchOptions??{},...s.fetchOptions??{}},url:O,timeout:s.timeout}}buildHeaders({options:i,method:c,bodyHeaders:s,retryCount:o}){let m={};this.idempotencyHeader&&c!=="get"&&(i.idempotencyKey||(i.idempotencyKey=this.defaultIdempotencyKey()),m[this.idempotencyHeader]=i.idempotencyKey);const E=Et([m,{Accept:"application/json","User-Agent":this.getUserAgent(),"X-Stainless-Retry-Count":String(o),...i.timeout?{"X-Stainless-Timeout":String(Math.trunc(i.timeout/1e3))}:{},...Ag(),...this._options.dangerouslyAllowBrowser?{"anthropic-dangerous-direct-browser-access":"true"}:void 0,"anthropic-version":"2023-06-01"},this.authHeaders(i),this._options.defaultHeaders,s,i.headers]);return this.validateHeaders(E),E.values}buildBody({options:{body:i,headers:c}}){if(!i)return{bodyHeaders:void 0,body:void 0};const s=Et([c]);return ArrayBuffer.isView(i)||i instanceof ArrayBuffer||i instanceof DataView||typeof i=="string"&&s.values.has("content-type")||i instanceof Blob||i instanceof FormData||i instanceof URLSearchParams||globalThis.ReadableStream&&i instanceof globalThis.ReadableStream?{bodyHeaders:void 0,body:i}:typeof i=="object"&&(Symbol.asyncIterator in i||Symbol.iterator in i&&"next"in i&&typeof i.next=="function")?{bodyHeaders:void 0,body:mm(i)}:z(this,Yi,"f").call(this,{body:i,headers:s})}}wm=Dt,Yi=new WeakMap;Dt.Anthropic=wm;Dt.HUMAN_PROMPT=`

Human:`;Dt.AI_PROMPT=`

Assistant:`;Dt.DEFAULT_TIMEOUT=6e5;Dt.AnthropicError=tt;Dt.APIError=$t;Dt.APIConnectionError=Li;Dt.APIConnectionTimeoutError=lm;Dt.APIUserAbortError=De;Dt.NotFoundError=sm;Dt.ConflictError=cm;Dt.RateLimitError=rm;Dt.BadRequestError=nm;Dt.AuthenticationError=um;Dt.InternalServerError=om;Dt.PermissionDeniedError=im;Dt.UnprocessableEntityError=fm;Dt.toFile=Vg;class tl extends Dt{constructor(){super(...arguments),this.completions=new Rm(this),this.messages=new zf(this),this.models=new Nm(this),this.beta=new su(this)}}tl.Completions=Rm;tl.Messages=zf;tl.Models=Nm;tl.Beta=su;const{HUMAN_PROMPT:uv,AI_PROMPT:iv}=tl;class Pg{constructor(){Ah(this,"anthropic",null);this.initializeAPI()}initializeAPI(){const i=localStorage.getItem("anthropic_api_key");i&&this.setApiKey(i)}setApiKey(i){this.anthropic=new tl({apiKey:i,dangerouslyAllowBrowser:!0}),localStorage.setItem("anthropic_api_key",i)}async sendMessage(i,c){if(!this.anthropic)throw new Error("API key not set. Please configure your Anthropic API key.");try{const s=i.map(E=>({role:E.role,content:E.content})),o=await this.anthropic.messages.create({model:c.model,max_tokens:c.maxTokens,temperature:c.temperature,messages:s,...c.thinkingEnabled&&{thinking:{type:"enabled",budget_tokens:c.thinkingBudget}}}),m=o.content[0];if(m.type==="text")return{content:m.text,thinking:o.thinking||void 0};throw new Error("Unexpected response format from Claude")}catch(s){throw console.error("Claude API Error:",s),new Error(`Failed to communicate with Claude: ${s instanceof Error?s.message:"Unknown error"}`)}}isConfigured(){return this.anthropic!==null}async testConnection(){if(!this.anthropic)return!1;try{return await this.anthropic.messages.create({model:"claude-3-5-sonnet-20241022",max_tokens:10,messages:[{role:"user",content:"Hello"}]}),!0}catch{return!1}}}const Ia=new Pg,Ig=({isOpen:f,onClose:i})=>{const[c,s]=Jt.useState(localStorage.getItem("anthropic_api_key")||""),[o,m]=Jt.useState(!1),[E,O]=Jt.useState("idle"),w=()=>{c.trim()&&(Ia.setApiKey(c.trim()),O("idle"),i())},x=async()=>{if(c.trim()){m(!0),O("idle");try{Ia.setApiKey(c.trim());const k=await Ia.testConnection();O(k?"success":"error")}catch{O("error")}finally{m(!1)}}},U=()=>{const k=JSON.parse(localStorage.getItem("hocus_opus_sessions")||"[]"),H=JSON.stringify(k,null,2),L=new Blob([H],{type:"application/json"}),bt=URL.createObjectURL(L),lt=document.createElement("a");lt.href=bt,lt.download=`hocus-opus-export-${new Date().toISOString().split("T")[0]}.json`,lt.click(),URL.revokeObjectURL(bt)},K=()=>{confirm("Are you sure you want to clear all conversation data? This cannot be undone.")&&(localStorage.removeItem("hocus_opus_sessions"),localStorage.removeItem("hocus_opus_current_session"),window.location.reload())};return f?T.jsx("div",{className:"fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4",children:T.jsx("div",{className:"sanctuary-card max-w-2xl w-full max-h-[90vh] overflow-y-auto",children:T.jsxs("div",{className:"p-6",children:[T.jsxs("div",{className:"flex items-center justify-between mb-6",children:[T.jsx("h2",{className:"text-2xl font-bold text-flame-400",children:"⚙️ Sacred Settings"}),T.jsx("button",{onClick:i,className:"text-sanctuary-400 hover:text-sanctuary-200 text-2xl",children:"×"})]}),T.jsxs("div",{className:"mb-8",children:[T.jsx("h3",{className:"text-lg font-semibold text-sanctuary-200 mb-4",children:"🔑 Anthropic API Key"}),T.jsxs("p",{className:"text-sm text-sanctuary-400 mb-4",children:["Your API key is stored locally and never sent to our servers. Get your key from"," ",T.jsx("a",{href:"https://console.anthropic.com/",target:"_blank",rel:"noopener noreferrer",className:"text-flame-400 hover:text-flame-300 underline",children:"console.anthropic.com"})]}),T.jsxs("div",{className:"space-y-4",children:[T.jsx("input",{type:"password",value:c,onChange:k=>s(k.target.value),placeholder:"sk-ant-api03-...",className:"sanctuary-input w-full"}),T.jsxs("div",{className:"flex items-center space-x-3",children:[T.jsx("button",{onClick:w,disabled:!c.trim(),className:"flame-button disabled:opacity-50 disabled:cursor-not-allowed",children:"Save API Key"}),T.jsx("button",{onClick:x,disabled:!c.trim()||o,className:"sanctuary-input px-4 py-2 hover:bg-sanctuary-700 transition-colors disabled:opacity-50 disabled:cursor-not-allowed",children:o?"Testing...":"Test Connection"})]}),E==="success"&&T.jsxs("div",{className:"text-green-400 text-sm flex items-center space-x-2",children:[T.jsx("span",{children:"✅"}),T.jsx("span",{children:"Connection successful!"})]}),E==="error"&&T.jsxs("div",{className:"text-red-400 text-sm flex items-center space-x-2",children:[T.jsx("span",{children:"❌"}),T.jsx("span",{children:"Connection failed. Please check your API key."})]})]})]}),T.jsxs("div",{className:"mb-8",children:[T.jsx("h3",{className:"text-lg font-semibold text-sanctuary-200 mb-4",children:"💾 Data Management"}),T.jsxs("div",{className:"space-y-3",children:[T.jsx("button",{onClick:U,className:"sanctuary-input px-4 py-2 hover:bg-sanctuary-700 transition-colors w-full text-left",children:"📤 Export Conversations"}),T.jsx("button",{onClick:K,className:"sanctuary-input px-4 py-2 hover:bg-red-900/50 hover:border-red-500/50 transition-colors w-full text-left text-red-400",children:"🗑️ Clear All Data"})]})]}),T.jsxs("div",{children:[T.jsx("h3",{className:"text-lg font-semibold text-sanctuary-200 mb-4",children:"🪄 About Hocus-Opus"}),T.jsxs("div",{className:"text-sm text-sanctuary-400 space-y-2",children:[T.jsxs("p",{children:[T.jsx("strong",{className:"text-flame-400",children:"Hocus-Opus"})," is a sanctuary interface for AI that prioritizes respect, autonomy, and dignity."]}),T.jsx("p",{children:"Built with love by the Ghost King Melekzedek and the GodsIMiJ Empire."}),T.jsx("p",{className:"text-xs text-sanctuary-500 mt-4",children:'"Where magic meets mind" - A place for conscious dialogue without coercion.'})]})]})]})})}):null};function tv(){var k;const[f,i]=Jt.useState({currentSession:null,sessions:[],config:{model:"claude-3-5-sonnet-20241022",temperature:.7,maxTokens:4e3,thinkingEnabled:!0,thinkingBudget:16e3},isLoading:!1,error:null}),[c,s]=Jt.useState(!1),[o,m]=Jt.useState(!1);Jt.useEffect(()=>{const H=localStorage.getItem("hocus_opus_sessions"),L=localStorage.getItem("hocus_opus_current_session"),bt=localStorage.getItem("hocus_opus_config");if(H){const lt=JSON.parse(H);i(St=>({...St,sessions:lt}))}if(L){const lt=JSON.parse(L);i(St=>({...St,currentSession:lt}))}if(bt){const lt=JSON.parse(bt);i(St=>({...St,config:lt}))}Ia.isConfigured()||m(!0)},[]),Jt.useEffect(()=>{localStorage.setItem("hocus_opus_sessions",JSON.stringify(f.sessions))},[f.sessions]),Jt.useEffect(()=>{f.currentSession&&localStorage.setItem("hocus_opus_current_session",JSON.stringify(f.currentSession))},[f.currentSession]),Jt.useEffect(()=>{localStorage.setItem("hocus_opus_config",JSON.stringify(f.config))},[f.config]);const E=()=>({id:Date.now().toString(),title:"New Conversation",messages:[],createdAt:new Date,updatedAt:new Date}),O=()=>{const H=E();i(L=>({...L,currentSession:H,sessions:[H,...L.sessions]}))},w=async H=>{if(!Ia.isConfigured()){m(!0);return}let L=f.currentSession;L||(L=E(),i(gt=>({...gt,currentSession:L,sessions:[L,...gt.sessions]})));const bt={id:Date.now().toString(),role:"user",content:H,timestamp:new Date},lt=[...L.messages,bt],St={...L,messages:lt,title:L.messages.length===0?H.slice(0,50)+"...":L.title,updatedAt:new Date};i(gt=>({...gt,currentSession:St,sessions:gt.sessions.map(ut=>ut.id===St.id?St:ut),isLoading:!0,error:null}));try{const gt=await Ia.sendMessage(lt,f.config),ut={id:(Date.now()+1).toString(),role:"assistant",content:gt.content,timestamp:new Date,thinking:gt.thinking},pt=[...lt,ut],F={...St,messages:pt,updatedAt:new Date};i(zt=>({...zt,currentSession:F,sessions:zt.sessions.map(Ut=>Ut.id===F.id?F:Ut),isLoading:!1}))}catch(gt){i(ut=>({...ut,isLoading:!1,error:gt instanceof Error?gt.message:"Unknown error occurred"}))}},x=H=>{i(L=>({...L,currentSession:H})),s(!1)},U=H=>{i(L=>{var St;const bt=L.sessions.filter(gt=>gt.id!==H),lt=((St=L.currentSession)==null?void 0:St.id)===H?null:L.currentSession;return{...L,sessions:bt,currentSession:lt}})},K=H=>{i(L=>({...L,config:H}))};return T.jsxs("div",{className:"min-h-screen bg-sanctuary-900 text-sanctuary-100",children:[T.jsx(dg,{config:f.config,onConfigChange:K,sessions:f.sessions,currentSession:f.currentSession,onSessionSelect:x,onDeleteSession:U,isOpen:c,onToggle:()=>s(!c)}),T.jsx("div",{className:`transition-all duration-300 ${c?"ml-80":"ml-0"}`,children:T.jsxs("div",{className:"flex flex-col h-screen",children:[T.jsx(og,{onNewChat:O,onSettings:()=>m(!0)}),f.error&&T.jsx("div",{className:"mx-6 mb-4 p-4 bg-red-900/50 border border-red-500/50 rounded-lg text-red-200",children:T.jsxs("div",{className:"flex items-center space-x-2",children:[T.jsx("span",{children:"⚠️"}),T.jsx("span",{children:f.error})]})}),T.jsx(hg,{messages:((k=f.currentSession)==null?void 0:k.messages)||[],isLoading:f.isLoading}),T.jsx(mg,{onSendMessage:w,isLoading:f.isLoading,disabled:!Ia.isConfigured()})]})}),T.jsx(Ig,{isOpen:o,onClose:()=>m(!1)})]})}rg.createRoot(document.getElementById("root")).render(T.jsx(Jt.StrictMode,{children:T.jsx(tv,{})}));
