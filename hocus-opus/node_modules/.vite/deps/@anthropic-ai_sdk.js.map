{"version": 3, "sources": ["../../@anthropic-ai/sdk/internal/tslib.mjs", "../../@anthropic-ai/sdk/src/internal/utils/uuid.ts", "../../@anthropic-ai/sdk/src/internal/errors.ts", "../../@anthropic-ai/sdk/src/core/error.ts", "../../@anthropic-ai/sdk/src/internal/utils/values.ts", "../../@anthropic-ai/sdk/src/internal/utils/sleep.ts", "../../@anthropic-ai/sdk/src/internal/utils/log.ts", "../../@anthropic-ai/sdk/src/version.ts", "../../@anthropic-ai/sdk/src/internal/detect-platform.ts", "../../@anthropic-ai/sdk/src/internal/shims.ts", "../../@anthropic-ai/sdk/src/internal/request-options.ts", "../../@anthropic-ai/sdk/src/internal/utils/bytes.ts", "../../@anthropic-ai/sdk/src/internal/decoders/line.ts", "../../@anthropic-ai/sdk/src/core/streaming.ts", "../../@anthropic-ai/sdk/src/internal/parse.ts", "../../@anthropic-ai/sdk/src/core/api-promise.ts", "../../@anthropic-ai/sdk/src/core/pagination.ts", "../../@anthropic-ai/sdk/src/internal/uploads.ts", "../../@anthropic-ai/sdk/src/internal/to-file.ts", "../../@anthropic-ai/sdk/src/core/resource.ts", "../../@anthropic-ai/sdk/src/internal/headers.ts", "../../@anthropic-ai/sdk/src/internal/utils/path.ts", "../../@anthropic-ai/sdk/src/resources/beta/files.ts", "../../@anthropic-ai/sdk/src/resources/beta/models.ts", "../../@anthropic-ai/sdk/src/internal/decoders/jsonl.ts", "../../@anthropic-ai/sdk/src/resources/beta/messages/batches.ts", "../../@anthropic-ai/sdk/src/_vendor/partial-json-parser/parser.ts", "../../@anthropic-ai/sdk/src/lib/BetaMessageStream.ts", "../../@anthropic-ai/sdk/src/internal/constants.ts", "../../@anthropic-ai/sdk/src/resources/beta/messages/messages.ts", "../../@anthropic-ai/sdk/src/resources/beta/beta.ts", "../../@anthropic-ai/sdk/src/resources/completions.ts", "../../@anthropic-ai/sdk/src/lib/MessageStream.ts", "../../@anthropic-ai/sdk/src/resources/messages/batches.ts", "../../@anthropic-ai/sdk/src/resources/messages/messages.ts", "../../@anthropic-ai/sdk/src/resources/models.ts", "../../@anthropic-ai/sdk/src/internal/utils/env.ts", "../../@anthropic-ai/sdk/src/client.ts"], "sourcesContent": ["function __classPrivateFieldSet(receiver, state, value, kind, f) {\n    if (kind === \"m\")\n        throw new TypeError(\"Private method is not writable\");\n    if (kind === \"a\" && !f)\n        throw new TypeError(\"Private accessor was defined without a setter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver))\n        throw new TypeError(\"Cannot write private member to an object whose class did not declare it\");\n    return kind === \"a\" ? f.call(receiver, value) : f ? (f.value = value) : state.set(receiver, value), value;\n}\nfunction __classPrivateFieldGet(receiver, state, kind, f) {\n    if (kind === \"a\" && !f)\n        throw new TypeError(\"Private accessor was defined without a getter\");\n    if (typeof state === \"function\" ? receiver !== state || !f : !state.has(receiver))\n        throw new TypeError(\"Cannot read private member from an object whose class did not declare it\");\n    return kind === \"m\" ? f : kind === \"a\" ? f.call(receiver) : f ? f.value : state.get(receiver);\n}\nexport { __classPrivateFieldSet, __classPrivateFieldGet };\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n/**\n * https://stackoverflow.com/a/2117523\n */\nexport let uuid4 = function () {\n  const { crypto } = globalThis as any;\n  if (crypto?.randomUUID) {\n    uuid4 = crypto.randomUUID.bind(crypto);\n    return crypto.randomUUID();\n  }\n  const u8 = new Uint8Array(1);\n  const randomByte = crypto ? () => crypto.getRandomValues(u8)[0]! : () => (Math.random() * 0xff) & 0xff;\n  return '10000000-1000-4000-8000-100000000000'.replace(/[018]/g, (c) =>\n    (+c ^ (randomByte() & (15 >> (+c / 4)))).toString(16),\n  );\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nexport function isAbortError(err: unknown) {\n  return (\n    typeof err === 'object' &&\n    err !== null &&\n    // Spec-compliant fetch implementations\n    (('name' in err && (err as any).name === 'AbortError') ||\n      // Expo fetch\n      ('message' in err && String((err as any).message).includes('FetchRequestCanceledException')))\n  );\n}\n\nexport const castToError = (err: any): Error => {\n  if (err instanceof Error) return err;\n  if (typeof err === 'object' && err !== null) {\n    try {\n      if (Object.prototype.toString.call(err) === '[object Error]') {\n        // @ts-ignore - not all envs have native support for cause yet\n        const error = new Error(err.message, err.cause ? { cause: err.cause } : {});\n        if (err.stack) error.stack = err.stack;\n        // @ts-ignore - not all envs have native support for cause yet\n        if (err.cause && !error.cause) error.cause = err.cause;\n        if (err.name) error.name = err.name;\n        return error;\n      }\n    } catch {}\n    try {\n      return new Error(JSON.stringify(err));\n    } catch {}\n  }\n  return new Error(err);\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { castToError } from '../internal/errors';\n\nexport class AnthropicError extends Error {}\n\nexport class APIError<\n  TStatus extends number | undefined = number | undefined,\n  THeaders extends Headers | undefined = Headers | undefined,\n  T<PERSON>rror extends Object | undefined = Object | undefined,\n> extends AnthropicError {\n  /** HTTP status for the response that caused the error */\n  readonly status: TStatus;\n  /** HTTP headers for the response that caused the error */\n  readonly headers: THeaders;\n  /** JSON body of the response that caused the error */\n  readonly error: TError;\n\n  readonly requestID: string | null | undefined;\n\n  constructor(status: TStatus, error: TError, message: string | undefined, headers: THeaders) {\n    super(`${APIError.makeMessage(status, error, message)}`);\n    this.status = status;\n    this.headers = headers;\n    this.requestID = headers?.get('request-id');\n    this.error = error;\n  }\n\n  private static makeMessage(status: number | undefined, error: any, message: string | undefined) {\n    const msg =\n      error?.message ?\n        typeof error.message === 'string' ?\n          error.message\n        : JSON.stringify(error.message)\n      : error ? JSON.stringify(error)\n      : message;\n\n    if (status && msg) {\n      return `${status} ${msg}`;\n    }\n    if (status) {\n      return `${status} status code (no body)`;\n    }\n    if (msg) {\n      return msg;\n    }\n    return '(no status code or body)';\n  }\n\n  static generate(\n    status: number | undefined,\n    errorResponse: Object | undefined,\n    message: string | undefined,\n    headers: Headers | undefined,\n  ): APIError {\n    if (!status || !headers) {\n      return new APIConnectionError({ message, cause: castToError(errorResponse) });\n    }\n\n    const error = errorResponse as Record<string, any>;\n\n    if (status === 400) {\n      return new BadRequestError(status, error, message, headers);\n    }\n\n    if (status === 401) {\n      return new AuthenticationError(status, error, message, headers);\n    }\n\n    if (status === 403) {\n      return new PermissionDeniedError(status, error, message, headers);\n    }\n\n    if (status === 404) {\n      return new NotFoundError(status, error, message, headers);\n    }\n\n    if (status === 409) {\n      return new ConflictError(status, error, message, headers);\n    }\n\n    if (status === 422) {\n      return new UnprocessableEntityError(status, error, message, headers);\n    }\n\n    if (status === 429) {\n      return new RateLimitError(status, error, message, headers);\n    }\n\n    if (status >= 500) {\n      return new InternalServerError(status, error, message, headers);\n    }\n\n    return new APIError(status, error, message, headers);\n  }\n}\n\nexport class APIUserAbortError extends APIError<undefined, undefined, undefined> {\n  constructor({ message }: { message?: string } = {}) {\n    super(undefined, undefined, message || 'Request was aborted.', undefined);\n  }\n}\n\nexport class APIConnectionError extends APIError<undefined, undefined, undefined> {\n  constructor({ message, cause }: { message?: string | undefined; cause?: Error | undefined }) {\n    super(undefined, undefined, message || 'Connection error.', undefined);\n    // in some environments the 'cause' property is already declared\n    // @ts-ignore\n    if (cause) this.cause = cause;\n  }\n}\n\nexport class APIConnectionTimeoutError extends APIConnectionError {\n  constructor({ message }: { message?: string } = {}) {\n    super({ message: message ?? 'Request timed out.' });\n  }\n}\n\nexport class BadRequestError extends APIError<400, Headers> {}\n\nexport class AuthenticationError extends APIError<401, Headers> {}\n\nexport class PermissionDeniedError extends APIError<403, Headers> {}\n\nexport class NotFoundError extends APIError<404, Headers> {}\n\nexport class ConflictError extends APIError<409, Headers> {}\n\nexport class UnprocessableEntityError extends APIError<422, Headers> {}\n\nexport class RateLimitError extends APIError<429, Headers> {}\n\nexport class InternalServerError extends APIError<number, Headers> {}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { AnthropicError } from '../../core/error';\n\n// https://url.spec.whatwg.org/#url-scheme-string\nconst startsWithSchemeRegexp = /^[a-z][a-z0-9+.-]*:/i;\n\nexport const isAbsoluteURL = (url: string): boolean => {\n  return startsWithSchemeRegexp.test(url);\n};\n\n/** Returns an object if the given value isn't an object, otherwise returns as-is */\nexport function maybeObj(x: unknown): object {\n  if (typeof x !== 'object') {\n    return {};\n  }\n\n  return x ?? {};\n}\n\n// https://stackoverflow.com/a/34491287\nexport function isEmptyObj(obj: Object | null | undefined): boolean {\n  if (!obj) return true;\n  for (const _k in obj) return false;\n  return true;\n}\n\n// https://eslint.org/docs/latest/rules/no-prototype-builtins\nexport function hasOwn<T extends object = object>(obj: T, key: PropertyKey): key is keyof T {\n  return Object.prototype.hasOwnProperty.call(obj, key);\n}\n\nexport function isObj(obj: unknown): obj is Record<string, unknown> {\n  return obj != null && typeof obj === 'object' && !Array.isArray(obj);\n}\n\nexport const ensurePresent = <T>(value: T | null | undefined): T => {\n  if (value == null) {\n    throw new AnthropicError(`Expected a value to be given but received ${value} instead.`);\n  }\n\n  return value;\n};\n\nexport const validatePositiveInteger = (name: string, n: unknown): number => {\n  if (typeof n !== 'number' || !Number.isInteger(n)) {\n    throw new AnthropicError(`${name} must be an integer`);\n  }\n  if (n < 0) {\n    throw new AnthropicError(`${name} must be a positive integer`);\n  }\n  return n;\n};\n\nexport const coerceInteger = (value: unknown): number => {\n  if (typeof value === 'number') return Math.round(value);\n  if (typeof value === 'string') return parseInt(value, 10);\n\n  throw new AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\n\nexport const coerceFloat = (value: unknown): number => {\n  if (typeof value === 'number') return value;\n  if (typeof value === 'string') return parseFloat(value);\n\n  throw new AnthropicError(`Could not coerce ${value} (type: ${typeof value}) into a number`);\n};\n\nexport const coerceBoolean = (value: unknown): boolean => {\n  if (typeof value === 'boolean') return value;\n  if (typeof value === 'string') return value === 'true';\n  return Boolean(value);\n};\n\nexport const maybeCoerceInteger = (value: unknown): number | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceInteger(value);\n};\n\nexport const maybeCoerceFloat = (value: unknown): number | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceFloat(value);\n};\n\nexport const maybeCoerceBoolean = (value: unknown): boolean | undefined => {\n  if (value === undefined) {\n    return undefined;\n  }\n  return coerceBoolean(value);\n};\n\nexport const safeJSON = (text: string) => {\n  try {\n    return JSON.parse(text);\n  } catch (err) {\n    return undefined;\n  }\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nexport const sleep = (ms: number) => new Promise<void>((resolve) => setTimeout(resolve, ms));\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { hasOwn } from './values';\nimport { type BaseAnthropic } from '../../client';\nimport { RequestOptions } from '../request-options';\n\ntype LogFn = (message: string, ...rest: unknown[]) => void;\nexport type Logger = {\n  error: LogFn;\n  warn: LogFn;\n  info: LogFn;\n  debug: LogFn;\n};\nexport type LogLevel = 'off' | 'error' | 'warn' | 'info' | 'debug';\n\nconst levelNumbers = {\n  off: 0,\n  error: 200,\n  warn: 300,\n  info: 400,\n  debug: 500,\n};\n\nexport const parseLogLevel = (\n  maybeLevel: string | undefined,\n  sourceName: string,\n  client: BaseAnthropic,\n): LogLevel | undefined => {\n  if (!maybeLevel) {\n    return undefined;\n  }\n  if (hasOwn(levelNumbers, maybeLevel)) {\n    return maybeLevel;\n  }\n  loggerFor(client).warn(\n    `${sourceName} was set to ${JSON.stringify(maybeLevel)}, expected one of ${JSON.stringify(\n      Object.keys(levelNumbers),\n    )}`,\n  );\n  return undefined;\n};\n\nfunction noop() {}\n\nfunction makeLogFn(fnLevel: keyof Logger, logger: Logger | undefined, logLevel: LogLevel) {\n  if (!logger || levelNumbers[fnLevel] > levelNumbers[logLevel]) {\n    return noop;\n  } else {\n    // Don't wrap logger functions, we want the stacktrace intact!\n    return logger[fnLevel].bind(logger);\n  }\n}\n\nconst noopLogger = {\n  error: noop,\n  warn: noop,\n  info: noop,\n  debug: noop,\n};\n\nlet cachedLoggers = new WeakMap<Logger, [LogLevel, Logger]>();\n\nexport function loggerFor(client: BaseAnthropic): Logger {\n  const logger = client.logger;\n  const logLevel = client.logLevel ?? 'off';\n  if (!logger) {\n    return noopLogger;\n  }\n\n  const cachedLogger = cachedLoggers.get(logger);\n  if (cachedLogger && cachedLogger[0] === logLevel) {\n    return cachedLogger[1];\n  }\n\n  const levelLogger = {\n    error: makeLogFn('error', logger, logLevel),\n    warn: makeLogFn('warn', logger, logLevel),\n    info: makeLogFn('info', logger, logLevel),\n    debug: makeLogFn('debug', logger, logLevel),\n  };\n\n  cachedLoggers.set(logger, [logLevel, levelLogger]);\n\n  return levelLogger;\n}\n\nexport const formatRequestDetails = (details: {\n  options?: RequestOptions | undefined;\n  headers?: Headers | Record<string, string> | undefined;\n  retryOfRequestLogID?: string | undefined;\n  retryOf?: string | undefined;\n  url?: string | undefined;\n  status?: number | undefined;\n  method?: string | undefined;\n  durationMs?: number | undefined;\n  message?: unknown;\n  body?: unknown;\n}) => {\n  if (details.options) {\n    details.options = { ...details.options };\n    delete details.options['headers']; // redundant + leaks internals\n  }\n  if (details.headers) {\n    details.headers = Object.fromEntries(\n      (details.headers instanceof Headers ? [...details.headers] : Object.entries(details.headers)).map(\n        ([name, value]) => [\n          name,\n          (\n            name.toLowerCase() === 'x-api-key' ||\n            name.toLowerCase() === 'authorization' ||\n            name.toLowerCase() === 'cookie' ||\n            name.toLowerCase() === 'set-cookie'\n          ) ?\n            '***'\n          : value,\n        ],\n      ),\n    );\n  }\n  if ('retryOfRequestLogID' in details) {\n    if (details.retryOfRequestLogID) {\n      details.retryOf = details.retryOfRequestLogID;\n    }\n    delete details.retryOfRequestLogID;\n  }\n  return details;\n};\n", "export const VERSION = '0.52.0'; // x-release-please-version\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { VERSION } from '../version';\n\nexport const isRunningInBrowser = () => {\n  return (\n    // @ts-ignore\n    typeof window !== 'undefined' &&\n    // @ts-ignore\n    typeof window.document !== 'undefined' &&\n    // @ts-ignore\n    typeof navigator !== 'undefined'\n  );\n};\n\ntype DetectedPlatform = 'deno' | 'node' | 'edge' | 'unknown';\n\n/**\n * Note this does not detect 'browser'; for that, use getBrowserInfo().\n */\nfunction getDetectedPlatform(): DetectedPlatform {\n  if (typeof Deno !== 'undefined' && Deno.build != null) {\n    return 'deno';\n  }\n  if (typeof EdgeRuntime !== 'undefined') {\n    return 'edge';\n  }\n  if (\n    Object.prototype.toString.call(\n      typeof (globalThis as any).process !== 'undefined' ? (globalThis as any).process : 0,\n    ) === '[object process]'\n  ) {\n    return 'node';\n  }\n  return 'unknown';\n}\n\ndeclare const Deno: any;\ndeclare const EdgeRuntime: any;\ntype Arch = 'x32' | 'x64' | 'arm' | 'arm64' | `other:${string}` | 'unknown';\ntype PlatformName =\n  | 'MacOS'\n  | 'Linux'\n  | 'Windows'\n  | 'FreeBSD'\n  | 'OpenBSD'\n  | 'iOS'\n  | 'Android'\n  | `Other:${string}`\n  | 'Unknown';\ntype Browser = 'ie' | 'edge' | 'chrome' | 'firefox' | 'safari';\ntype PlatformProperties = {\n  'X-Stainless-Lang': 'js';\n  'X-Stainless-Package-Version': string;\n  'X-Stainless-OS': PlatformName;\n  'X-Stainless-Arch': Arch;\n  'X-Stainless-Runtime': 'node' | 'deno' | 'edge' | `browser:${Browser}` | 'unknown';\n  'X-Stainless-Runtime-Version': string;\n};\nconst getPlatformProperties = (): PlatformProperties => {\n  const detectedPlatform = getDetectedPlatform();\n  if (detectedPlatform === 'deno') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': normalizePlatform(Deno.build.os),\n      'X-Stainless-Arch': normalizeArch(Deno.build.arch),\n      'X-Stainless-Runtime': 'deno',\n      'X-Stainless-Runtime-Version':\n        typeof Deno.version === 'string' ? Deno.version : Deno.version?.deno ?? 'unknown',\n    };\n  }\n  if (typeof EdgeRuntime !== 'undefined') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': 'Unknown',\n      'X-Stainless-Arch': `other:${EdgeRuntime}`,\n      'X-Stainless-Runtime': 'edge',\n      'X-Stainless-Runtime-Version': (globalThis as any).process.version,\n    };\n  }\n  // Check if Node.js\n  if (detectedPlatform === 'node') {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': normalizePlatform((globalThis as any).process.platform),\n      'X-Stainless-Arch': normalizeArch((globalThis as any).process.arch),\n      'X-Stainless-Runtime': 'node',\n      'X-Stainless-Runtime-Version': (globalThis as any).process.version,\n    };\n  }\n\n  const browserInfo = getBrowserInfo();\n  if (browserInfo) {\n    return {\n      'X-Stainless-Lang': 'js',\n      'X-Stainless-Package-Version': VERSION,\n      'X-Stainless-OS': 'Unknown',\n      'X-Stainless-Arch': 'unknown',\n      'X-Stainless-Runtime': `browser:${browserInfo.browser}`,\n      'X-Stainless-Runtime-Version': browserInfo.version,\n    };\n  }\n\n  // TODO add support for Cloudflare workers, etc.\n  return {\n    'X-Stainless-Lang': 'js',\n    'X-Stainless-Package-Version': VERSION,\n    'X-Stainless-OS': 'Unknown',\n    'X-Stainless-Arch': 'unknown',\n    'X-Stainless-Runtime': 'unknown',\n    'X-Stainless-Runtime-Version': 'unknown',\n  };\n};\n\ntype BrowserInfo = {\n  browser: Browser;\n  version: string;\n};\n\ndeclare const navigator: { userAgent: string } | undefined;\n\n// Note: modified from https://github.com/JS-DevTools/host-environment/blob/b1ab79ecde37db5d6e163c050e54fe7d287d7c92/src/isomorphic.browser.ts\nfunction getBrowserInfo(): BrowserInfo | null {\n  if (typeof navigator === 'undefined' || !navigator) {\n    return null;\n  }\n\n  // NOTE: The order matters here!\n  const browserPatterns = [\n    { key: 'edge' as const, pattern: /Edge(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'ie' as const, pattern: /MSIE(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'ie' as const, pattern: /Trident(?:.*rv\\:(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'chrome' as const, pattern: /Chrome(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'firefox' as const, pattern: /Firefox(?:\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?/ },\n    { key: 'safari' as const, pattern: /(?:Version\\W+(\\d+)\\.(\\d+)(?:\\.(\\d+))?)?(?:\\W+Mobile\\S*)?\\W+Safari/ },\n  ];\n\n  // Find the FIRST matching browser\n  for (const { key, pattern } of browserPatterns) {\n    const match = pattern.exec(navigator.userAgent);\n    if (match) {\n      const major = match[1] || 0;\n      const minor = match[2] || 0;\n      const patch = match[3] || 0;\n\n      return { browser: key, version: `${major}.${minor}.${patch}` };\n    }\n  }\n\n  return null;\n}\n\nconst normalizeArch = (arch: string): Arch => {\n  // Node docs:\n  // - https://nodejs.org/api/process.html#processarch\n  // Deno docs:\n  // - https://doc.deno.land/deno/stable/~/Deno.build\n  if (arch === 'x32') return 'x32';\n  if (arch === 'x86_64' || arch === 'x64') return 'x64';\n  if (arch === 'arm') return 'arm';\n  if (arch === 'aarch64' || arch === 'arm64') return 'arm64';\n  if (arch) return `other:${arch}`;\n  return 'unknown';\n};\n\nconst normalizePlatform = (platform: string): PlatformName => {\n  // Node platforms:\n  // - https://nodejs.org/api/process.html#processplatform\n  // Deno platforms:\n  // - https://doc.deno.land/deno/stable/~/Deno.build\n  // - https://github.com/denoland/deno/issues/14799\n\n  platform = platform.toLowerCase();\n\n  // NOTE: this iOS check is untested and may not work\n  // Node does not work natively on IOS, there is a fork at\n  // https://github.com/nodejs-mobile/nodejs-mobile\n  // however it is unknown at the time of writing how to detect if it is running\n  if (platform.includes('ios')) return 'iOS';\n  if (platform === 'android') return 'Android';\n  if (platform === 'darwin') return 'MacOS';\n  if (platform === 'win32') return 'Windows';\n  if (platform === 'freebsd') return 'FreeBSD';\n  if (platform === 'openbsd') return 'OpenBSD';\n  if (platform === 'linux') return 'Linux';\n  if (platform) return `Other:${platform}`;\n  return 'Unknown';\n};\n\nlet _platformHeaders: PlatformProperties;\nexport const getPlatformHeaders = () => {\n  return (_platformHeaders ??= getPlatformProperties());\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n/**\n * This module provides internal shims and utility functions for environments where certain Node.js or global types may not be available.\n *\n * These are used to ensure we can provide a consistent behaviour between different JavaScript environments and good error\n * messages in cases where an environment isn't fully supported.\n */\n\nimport { type Fetch } from './builtin-types';\nimport { type ReadableStream } from './shim-types';\n\nexport function getDefaultFetch(): Fetch {\n  if (typeof fetch !== 'undefined') {\n    return fetch as any;\n  }\n\n  throw new Error(\n    '`fetch` is not defined as a global; Either pass `fetch` to the client, `new Anthropic({ fetch })` or polyfill the global, `globalThis.fetch = fetch`',\n  );\n}\n\ntype ReadableStreamArgs = ConstructorParameters<typeof ReadableStream>;\n\nexport function makeReadableStream(...args: ReadableStreamArgs): ReadableStream {\n  const ReadableStream = (globalThis as any).ReadableStream;\n  if (typeof ReadableStream === 'undefined') {\n    // Note: All of the platforms / runtimes we officially support already define\n    // `ReadableStream` as a global, so this should only ever be hit on unsupported runtimes.\n    throw new Error(\n      '`ReadableStream` is not defined as a global; You will need to polyfill it, `globalThis.ReadableStream = ReadableStream`',\n    );\n  }\n\n  return new ReadableStream(...args);\n}\n\nexport function ReadableStreamFrom<T>(iterable: Iterable<T> | AsyncIterable<T>): ReadableStream<T> {\n  let iter: AsyncIterator<T> | Iterator<T> =\n    Symbol.asyncIterator in iterable ? iterable[Symbol.asyncIterator]() : iterable[Symbol.iterator]();\n\n  return makeReadableStream({\n    start() {},\n    async pull(controller: any) {\n      const { done, value } = await iter.next();\n      if (done) {\n        controller.close();\n      } else {\n        controller.enqueue(value);\n      }\n    },\n    async cancel() {\n      await iter.return?.();\n    },\n  });\n}\n\n/**\n * Most browsers don't yet have async iterable support for ReadableStream,\n * and Node has a very different way of reading bytes from its \"ReadableStream\".\n *\n * This polyfill was pulled from https://github.com/MattiasBuelens/web-streams-polyfill/pull/122#issuecomment-1627354490\n */\nexport function ReadableStreamToAsyncIterable<T>(stream: any): AsyncIterableIterator<T> {\n  if (stream[Symbol.asyncIterator]) return stream;\n\n  const reader = stream.getReader();\n  return {\n    async next() {\n      try {\n        const result = await reader.read();\n        if (result?.done) reader.releaseLock(); // release lock when stream becomes closed\n        return result;\n      } catch (e) {\n        reader.releaseLock(); // release lock when stream becomes errored\n        throw e;\n      }\n    },\n    async return() {\n      const cancelPromise = reader.cancel();\n      reader.releaseLock();\n      await cancelPromise;\n      return { done: true, value: undefined };\n    },\n    [Symbol.asyncIterator]() {\n      return this;\n    },\n  };\n}\n\n/**\n * Cancels a ReadableStream we don't need to consume.\n * See https://undici.nodejs.org/#/?id=garbage-collection\n */\nexport async function CancelReadableStream(stream: any): Promise<void> {\n  if (stream === null || typeof stream !== 'object') return;\n\n  if (stream[Symbol.asyncIterator]) {\n    await stream[Symbol.asyncIterator]().return?.();\n    return;\n  }\n\n  const reader = stream.getReader();\n  const cancelPromise = reader.cancel();\n  reader.releaseLock();\n  await cancelPromise;\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { NullableHeaders } from './headers';\n\nimport type { BodyInit } from './builtin-types';\nimport { Stream } from '../core/streaming';\nimport type { HTTPMethod, MergedRequestInit } from './types';\nimport { type HeadersLike } from './headers';\n\nexport type FinalRequestOptions = RequestOptions & { method: HTTPMethod; path: string };\n\nexport type RequestOptions = {\n  method?: HTTPMethod;\n  path?: string;\n  query?: object | undefined | null;\n  body?: unknown;\n  headers?: HeadersLike;\n  maxRetries?: number;\n  stream?: boolean | undefined;\n  timeout?: number;\n  fetchOptions?: MergedRequestInit;\n  signal?: AbortSignal | undefined | null;\n  idempotencyKey?: string;\n\n  __binaryResponse?: boolean | undefined;\n  __streamClass?: typeof Stream;\n};\n\nexport type EncodedContent = { bodyHeaders: HeadersLike; body: BodyInit };\nexport type RequestEncoder = (request: { headers: NullableHeaders; body: unknown }) => EncodedContent;\n\nexport const FallbackEncoder: RequestEncoder = ({ headers, body }) => {\n  return {\n    bodyHeaders: {\n      'content-type': 'application/json',\n    },\n    body: JSON.stringify(body),\n  };\n};\n", "export function concatBytes(buffers: Uint8Array[]): Uint8Array {\n  let length = 0;\n  for (const buffer of buffers) {\n    length += buffer.length;\n  }\n  const output = new Uint8Array(length);\n  let index = 0;\n  for (const buffer of buffers) {\n    output.set(buffer, index);\n    index += buffer.length;\n  }\n\n  return output;\n}\n\nlet encodeUTF8_: (str: string) => Uint8Array;\nexport function encodeUTF8(str: string) {\n  let encoder;\n  return (\n    encodeUTF8_ ??\n    ((encoder = new (globalThis as any).TextEncoder()), (encodeUTF8_ = encoder.encode.bind(encoder)))\n  )(str);\n}\n\nlet decodeUTF8_: (bytes: Uint8Array) => string;\nexport function decodeUTF8(bytes: Uint8Array) {\n  let decoder;\n  return (\n    decodeUTF8_ ??\n    ((decoder = new (globalThis as any).TextDecoder()), (decodeUTF8_ = decoder.decode.bind(decoder)))\n  )(bytes);\n}\n", "import { concatBytes, decodeUTF8, encodeUTF8 } from '../utils/bytes';\n\nexport type Bytes = string | ArrayBuffer | Uint8Array | null | undefined;\n\n/**\n * A re-implementation of httpx's `LineDecoder` in Python that handles incrementally\n * reading lines from text.\n *\n * https://github.com/encode/httpx/blob/920333ea98118e9cf617f246905d7b202510941c/httpx/_decoders.py#L258\n */\nexport class LineDecoder {\n  // prettier-ignore\n  static NEWLINE_CHARS = new Set(['\\n', '\\r']);\n  static NEWLINE_REGEXP = /\\r\\n|[\\n\\r]/g;\n\n  #buffer: Uint8Array;\n  #carriageReturnIndex: number | null;\n\n  constructor() {\n    this.#buffer = new Uint8Array();\n    this.#carriageReturnIndex = null;\n  }\n\n  decode(chunk: Bytes): string[] {\n    if (chunk == null) {\n      return [];\n    }\n\n    const binaryChunk =\n      chunk instanceof ArrayBuffer ? new Uint8Array(chunk)\n      : typeof chunk === 'string' ? encodeUTF8(chunk)\n      : chunk;\n\n    this.#buffer = concatBytes([this.#buffer, binaryChunk]);\n\n    const lines: string[] = [];\n    let patternIndex;\n    while ((patternIndex = findNewlineIndex(this.#buffer, this.#carriageReturnIndex)) != null) {\n      if (patternIndex.carriage && this.#carriageReturnIndex == null) {\n        // skip until we either get a corresponding `\\n`, a new `\\r` or nothing\n        this.#carriageReturnIndex = patternIndex.index;\n        continue;\n      }\n\n      // we got double \\r or \\rtext\\n\n      if (\n        this.#carriageReturnIndex != null &&\n        (patternIndex.index !== this.#carriageReturnIndex + 1 || patternIndex.carriage)\n      ) {\n        lines.push(decodeUTF8(this.#buffer.subarray(0, this.#carriageReturnIndex - 1)));\n        this.#buffer = this.#buffer.subarray(this.#carriageReturnIndex);\n        this.#carriageReturnIndex = null;\n        continue;\n      }\n\n      const endIndex =\n        this.#carriageReturnIndex !== null ? patternIndex.preceding - 1 : patternIndex.preceding;\n\n      const line = decodeUTF8(this.#buffer.subarray(0, endIndex));\n      lines.push(line);\n\n      this.#buffer = this.#buffer.subarray(patternIndex.index);\n      this.#carriageReturnIndex = null;\n    }\n\n    return lines;\n  }\n\n  flush(): string[] {\n    if (!this.#buffer.length) {\n      return [];\n    }\n    return this.decode('\\n');\n  }\n}\n\n/**\n * This function searches the buffer for the end patterns, (\\r or \\n)\n * and returns an object with the index preceding the matched newline and the\n * index after the newline char. `null` is returned if no new line is found.\n *\n * ```ts\n * findNewLineIndex('abc\\ndef') -> { preceding: 2, index: 3 }\n * ```\n */\nfunction findNewlineIndex(\n  buffer: Uint8Array,\n  startIndex: number | null,\n): { preceding: number; index: number; carriage: boolean } | null {\n  const newline = 0x0a; // \\n\n  const carriage = 0x0d; // \\r\n\n  for (let i = startIndex ?? 0; i < buffer.length; i++) {\n    if (buffer[i] === newline) {\n      return { preceding: i, index: i + 1, carriage: false };\n    }\n\n    if (buffer[i] === carriage) {\n      return { preceding: i, index: i + 1, carriage: true };\n    }\n  }\n\n  return null;\n}\n\nexport function findDoubleNewlineIndex(buffer: Uint8Array): number {\n  // This function searches the buffer for the end patterns (\\r\\r, \\n\\n, \\r\\n\\r\\n)\n  // and returns the index right after the first occurrence of any pattern,\n  // or -1 if none of the patterns are found.\n  const newline = 0x0a; // \\n\n  const carriage = 0x0d; // \\r\n\n  for (let i = 0; i < buffer.length - 1; i++) {\n    if (buffer[i] === newline && buffer[i + 1] === newline) {\n      // \\n\\n\n      return i + 2;\n    }\n    if (buffer[i] === carriage && buffer[i + 1] === carriage) {\n      // \\r\\r\n      return i + 2;\n    }\n    if (\n      buffer[i] === carriage &&\n      buffer[i + 1] === newline &&\n      i + 3 < buffer.length &&\n      buffer[i + 2] === carriage &&\n      buffer[i + 3] === newline\n    ) {\n      // \\r\\n\\r\\n\n      return i + 4;\n    }\n  }\n\n  return -1;\n}\n", "import { AnthropicError } from './error';\nimport { type ReadableStream } from '../internal/shim-types';\nimport { makeReadableStream } from '../internal/shims';\nimport { findDoubleNewlineIndex, LineDecoder } from '../internal/decoders/line';\nimport { ReadableStreamToAsyncIterable } from '../internal/shims';\nimport { isAbortError } from '../internal/errors';\nimport { safeJSON } from '../internal/utils/values';\nimport { encodeUTF8 } from '../internal/utils/bytes';\n\nimport { APIError } from './error';\n\ntype Bytes = string | ArrayBuffer | Uint8Array | null | undefined;\n\nexport type ServerSentEvent = {\n  event: string | null;\n  data: string;\n  raw: string[];\n};\n\nexport class Stream<Item> implements AsyncIterable<Item> {\n  controller: AbortController;\n\n  constructor(\n    private iterator: () => AsyncIterator<Item>,\n    controller: AbortController,\n  ) {\n    this.controller = controller;\n  }\n\n  static fromSSEResponse<Item>(response: Response, controller: AbortController): Stream<Item> {\n    let consumed = false;\n\n    async function* iterator(): AsyncIterator<Item, any, undefined> {\n      if (consumed) {\n        throw new AnthropicError('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n      }\n      consumed = true;\n      let done = false;\n      try {\n        for await (const sse of _iterSSEMessages(response, controller)) {\n          if (sse.event === 'completion') {\n            try {\n              yield JSON.parse(sse.data);\n            } catch (e) {\n              console.error(`Could not parse message into JSON:`, sse.data);\n              console.error(`From chunk:`, sse.raw);\n              throw e;\n            }\n          }\n\n          if (\n            sse.event === 'message_start' ||\n            sse.event === 'message_delta' ||\n            sse.event === 'message_stop' ||\n            sse.event === 'content_block_start' ||\n            sse.event === 'content_block_delta' ||\n            sse.event === 'content_block_stop'\n          ) {\n            try {\n              yield JSON.parse(sse.data);\n            } catch (e) {\n              console.error(`Could not parse message into JSON:`, sse.data);\n              console.error(`From chunk:`, sse.raw);\n              throw e;\n            }\n          }\n\n          if (sse.event === 'ping') {\n            continue;\n          }\n\n          if (sse.event === 'error') {\n            throw new APIError(undefined, safeJSON(sse.data) ?? sse.data, undefined, response.headers);\n          }\n        }\n        done = true;\n      } catch (e) {\n        // If the user calls `stream.controller.abort()`, we should exit without throwing.\n        if (isAbortError(e)) return;\n        throw e;\n      } finally {\n        // If the user `break`s, abort the ongoing request.\n        if (!done) controller.abort();\n      }\n    }\n\n    return new Stream(iterator, controller);\n  }\n\n  /**\n   * Generates a Stream from a newline-separated ReadableStream\n   * where each item is a JSON value.\n   */\n  static fromReadableStream<Item>(readableStream: ReadableStream, controller: AbortController): Stream<Item> {\n    let consumed = false;\n\n    async function* iterLines(): AsyncGenerator<string, void, unknown> {\n      const lineDecoder = new LineDecoder();\n\n      const iter = ReadableStreamToAsyncIterable<Bytes>(readableStream);\n      for await (const chunk of iter) {\n        for (const line of lineDecoder.decode(chunk)) {\n          yield line;\n        }\n      }\n\n      for (const line of lineDecoder.flush()) {\n        yield line;\n      }\n    }\n\n    async function* iterator(): AsyncIterator<Item, any, undefined> {\n      if (consumed) {\n        throw new AnthropicError('Cannot iterate over a consumed stream, use `.tee()` to split the stream.');\n      }\n      consumed = true;\n      let done = false;\n      try {\n        for await (const line of iterLines()) {\n          if (done) continue;\n          if (line) yield JSON.parse(line);\n        }\n        done = true;\n      } catch (e) {\n        // If the user calls `stream.controller.abort()`, we should exit without throwing.\n        if (isAbortError(e)) return;\n        throw e;\n      } finally {\n        // If the user `break`s, abort the ongoing request.\n        if (!done) controller.abort();\n      }\n    }\n\n    return new Stream(iterator, controller);\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<Item> {\n    return this.iterator();\n  }\n\n  /**\n   * Splits the stream into two streams which can be\n   * independently read from at different speeds.\n   */\n  tee(): [Stream<Item>, Stream<Item>] {\n    const left: Array<Promise<IteratorResult<Item>>> = [];\n    const right: Array<Promise<IteratorResult<Item>>> = [];\n    const iterator = this.iterator();\n\n    const teeIterator = (queue: Array<Promise<IteratorResult<Item>>>): AsyncIterator<Item> => {\n      return {\n        next: () => {\n          if (queue.length === 0) {\n            const result = iterator.next();\n            left.push(result);\n            right.push(result);\n          }\n          return queue.shift()!;\n        },\n      };\n    };\n\n    return [\n      new Stream(() => teeIterator(left), this.controller),\n      new Stream(() => teeIterator(right), this.controller),\n    ];\n  }\n\n  /**\n   * Converts this stream to a newline-separated ReadableStream of\n   * JSON stringified values in the stream\n   * which can be turned back into a Stream with `Stream.fromReadableStream()`.\n   */\n  toReadableStream(): ReadableStream {\n    const self = this;\n    let iter: AsyncIterator<Item>;\n\n    return makeReadableStream({\n      async start() {\n        iter = self[Symbol.asyncIterator]();\n      },\n      async pull(ctrl: any) {\n        try {\n          const { value, done } = await iter.next();\n          if (done) return ctrl.close();\n\n          const bytes = encodeUTF8(JSON.stringify(value) + '\\n');\n\n          ctrl.enqueue(bytes);\n        } catch (err) {\n          ctrl.error(err);\n        }\n      },\n      async cancel() {\n        await iter.return?.();\n      },\n    });\n  }\n}\n\nexport async function* _iterSSEMessages(\n  response: Response,\n  controller: AbortController,\n): AsyncGenerator<ServerSentEvent, void, unknown> {\n  if (!response.body) {\n    controller.abort();\n    if (\n      typeof (globalThis as any).navigator !== 'undefined' &&\n      (globalThis as any).navigator.product === 'ReactNative'\n    ) {\n      throw new AnthropicError(\n        `The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api`,\n      );\n    }\n    throw new AnthropicError(`Attempted to iterate over a response with no body`);\n  }\n\n  const sseDecoder = new SSEDecoder();\n  const lineDecoder = new LineDecoder();\n\n  const iter = ReadableStreamToAsyncIterable<Bytes>(response.body);\n  for await (const sseChunk of iterSSEChunks(iter)) {\n    for (const line of lineDecoder.decode(sseChunk)) {\n      const sse = sseDecoder.decode(line);\n      if (sse) yield sse;\n    }\n  }\n\n  for (const line of lineDecoder.flush()) {\n    const sse = sseDecoder.decode(line);\n    if (sse) yield sse;\n  }\n}\n\n/**\n * Given an async iterable iterator, iterates over it and yields full\n * SSE chunks, i.e. yields when a double new-line is encountered.\n */\nasync function* iterSSEChunks(iterator: AsyncIterableIterator<Bytes>): AsyncGenerator<Uint8Array> {\n  let data = new Uint8Array();\n\n  for await (const chunk of iterator) {\n    if (chunk == null) {\n      continue;\n    }\n\n    const binaryChunk =\n      chunk instanceof ArrayBuffer ? new Uint8Array(chunk)\n      : typeof chunk === 'string' ? encodeUTF8(chunk)\n      : chunk;\n\n    let newData = new Uint8Array(data.length + binaryChunk.length);\n    newData.set(data);\n    newData.set(binaryChunk, data.length);\n    data = newData;\n\n    let patternIndex;\n    while ((patternIndex = findDoubleNewlineIndex(data)) !== -1) {\n      yield data.slice(0, patternIndex);\n      data = data.slice(patternIndex);\n    }\n  }\n\n  if (data.length > 0) {\n    yield data;\n  }\n}\n\nclass SSEDecoder {\n  private data: string[];\n  private event: string | null;\n  private chunks: string[];\n\n  constructor() {\n    this.event = null;\n    this.data = [];\n    this.chunks = [];\n  }\n\n  decode(line: string) {\n    if (line.endsWith('\\r')) {\n      line = line.substring(0, line.length - 1);\n    }\n\n    if (!line) {\n      // empty line and we didn't previously encounter any messages\n      if (!this.event && !this.data.length) return null;\n\n      const sse: ServerSentEvent = {\n        event: this.event,\n        data: this.data.join('\\n'),\n        raw: this.chunks,\n      };\n\n      this.event = null;\n      this.data = [];\n      this.chunks = [];\n\n      return sse;\n    }\n\n    this.chunks.push(line);\n\n    if (line.startsWith(':')) {\n      return null;\n    }\n\n    let [fieldname, _, value] = partition(line, ':');\n\n    if (value.startsWith(' ')) {\n      value = value.substring(1);\n    }\n\n    if (fieldname === 'event') {\n      this.event = value;\n    } else if (fieldname === 'data') {\n      this.data.push(value);\n    }\n\n    return null;\n  }\n}\n\nfunction partition(str: string, delimiter: string): [string, string, string] {\n  const index = str.indexOf(delimiter);\n  if (index !== -1) {\n    return [str.substring(0, index), delimiter, str.substring(index + delimiter.length)];\n  }\n\n  return [str, '', ''];\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport type { FinalRequestOptions } from './request-options';\nimport { Stream } from '../core/streaming';\nimport { type BaseAnthropic } from '../client';\nimport { formatRequestDetails, loggerFor } from './utils/log';\nimport type { AbstractPage } from '../core/pagination';\n\nexport type APIResponseProps = {\n  response: Response;\n  options: FinalRequestOptions;\n  controller: AbortController;\n  requestLogID: string;\n  retryOfRequestLogID: string | undefined;\n  startTime: number;\n};\n\nexport async function defaultParseResponse<T>(\n  client: BaseAnthropic,\n  props: APIResponseProps,\n): Promise<WithRequestID<T>> {\n  const { response, requestLogID, retryOfRequestLogID, startTime } = props;\n  const body = await (async () => {\n    if (props.options.stream) {\n      loggerFor(client).debug('response', response.status, response.url, response.headers, response.body);\n\n      // Note: there is an invariant here that isn't represented in the type system\n      // that if you set `stream: true` the response type must also be `Stream<T>`\n\n      if (props.options.__streamClass) {\n        return props.options.__streamClass.fromSSEResponse(response, props.controller) as any;\n      }\n\n      return Stream.fromSSEResponse(response, props.controller) as any;\n    }\n\n    // fetch refuses to read the body when the status code is 204.\n    if (response.status === 204) {\n      return null as T;\n    }\n\n    if (props.options.__binaryResponse) {\n      return response as unknown as T;\n    }\n\n    const contentType = response.headers.get('content-type');\n    const mediaType = contentType?.split(';')[0]?.trim();\n    const isJSON = mediaType?.includes('application/json') || mediaType?.endsWith('+json');\n    if (isJSON) {\n      const json = await response.json();\n      return addRequestID(json as T, response);\n    }\n\n    const text = await response.text();\n    return text as unknown as T;\n  })();\n  loggerFor(client).debug(\n    `[${requestLogID}] response parsed`,\n    formatRequestDetails({\n      retryOfRequestLogID,\n      url: response.url,\n      status: response.status,\n      body,\n      durationMs: Date.now() - startTime,\n    }),\n  );\n  return body;\n}\n\nexport type WithRequestID<T> =\n  T extends Array<any> | Response | AbstractPage<any> ? T\n  : T extends Record<string, any> ? T & { _request_id?: string | null }\n  : T;\n\nexport function addRequestID<T>(value: T, response: Response): WithRequestID<T> {\n  if (!value || typeof value !== 'object' || Array.isArray(value)) {\n    return value as WithRequestID<T>;\n  }\n\n  return Object.defineProperty(value, '_request_id', {\n    value: response.headers.get('request-id'),\n    enumerable: false,\n  }) as WithRequestID<T>;\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { type BaseAnthropic } from '../client';\n\nimport { type PromiseOrValue } from '../internal/types';\nimport {\n  type APIResponseProps,\n  type WithRequestID,\n  defaultParseResponse,\n  addRequestID,\n} from '../internal/parse';\n\n/**\n * A subclass of `Promise` providing additional helper methods\n * for interacting with the SDK.\n */\nexport class APIPromise<T> extends Promise<WithRequestID<T>> {\n  private parsedPromise: Promise<WithRequestID<T>> | undefined;\n  #client: BaseAnthropic;\n\n  constructor(\n    client: BaseAnthropic,\n    private responsePromise: Promise<APIResponseProps>,\n    private parseResponse: (\n      client: BaseAnthropic,\n      props: APIResponseProps,\n    ) => PromiseOrValue<WithRequestID<T>> = defaultParseResponse,\n  ) {\n    super((resolve) => {\n      // this is maybe a bit weird but this has to be a no-op to not implicitly\n      // parse the response body; instead .then, .catch, .finally are overridden\n      // to parse the response\n      resolve(null as any);\n    });\n    this.#client = client;\n  }\n\n  _thenUnwrap<U>(transform: (data: T, props: APIResponseProps) => U): APIPromise<U> {\n    return new APIPromise(this.#client, this.responsePromise, async (client, props) =>\n      addRequestID(transform(await this.parseResponse(client, props), props), props.response),\n    );\n  }\n\n  /**\n   * Gets the raw `Response` instance instead of parsing the response\n   * data.\n   *\n   * If you want to parse the response body but still get the `Response`\n   * instance, you can use {@link withResponse()}.\n   *\n   * 👋 Getting the wrong TypeScript type for `Response`?\n   * Try setting `\"moduleResolution\": \"NodeNext\"` or add `\"lib\": [\"DOM\"]`\n   * to your `tsconfig.json`.\n   */\n  asResponse(): Promise<Response> {\n    return this.responsePromise.then((p) => p.response);\n  }\n\n  /**\n   * Gets the parsed response data, the raw `Response` instance and the ID of the request,\n   * returned via the `request-id` header which is useful for debugging requests and resporting\n   * issues to Anthropic.\n   *\n   * If you just want to get the raw `Response` instance without parsing it,\n   * you can use {@link asResponse()}.\n   *\n   * 👋 Getting the wrong TypeScript type for `Response`?\n   * Try setting `\"moduleResolution\": \"NodeNext\"` or add `\"lib\": [\"DOM\"]`\n   * to your `tsconfig.json`.\n   */\n  async withResponse(): Promise<{ data: T; response: Response; request_id: string | null | undefined }> {\n    const [data, response] = await Promise.all([this.parse(), this.asResponse()]);\n    return { data, response, request_id: response.headers.get('request-id') };\n  }\n\n  private parse(): Promise<WithRequestID<T>> {\n    if (!this.parsedPromise) {\n      this.parsedPromise = this.responsePromise.then(\n        (data) => this.parseResponse(this.#client, data) as any as Promise<WithRequestID<T>>,\n      );\n    }\n    return this.parsedPromise;\n  }\n\n  override then<TResult1 = WithRequestID<T>, TResult2 = never>(\n    onfulfilled?: ((value: WithRequestID<T>) => TResult1 | PromiseLike<TResult1>) | undefined | null,\n    onrejected?: ((reason: any) => TResult2 | PromiseLike<TResult2>) | undefined | null,\n  ): Promise<TResult1 | TResult2> {\n    return this.parse().then(onfulfilled, onrejected);\n  }\n\n  override catch<TResult = never>(\n    onrejected?: ((reason: any) => TResult | PromiseLike<TResult>) | undefined | null,\n  ): Promise<WithRequestID<T> | TResult> {\n    return this.parse().catch(onrejected);\n  }\n\n  override finally(onfinally?: (() => void) | undefined | null): Promise<WithRequestID<T>> {\n    return this.parse().finally(onfinally);\n  }\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { AnthropicError } from './error';\nimport { FinalRequestOptions } from '../internal/request-options';\nimport { defaultParseResponse, WithRequestID } from '../internal/parse';\nimport { type BaseAnthropic } from '../client';\nimport { APIPromise } from './api-promise';\nimport { type APIResponseProps } from '../internal/parse';\nimport { maybeObj } from '../internal/utils/values';\n\nexport type PageRequestOptions = Pick<FinalRequestOptions, 'query' | 'headers' | 'body' | 'path' | 'method'>;\n\nexport abstract class AbstractPage<Item> implements AsyncIterable<Item> {\n  #client: BaseAnthropic;\n  protected options: FinalRequestOptions;\n\n  protected response: Response;\n  protected body: unknown;\n\n  constructor(client: BaseAnthropic, response: Response, body: unknown, options: FinalRequestOptions) {\n    this.#client = client;\n    this.options = options;\n    this.response = response;\n    this.body = body;\n  }\n\n  abstract nextPageRequestOptions(): PageRequestOptions | null;\n\n  abstract getPaginatedItems(): Item[];\n\n  hasNextPage(): boolean {\n    const items = this.getPaginatedItems();\n    if (!items.length) return false;\n    return this.nextPageRequestOptions() != null;\n  }\n\n  async getNextPage(): Promise<this> {\n    const nextOptions = this.nextPageRequestOptions();\n    if (!nextOptions) {\n      throw new AnthropicError(\n        'No next page expected; please check `.hasNextPage()` before calling `.getNextPage()`.',\n      );\n    }\n\n    return await this.#client.requestAPIList(this.constructor as any, nextOptions);\n  }\n\n  async *iterPages(): AsyncGenerator<this> {\n    let page: this = this;\n    yield page;\n    while (page.hasNextPage()) {\n      page = await page.getNextPage();\n      yield page;\n    }\n  }\n\n  async *[Symbol.asyncIterator](): AsyncGenerator<Item> {\n    for await (const page of this.iterPages()) {\n      for (const item of page.getPaginatedItems()) {\n        yield item;\n      }\n    }\n  }\n}\n\n/**\n * This subclass of Promise will resolve to an instantiated Page once the request completes.\n *\n * It also implements AsyncIterable to allow auto-paginating iteration on an unawaited list call, eg:\n *\n *    for await (const item of client.items.list()) {\n *      console.log(item)\n *    }\n */\nexport class PagePromise<\n    PageClass extends AbstractPage<Item>,\n    Item = ReturnType<PageClass['getPaginatedItems']>[number],\n  >\n  extends APIPromise<PageClass>\n  implements AsyncIterable<Item>\n{\n  constructor(\n    client: BaseAnthropic,\n    request: Promise<APIResponseProps>,\n    Page: new (...args: ConstructorParameters<typeof AbstractPage>) => PageClass,\n  ) {\n    super(\n      client,\n      request,\n      async (client, props) =>\n        new Page(\n          client,\n          props.response,\n          await defaultParseResponse(client, props),\n          props.options,\n        ) as WithRequestID<PageClass>,\n    );\n  }\n\n  /**\n   * Allow auto-paginating iteration on an unawaited list call, eg:\n   *\n   *    for await (const item of client.items.list()) {\n   *      console.log(item)\n   *    }\n   */\n  async *[Symbol.asyncIterator]() {\n    const page = await this;\n    for await (const item of page) {\n      yield item;\n    }\n  }\n}\n\nexport interface PageResponse<Item> {\n  data: Array<Item>;\n\n  has_more: boolean;\n\n  first_id: string | null;\n\n  last_id: string | null;\n}\n\nexport interface PageParams {\n  /**\n   * Number of items per page.\n   */\n  limit?: number;\n\n  before_id?: string;\n\n  after_id?: string;\n}\n\nexport class Page<Item> extends AbstractPage<Item> implements PageResponse<Item> {\n  data: Array<Item>;\n\n  has_more: boolean;\n\n  first_id: string | null;\n\n  last_id: string | null;\n\n  constructor(\n    client: BaseAnthropic,\n    response: Response,\n    body: PageResponse<Item>,\n    options: FinalRequestOptions,\n  ) {\n    super(client, response, body, options);\n\n    this.data = body.data || [];\n    this.has_more = body.has_more || false;\n    this.first_id = body.first_id || null;\n    this.last_id = body.last_id || null;\n  }\n\n  getPaginatedItems(): Item[] {\n    return this.data ?? [];\n  }\n\n  override hasNextPage(): boolean {\n    if (this.has_more === false) {\n      return false;\n    }\n\n    return super.hasNextPage();\n  }\n\n  nextPageRequestOptions(): PageRequestOptions | null {\n    if ((this.options.query as Record<string, unknown>)?.['before_id']) {\n      // in reverse\n      const first_id = this.first_id;\n      if (!first_id) {\n        return null;\n      }\n\n      return {\n        ...this.options,\n        query: {\n          ...maybeObj(this.options.query),\n          before_id: first_id,\n        },\n      };\n    }\n\n    const cursor = this.last_id;\n    if (!cursor) {\n      return null;\n    }\n\n    return {\n      ...this.options,\n      query: {\n        ...maybeObj(this.options.query),\n        after_id: cursor,\n      },\n    };\n  }\n}\n", "import { type RequestOptions } from './request-options';\nimport type { FilePropertyBag, Fetch } from './builtin-types';\nimport type { BaseAnthropic } from '../client';\nimport { ReadableStreamFrom } from './shims';\n\nexport type BlobPart = string | ArrayBuffer | ArrayBufferView | Blob | DataView;\ntype FsReadStream = AsyncIterable<Uint8Array> & { path: string | { toString(): string } };\n\n// https://github.com/oven-sh/bun/issues/5980\ninterface BunFile extends Blob {\n  readonly name?: string | undefined;\n}\n\nexport const checkFileSupport = () => {\n  if (typeof File === 'undefined') {\n    const { process } = globalThis as any;\n    const isOldNode =\n      typeof process?.versions?.node === 'string' && parseInt(process.versions.node.split('.')) < 20;\n    throw new Error(\n      '`File` is not defined as a global, which is required for file uploads.' +\n        (isOldNode ?\n          \" Update to Node 20 LTS or newer, or set `globalThis.File` to `import('node:buffer').File`.\"\n        : ''),\n    );\n  }\n};\n\n/**\n * Typically, this is a native \"File\" class.\n *\n * We provide the {@link toFile} utility to convert a variety of objects\n * into the File class.\n *\n * For convenience, you can also pass a fetch Response, or in Node,\n * the result of fs.createReadStream().\n */\nexport type Uploadable = File | Response | FsReadStream | BunFile;\n\n/**\n * Construct a `File` instance. This is used to ensure a helpful error is thrown\n * for environments that don't define a global `File` yet.\n */\nexport function makeFile(\n  fileBits: BlobPart[],\n  fileName: string | undefined,\n  options?: FilePropertyBag,\n): File {\n  checkFileSupport();\n  return new File(fileBits as any, fileName ?? 'unknown_file', options);\n}\n\nexport function getName(value: any): string | undefined {\n  return (\n    (\n      (typeof value === 'object' &&\n        value !== null &&\n        (('name' in value && value.name && String(value.name)) ||\n          ('url' in value && value.url && String(value.url)) ||\n          ('filename' in value && value.filename && String(value.filename)) ||\n          ('path' in value && value.path && String(value.path)))) ||\n      ''\n    )\n      .split(/[\\\\/]/)\n      .pop() || undefined\n  );\n}\n\nexport const isAsyncIterable = (value: any): value is AsyncIterable<any> =>\n  value != null && typeof value === 'object' && typeof value[Symbol.asyncIterator] === 'function';\n\n/**\n * Returns a multipart/form-data request if any part of the given request body contains a File / Blob value.\n * Otherwise returns the request as is.\n */\nexport const maybeMultipartFormRequestOptions = async (\n  opts: RequestOptions,\n  fetch: BaseAnthropic | Fetch,\n): Promise<RequestOptions> => {\n  if (!hasUploadableValue(opts.body)) return opts;\n\n  return { ...opts, body: await createForm(opts.body, fetch) };\n};\n\ntype MultipartFormRequestOptions = Omit<RequestOptions, 'body'> & { body: unknown };\n\nexport const multipartFormRequestOptions = async (\n  opts: MultipartFormRequestOptions,\n  fetch: BaseAnthropic | Fetch,\n): Promise<RequestOptions> => {\n  return { ...opts, body: await createForm(opts.body, fetch) };\n};\n\nconst supportsFormDataMap = new WeakMap<Fetch, Promise<boolean>>();\n\n/**\n * node-fetch doesn't support the global FormData object in recent node versions. Instead of sending\n * properly-encoded form data, it just stringifies the object, resulting in a request body of \"[object FormData]\".\n * This function detects if the fetch function provided supports the global FormData object to avoid\n * confusing error messages later on.\n */\nfunction supportsFormData(fetchObject: BaseAnthropic | Fetch): Promise<boolean> {\n  const fetch: Fetch = typeof fetchObject === 'function' ? fetchObject : (fetchObject as any).fetch;\n  const cached = supportsFormDataMap.get(fetch);\n  if (cached) return cached;\n  const promise = (async () => {\n    try {\n      const FetchResponse = (\n        'Response' in fetch ?\n          fetch.Response\n        : (await fetch('data:,')).constructor) as typeof Response;\n      const data = new FormData();\n      if (data.toString() === (await new FetchResponse(data).text())) {\n        return false;\n      }\n      return true;\n    } catch {\n      // avoid false negatives\n      return true;\n    }\n  })();\n  supportsFormDataMap.set(fetch, promise);\n  return promise;\n}\n\nexport const createForm = async <T = Record<string, unknown>>(\n  body: T | undefined,\n  fetch: BaseAnthropic | Fetch,\n): Promise<FormData> => {\n  if (!(await supportsFormData(fetch))) {\n    throw new TypeError(\n      'The provided fetch function does not support file uploads with the current global FormData class.',\n    );\n  }\n  const form = new FormData();\n  await Promise.all(Object.entries(body || {}).map(([key, value]) => addFormValue(form, key, value)));\n  return form;\n};\n\n// We check for Blob not File because Bun.File doesn't inherit from File,\n// but they both inherit from Blob and have a `name` property at runtime.\nconst isNamedBlob = (value: object): value is Blob => value instanceof Blob && 'name' in value;\n\nconst isUploadable = (value: unknown) =>\n  typeof value === 'object' &&\n  value !== null &&\n  (value instanceof Response || isAsyncIterable(value) || isNamedBlob(value));\n\nconst hasUploadableValue = (value: unknown): boolean => {\n  if (isUploadable(value)) return true;\n  if (Array.isArray(value)) return value.some(hasUploadableValue);\n  if (value && typeof value === 'object') {\n    for (const k in value) {\n      if (hasUploadableValue((value as any)[k])) return true;\n    }\n  }\n  return false;\n};\n\nconst addFormValue = async (form: FormData, key: string, value: unknown): Promise<void> => {\n  if (value === undefined) return;\n  if (value == null) {\n    throw new TypeError(\n      `Received null for \"${key}\"; to pass null in FormData, you must use the string 'null'`,\n    );\n  }\n\n  // TODO: make nested formats configurable\n  if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n    form.append(key, String(value));\n  } else if (value instanceof Response) {\n    let options = {} as FilePropertyBag;\n    const contentType = value.headers.get('Content-Type');\n    if (contentType) {\n      options = { type: contentType };\n    }\n\n    form.append(key, makeFile([await value.blob()], getName(value), options));\n  } else if (isAsyncIterable(value)) {\n    form.append(key, makeFile([await new Response(ReadableStreamFrom(value)).blob()], getName(value)));\n  } else if (isNamedBlob(value)) {\n    form.append(key, makeFile([value], getName(value), { type: value.type }));\n  } else if (Array.isArray(value)) {\n    await Promise.all(value.map((entry) => addFormValue(form, key + '[]', entry)));\n  } else if (typeof value === 'object') {\n    await Promise.all(\n      Object.entries(value).map(([name, prop]) => addFormValue(form, `${key}[${name}]`, prop)),\n    );\n  } else {\n    throw new TypeError(\n      `Invalid value given to form, expected a string, number, boolean, object, Array, File or Blob but got ${value} instead`,\n    );\n  }\n};\n", "import { BlobPart, getName, makeFile, isAsyncIterable } from './uploads';\nimport type { FilePropertyBag } from './builtin-types';\nimport { checkFileSupport } from './uploads';\n\ntype BlobLikePart = string | ArrayBuffer | ArrayBufferView | BlobLike | DataView;\n\n/**\n * Intended to match DOM Blob, node-fetch Blob, node:buffer Blob, etc.\n * Don't add arrayBuffer here, node-fetch doesn't have it\n */\ninterface BlobLike {\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/size) */\n  readonly size: number;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/type) */\n  readonly type: string;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/text) */\n  text(): Promise<string>;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/Blob/slice) */\n  slice(start?: number, end?: number): BlobLike;\n}\n\n/**\n * This check adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isBlobLike = (value: any): value is BlobLike & { arrayBuffer(): Promise<ArrayBuffer> } =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.size === 'number' &&\n  typeof value.type === 'string' &&\n  typeof value.text === 'function' &&\n  typeof value.slice === 'function' &&\n  typeof value.arrayBuffer === 'function';\n\n/**\n * Intended to match DOM File, node:buffer File, undici File, etc.\n */\ninterface FileLike extends BlobLike {\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/File/lastModified) */\n  readonly lastModified: number;\n  /** [MDN Reference](https://developer.mozilla.org/docs/Web/API/File/name) */\n  readonly name?: string | undefined;\n}\n\n/**\n * This check adds the arrayBuffer() method type because it is available and used at runtime\n */\nconst isFileLike = (value: any): value is FileLike & { arrayBuffer(): Promise<ArrayBuffer> } =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.name === 'string' &&\n  typeof value.lastModified === 'number' &&\n  isBlobLike(value);\n\n/**\n * Intended to match DOM Response, node-fetch Response, undici Response, etc.\n */\nexport interface ResponseLike {\n  url: string;\n  blob(): Promise<BlobLike>;\n}\n\nconst isResponseLike = (value: any): value is ResponseLike =>\n  value != null &&\n  typeof value === 'object' &&\n  typeof value.url === 'string' &&\n  typeof value.blob === 'function';\n\nexport type ToFileInput =\n  | FileLike\n  | ResponseLike\n  | Exclude<BlobLikePart, string>\n  | AsyncIterable<BlobLikePart>;\n\n/**\n * Helper for creating a {@link File} to pass to an SDK upload method from a variety of different data formats\n * @param value the raw content of the file.  Can be an {@link Uploadable}, {@link BlobLikePart}, or {@link AsyncIterable} of {@link BlobLikePart}s\n * @param {string=} name the name of the file. If omitted, toFile will try to determine a file name from bits if possible\n * @param {Object=} options additional properties\n * @param {string=} options.type the MIME type of the content\n * @param {number=} options.lastModified the last modified timestamp\n * @returns a {@link File} with the given properties\n */\nexport async function toFile(\n  value: ToFileInput | PromiseLike<ToFileInput>,\n  name?: string | null | undefined,\n  options?: FilePropertyBag | undefined,\n): Promise<File> {\n  checkFileSupport();\n\n  // If it's a promise, resolve it.\n  value = await value;\n\n  name ||= getName(value);\n\n  // If we've been given a `File` we don't need to do anything if the name / options\n  // have not been customised.\n  if (isFileLike(value)) {\n    if (value instanceof File && name == null && options == null) {\n      return value;\n    }\n    return makeFile([await value.arrayBuffer()], name ?? value.name, {\n      type: value.type,\n      lastModified: value.lastModified,\n      ...options,\n    });\n  }\n\n  if (isResponseLike(value)) {\n    const blob = await value.blob();\n    name ||= new URL(value.url).pathname.split(/[\\\\/]/).pop();\n\n    return makeFile(await getBytes(blob), name, options);\n  }\n\n  const parts = await getBytes(value);\n\n  if (!options?.type) {\n    const type = parts.find((part) => typeof part === 'object' && 'type' in part && part.type);\n    if (typeof type === 'string') {\n      options = { ...options, type };\n    }\n  }\n\n  return makeFile(parts, name, options);\n}\n\nasync function getBytes(value: BlobLikePart | AsyncIterable<BlobLikePart>): Promise<Array<BlobPart>> {\n  let parts: Array<BlobPart> = [];\n  if (\n    typeof value === 'string' ||\n    ArrayBuffer.isView(value) || // includes Uint8Array, Buffer, etc.\n    value instanceof ArrayBuffer\n  ) {\n    parts.push(value);\n  } else if (isBlobLike(value)) {\n    parts.push(value instanceof Blob ? value : await value.arrayBuffer());\n  } else if (\n    isAsyncIterable(value) // includes Readable, ReadableStream, etc.\n  ) {\n    for await (const chunk of value) {\n      parts.push(...(await getBytes(chunk as BlobLikePart))); // TODO, consider validating?\n    }\n  } else {\n    const constructor = value?.constructor?.name;\n    throw new Error(\n      `Unexpected data type: ${typeof value}${\n        constructor ? `; constructor: ${constructor}` : ''\n      }${propsForError(value)}`,\n    );\n  }\n\n  return parts;\n}\n\nfunction propsForError(value: unknown): string {\n  if (typeof value !== 'object' || value === null) return '';\n  const props = Object.getOwnPropertyNames(value);\n  return `; props: [${props.map((p) => `\"${p}\"`).join(', ')}]`;\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { BaseAnthropic } from '../client';\n\nexport class APIResource {\n  protected _client: BaseAnthropic;\n\n  constructor(client: BaseAnthropic) {\n    this._client = client;\n  }\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\ntype HeaderValue = string | undefined | null;\nexport type HeadersLike =\n  | Headers\n  | readonly HeaderValue[][]\n  | Record<string, HeaderValue | readonly HeaderValue[]>\n  | undefined\n  | null\n  | NullableHeaders;\n\nconst brand_privateNullableHeaders = Symbol.for('brand.privateNullableHeaders') as symbol & {\n  description: 'brand.privateNullableHeaders';\n};\n\n/**\n * @internal\n * Users can pass explicit nulls to unset default headers. When we parse them\n * into a standard headers type we need to preserve that information.\n */\nexport type NullableHeaders = {\n  /** Brand check, prevent users from creating a NullableHeaders. */\n  [_: typeof brand_privateNullableHeaders]: true;\n  /** Parsed headers. */\n  values: Headers;\n  /** Set of lowercase header names explicitly set to null. */\n  nulls: Set<string>;\n};\n\nconst isArray = Array.isArray as (val: unknown) => val is readonly unknown[];\n\nfunction* iterateHeaders(headers: HeadersLike): IterableIterator<readonly [string, string | null]> {\n  if (!headers) return;\n\n  if (brand_privateNullableHeaders in headers) {\n    const { values, nulls } = headers as NullableHeaders;\n    yield* values.entries();\n    for (const name of nulls) {\n      yield [name, null];\n    }\n    return;\n  }\n\n  let shouldClear = false;\n  let iter: Iterable<readonly (HeaderValue | readonly HeaderValue[])[]>;\n  if (headers instanceof Headers) {\n    iter = headers.entries();\n  } else if (isArray(headers)) {\n    iter = headers;\n  } else {\n    shouldClear = true;\n    iter = Object.entries(headers ?? {});\n  }\n  for (let row of iter) {\n    const name = row[0];\n    if (typeof name !== 'string') throw new TypeError('expected header name to be a string');\n    const values = isArray(row[1]) ? row[1] : [row[1]];\n    let didClear = false;\n    for (const value of values) {\n      if (value === undefined) continue;\n\n      // Objects keys always overwrite older headers, they never append.\n      // Yield a null to clear the header before adding the new values.\n      if (shouldClear && !didClear) {\n        didClear = true;\n        yield [name, null];\n      }\n      yield [name, value];\n    }\n  }\n}\n\nexport const buildHeaders = (newHeaders: HeadersLike[]): NullableHeaders => {\n  const targetHeaders = new Headers();\n  const nullHeaders = new Set<string>();\n  for (const headers of newHeaders) {\n    const seenHeaders = new Set<string>();\n    for (const [name, value] of iterateHeaders(headers)) {\n      const lowerName = name.toLowerCase();\n      if (!seenHeaders.has(lowerName)) {\n        targetHeaders.delete(name);\n        seenHeaders.add(lowerName);\n      }\n      if (value === null) {\n        targetHeaders.delete(name);\n        nullHeaders.add(lowerName);\n      } else {\n        targetHeaders.append(name, value);\n        nullHeaders.delete(lowerName);\n      }\n    }\n  }\n  return { [brand_privateNullableHeaders]: true, values: targetHeaders, nulls: nullHeaders };\n};\n\nexport const isEmptyHeaders = (headers: HeadersLike) => {\n  for (const _ of iterateHeaders(headers)) return false;\n  return true;\n};\n", "import { AnthropicError } from '../../core/error';\n\n/**\n * Percent-encode everything that isn't safe to have in a path without encoding safe chars.\n *\n * Taken from https://datatracker.ietf.org/doc/html/rfc3986#section-3.3:\n * > unreserved  = ALPHA / DIGIT / \"-\" / \".\" / \"_\" / \"~\"\n * > sub-delims  = \"!\" / \"$\" / \"&\" / \"'\" / \"(\" / \")\" / \"*\" / \"+\" / \",\" / \";\" / \"=\"\n * > pchar       = unreserved / pct-encoded / sub-delims / \":\" / \"@\"\n */\nexport function encodeURIPath(str: string) {\n  return str.replace(/[^A-Za-z0-9\\-._~!$&'()*+,;=:@]+/g, encodeURIComponent);\n}\n\nexport const createPathTagFunction = (pathEncoder = encodeURIPath) =>\n  function path(statics: readonly string[], ...params: readonly unknown[]): string {\n    // If there are no params, no processing is needed.\n    if (statics.length === 1) return statics[0]!;\n\n    let postPath = false;\n    const path = statics.reduce((previousValue, currentValue, index) => {\n      if (/[?#]/.test(currentValue)) {\n        postPath = true;\n      }\n      return (\n        previousValue +\n        currentValue +\n        (index === params.length ? '' : (postPath ? encodeURIComponent : pathEncoder)(String(params[index])))\n      );\n    }, '');\n\n    const pathOnly = path.split(/[?#]/, 1)[0]!;\n    const invalidSegments = [];\n    const invalidSegmentPattern = /(?<=^|\\/)(?:\\.|%2e){1,2}(?=\\/|$)/gi;\n    let match;\n\n    // Find all invalid segments\n    while ((match = invalidSegmentPattern.exec(pathOnly)) !== null) {\n      invalidSegments.push({\n        start: match.index,\n        length: match[0].length,\n      });\n    }\n\n    if (invalidSegments.length > 0) {\n      let lastEnd = 0;\n      const underline = invalidSegments.reduce((acc, segment) => {\n        const spaces = ' '.repeat(segment.start - lastEnd);\n        const arrows = '^'.repeat(segment.length);\n        lastEnd = segment.start + segment.length;\n        return acc + spaces + arrows;\n      }, '');\n\n      throw new AnthropicError(\n        `Path parameters result in path with invalid segments:\\n${path}\\n${underline}`,\n      );\n    }\n\n    return path;\n  };\n\n/**\n * URI-encodes path params and ensures no unsafe /./ or /../ path segments are introduced.\n */\nexport const path = createPathTagFunction(encodeURIPath);\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../../core/resource';\nimport * as BetaAPI from './beta';\nimport { APIPromise } from '../../core/api-promise';\nimport { Page, type PageParams, PagePromise } from '../../core/pagination';\nimport { type Uploadable } from '../../core/uploads';\nimport { buildHeaders } from '../../internal/headers';\nimport { RequestOptions } from '../../internal/request-options';\nimport { multipartFormRequestOptions } from '../../internal/uploads';\nimport { path } from '../../internal/utils/path';\n\nexport class Files extends APIResource {\n  /**\n   * List Files\n   *\n   * @example\n   * ```ts\n   * // Automatically fetches more pages as needed.\n   * for await (const fileMetadata of client.beta.files.list()) {\n   *   // ...\n   * }\n   * ```\n   */\n  list(\n    params: FileListParams | null | undefined = {},\n    options?: RequestOptions,\n  ): PagePromise<FileMetadataPage, FileMetadata> {\n    const { betas, ...query } = params ?? {};\n    return this._client.getAPIList('/v1/files', Page<FileMetadata>, {\n      query,\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'files-api-2025-04-14'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * Delete File\n   *\n   * @example\n   * ```ts\n   * const deletedFile = await client.beta.files.delete(\n   *   'file_id',\n   * );\n   * ```\n   */\n  delete(\n    fileID: string,\n    params: FileDeleteParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<DeletedFile> {\n    const { betas } = params ?? {};\n    return this._client.delete(path`/v1/files/${fileID}`, {\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'files-api-2025-04-14'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * Download File\n   *\n   * @example\n   * ```ts\n   * const response = await client.beta.files.download(\n   *   'file_id',\n   * );\n   *\n   * const content = await response.blob();\n   * console.log(content);\n   * ```\n   */\n  download(\n    fileID: string,\n    params: FileDownloadParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<Response> {\n    const { betas } = params ?? {};\n    return this._client.get(path`/v1/files/${fileID}/content`, {\n      ...options,\n      headers: buildHeaders([\n        {\n          'anthropic-beta': [...(betas ?? []), 'files-api-2025-04-14'].toString(),\n          Accept: 'application/binary',\n        },\n        options?.headers,\n      ]),\n      __binaryResponse: true,\n    });\n  }\n\n  /**\n   * Get File Metadata\n   *\n   * @example\n   * ```ts\n   * const fileMetadata =\n   *   await client.beta.files.retrieveMetadata('file_id');\n   * ```\n   */\n  retrieveMetadata(\n    fileID: string,\n    params: FileRetrieveMetadataParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<FileMetadata> {\n    const { betas } = params ?? {};\n    return this._client.get(path`/v1/files/${fileID}`, {\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'files-api-2025-04-14'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * Upload File\n   *\n   * @example\n   * ```ts\n   * const fileMetadata = await client.beta.files.upload({\n   *   file: fs.createReadStream('path/to/file'),\n   * });\n   * ```\n   */\n  upload(params: FileUploadParams, options?: RequestOptions): APIPromise<FileMetadata> {\n    const { betas, ...body } = params;\n    return this._client.post(\n      '/v1/files',\n      multipartFormRequestOptions(\n        {\n          body,\n          ...options,\n          headers: buildHeaders([\n            { 'anthropic-beta': [...(betas ?? []), 'files-api-2025-04-14'].toString() },\n            options?.headers,\n          ]),\n        },\n        this._client,\n      ),\n    );\n  }\n}\n\nexport type FileMetadataPage = Page<FileMetadata>;\n\nexport interface DeletedFile {\n  /**\n   * ID of the deleted file.\n   */\n  id: string;\n\n  /**\n   * Deleted object type.\n   *\n   * For file deletion, this is always `\"file_deleted\"`.\n   */\n  type?: 'file_deleted';\n}\n\nexport interface FileMetadata {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * RFC 3339 datetime string representing when the file was created.\n   */\n  created_at: string;\n\n  /**\n   * Original filename of the uploaded file.\n   */\n  filename: string;\n\n  /**\n   * MIME type of the file.\n   */\n  mime_type: string;\n\n  /**\n   * Size of the file in bytes.\n   */\n  size_bytes: number;\n\n  /**\n   * Object type.\n   *\n   * For files, this is always `\"file\"`.\n   */\n  type: 'file';\n\n  /**\n   * Whether the file can be downloaded.\n   */\n  downloadable?: boolean;\n}\n\nexport interface FileListParams extends PageParams {\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface FileDeleteParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface FileDownloadParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface FileRetrieveMetadataParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface FileUploadParams {\n  /**\n   * Body param: The file to upload\n   */\n  file: Uploadable;\n\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport declare namespace Files {\n  export {\n    type DeletedFile as DeletedFile,\n    type FileMetadata as FileMetadata,\n    type FileMetadataPage as FileMetadataPage,\n    type FileListParams as FileListParams,\n    type FileDeleteParams as FileDeleteParams,\n    type FileDownloadParams as FileDownloadParams,\n    type FileRetrieveMetadataParams as FileRetrieveMetadataParams,\n    type FileUploadParams as FileUploadParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../../core/resource';\nimport * as BetaAPI from './beta';\nimport { APIPromise } from '../../core/api-promise';\nimport { Page, type PageParams, PagePromise } from '../../core/pagination';\nimport { buildHeaders } from '../../internal/headers';\nimport { RequestOptions } from '../../internal/request-options';\nimport { path } from '../../internal/utils/path';\n\nexport class Models extends APIResource {\n  /**\n   * Get a specific model.\n   *\n   * The Models API response can be used to determine information about a specific\n   * model or resolve a model alias to a model ID.\n   *\n   * @example\n   * ```ts\n   * const betaModelInfo = await client.beta.models.retrieve(\n   *   'model_id',\n   * );\n   * ```\n   */\n  retrieve(\n    modelID: string,\n    params: ModelRetrieveParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<BetaModelInfo> {\n    const { betas } = params ?? {};\n    return this._client.get(path`/v1/models/${modelID}?beta=true`, {\n      ...options,\n      headers: buildHeaders([\n        { ...(betas?.toString() != null ? { 'anthropic-beta': betas?.toString() } : undefined) },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * List available models.\n   *\n   * The Models API response can be used to determine which models are available for\n   * use in the API. More recently released models are listed first.\n   *\n   * @example\n   * ```ts\n   * // Automatically fetches more pages as needed.\n   * for await (const betaModelInfo of client.beta.models.list()) {\n   *   // ...\n   * }\n   * ```\n   */\n  list(\n    params: ModelListParams | null | undefined = {},\n    options?: RequestOptions,\n  ): PagePromise<BetaModelInfosPage, BetaModelInfo> {\n    const { betas, ...query } = params ?? {};\n    return this._client.getAPIList('/v1/models?beta=true', Page<BetaModelInfo>, {\n      query,\n      ...options,\n      headers: buildHeaders([\n        { ...(betas?.toString() != null ? { 'anthropic-beta': betas?.toString() } : undefined) },\n        options?.headers,\n      ]),\n    });\n  }\n}\n\nexport type BetaModelInfosPage = Page<BetaModelInfo>;\n\nexport interface BetaModelInfo {\n  /**\n   * Unique model identifier.\n   */\n  id: string;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the model was released.\n   * May be set to an epoch value if the release date is unknown.\n   */\n  created_at: string;\n\n  /**\n   * A human-readable name for the model.\n   */\n  display_name: string;\n\n  /**\n   * Object type.\n   *\n   * For Models, this is always `\"model\"`.\n   */\n  type: 'model';\n}\n\nexport interface ModelRetrieveParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface ModelListParams extends PageParams {\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport declare namespace Models {\n  export {\n    type BetaModelInfo as BetaModelInfo,\n    type BetaModelInfosPage as BetaModelInfosPage,\n    type ModelRetrieveParams as ModelRetrieveParams,\n    type ModelListParams as ModelListParams,\n  };\n}\n", "import { AnthropicError } from '../../core/error';\nimport { ReadableStreamToAsyncIterable } from '../shims';\nimport { LineDecoder, type Bytes } from './line';\n\nexport class JSONLDecoder<T> {\n  controller: AbortController;\n\n  constructor(\n    private iterator: AsyncIterableIterator<Bytes>,\n    controller: AbortController,\n  ) {\n    this.controller = controller;\n  }\n\n  private async *decoder(): AsyncIterator<T, any, undefined> {\n    const lineDecoder = new LineDecoder();\n    for await (const chunk of this.iterator) {\n      for (const line of lineDecoder.decode(chunk)) {\n        yield JSON.parse(line);\n      }\n    }\n\n    for (const line of lineDecoder.flush()) {\n      yield JSON.parse(line);\n    }\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<T> {\n    return this.decoder();\n  }\n\n  static fromResponse<T>(response: Response, controller: AbortController): JSONLDecoder<T> {\n    if (!response.body) {\n      controller.abort();\n      if (\n        typeof (globalThis as any).navigator !== 'undefined' &&\n        (globalThis as any).navigator.product === 'ReactNative'\n      ) {\n        throw new AnthropicError(\n          `The default react-native fetch implementation does not support streaming. Please use expo/fetch: https://docs.expo.dev/versions/latest/sdk/expo/#expofetch-api`,\n        );\n      }\n      throw new AnthropicError(`Attempted to iterate over a response with no body`);\n    }\n\n    return new JSONLDecoder(ReadableStreamToAsyncIterable<Bytes>(response.body), controller);\n  }\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../../../core/resource';\nimport * as BetaAPI from '../beta';\nimport { APIPromise } from '../../../core/api-promise';\nimport * as BetaMessagesAPI from './messages';\nimport { Page, type PageParams, PagePromise } from '../../../core/pagination';\nimport { buildHeaders } from '../../../internal/headers';\nimport { RequestOptions } from '../../../internal/request-options';\nimport { JSONLDecoder } from '../../../internal/decoders/jsonl';\nimport { AnthropicError } from '../../../error';\nimport { path } from '../../../internal/utils/path';\n\nexport class Batches extends APIResource {\n  /**\n   * Send a batch of Message creation requests.\n   *\n   * The Message Batches API can be used to process multiple Messages API requests at\n   * once. Once a Message Batch is created, it begins processing immediately. Batches\n   * can take up to 24 hours to complete.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const betaMessageBatch =\n   *   await client.beta.messages.batches.create({\n   *     requests: [\n   *       {\n   *         custom_id: 'my-custom-id-1',\n   *         params: {\n   *           max_tokens: 1024,\n   *           messages: [\n   *             { content: 'Hello, world', role: 'user' },\n   *           ],\n   *           model: 'claude-3-7-sonnet-20250219',\n   *         },\n   *       },\n   *     ],\n   *   });\n   * ```\n   */\n  create(params: BatchCreateParams, options?: RequestOptions): APIPromise<BetaMessageBatch> {\n    const { betas, ...body } = params;\n    return this._client.post('/v1/messages/batches?beta=true', {\n      body,\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'message-batches-2024-09-24'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * This endpoint is idempotent and can be used to poll for Message Batch\n   * completion. To access the results of a Message Batch, make a request to the\n   * `results_url` field in the response.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const betaMessageBatch =\n   *   await client.beta.messages.batches.retrieve(\n   *     'message_batch_id',\n   *   );\n   * ```\n   */\n  retrieve(\n    messageBatchID: string,\n    params: BatchRetrieveParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<BetaMessageBatch> {\n    const { betas } = params ?? {};\n    return this._client.get(path`/v1/messages/batches/${messageBatchID}?beta=true`, {\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'message-batches-2024-09-24'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * List all Message Batches within a Workspace. Most recently created batches are\n   * returned first.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * // Automatically fetches more pages as needed.\n   * for await (const betaMessageBatch of client.beta.messages.batches.list()) {\n   *   // ...\n   * }\n   * ```\n   */\n  list(\n    params: BatchListParams | null | undefined = {},\n    options?: RequestOptions,\n  ): PagePromise<BetaMessageBatchesPage, BetaMessageBatch> {\n    const { betas, ...query } = params ?? {};\n    return this._client.getAPIList('/v1/messages/batches?beta=true', Page<BetaMessageBatch>, {\n      query,\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'message-batches-2024-09-24'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * Delete a Message Batch.\n   *\n   * Message Batches can only be deleted once they've finished processing. If you'd\n   * like to delete an in-progress batch, you must first cancel it.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const betaDeletedMessageBatch =\n   *   await client.beta.messages.batches.delete(\n   *     'message_batch_id',\n   *   );\n   * ```\n   */\n  delete(\n    messageBatchID: string,\n    params: BatchDeleteParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<BetaDeletedMessageBatch> {\n    const { betas } = params ?? {};\n    return this._client.delete(path`/v1/messages/batches/${messageBatchID}?beta=true`, {\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'message-batches-2024-09-24'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * Batches may be canceled any time before processing ends. Once cancellation is\n   * initiated, the batch enters a `canceling` state, at which time the system may\n   * complete any in-progress, non-interruptible requests before finalizing\n   * cancellation.\n   *\n   * The number of canceled requests is specified in `request_counts`. To determine\n   * which requests were canceled, check the individual results within the batch.\n   * Note that cancellation may not result in any canceled requests if they were\n   * non-interruptible.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const betaMessageBatch =\n   *   await client.beta.messages.batches.cancel(\n   *     'message_batch_id',\n   *   );\n   * ```\n   */\n  cancel(\n    messageBatchID: string,\n    params: BatchCancelParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<BetaMessageBatch> {\n    const { betas } = params ?? {};\n    return this._client.post(path`/v1/messages/batches/${messageBatchID}/cancel?beta=true`, {\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'message-batches-2024-09-24'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * Streams the results of a Message Batch as a `.jsonl` file.\n   *\n   * Each line in the file is a JSON object containing the result of a single request\n   * in the Message Batch. Results are not guaranteed to be in the same order as\n   * requests. Use the `custom_id` field to match results to requests.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const betaMessageBatchIndividualResponse =\n   *   await client.beta.messages.batches.results(\n   *     'message_batch_id',\n   *   );\n   * ```\n   */\n  async results(\n    messageBatchID: string,\n    params: BatchResultsParams | undefined = {},\n    options?: RequestOptions,\n  ): Promise<JSONLDecoder<BetaMessageBatchIndividualResponse>> {\n    const batch = await this.retrieve(messageBatchID);\n    if (!batch.results_url) {\n      throw new AnthropicError(\n        `No batch \\`results_url\\`; Has it finished processing? ${batch.processing_status} - ${batch.id}`,\n      );\n    }\n\n    const { betas } = params ?? {};\n    return this._client\n      .get(batch.results_url, {\n        ...options,\n        headers: buildHeaders([\n          {\n            'anthropic-beta': [...(betas ?? []), 'message-batches-2024-09-24'].toString(),\n            Accept: 'application/binary',\n          },\n          options?.headers,\n        ]),\n        stream: true,\n        __binaryResponse: true,\n      })\n      ._thenUnwrap((_, props) => JSONLDecoder.fromResponse(props.response, props.controller)) as APIPromise<\n      JSONLDecoder<BetaMessageBatchIndividualResponse>\n    >;\n  }\n}\n\nexport type BetaMessageBatchesPage = Page<BetaMessageBatch>;\n\nexport interface BetaDeletedMessageBatch {\n  /**\n   * ID of the Message Batch.\n   */\n  id: string;\n\n  /**\n   * Deleted object type.\n   *\n   * For Message Batches, this is always `\"message_batch_deleted\"`.\n   */\n  type: 'message_batch_deleted';\n}\n\nexport interface BetaMessageBatch {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the Message Batch was\n   * archived and its results became unavailable.\n   */\n  archived_at: string | null;\n\n  /**\n   * RFC 3339 datetime string representing the time at which cancellation was\n   * initiated for the Message Batch. Specified only if cancellation was initiated.\n   */\n  cancel_initiated_at: string | null;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the Message Batch was\n   * created.\n   */\n  created_at: string;\n\n  /**\n   * RFC 3339 datetime string representing the time at which processing for the\n   * Message Batch ended. Specified only once processing ends.\n   *\n   * Processing ends when every request in a Message Batch has either succeeded,\n   * errored, canceled, or expired.\n   */\n  ended_at: string | null;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the Message Batch will\n   * expire and end processing, which is 24 hours after creation.\n   */\n  expires_at: string;\n\n  /**\n   * Processing status of the Message Batch.\n   */\n  processing_status: 'in_progress' | 'canceling' | 'ended';\n\n  /**\n   * Tallies requests within the Message Batch, categorized by their status.\n   *\n   * Requests start as `processing` and move to one of the other statuses only once\n   * processing of the entire batch ends. The sum of all values always matches the\n   * total number of requests in the batch.\n   */\n  request_counts: BetaMessageBatchRequestCounts;\n\n  /**\n   * URL to a `.jsonl` file containing the results of the Message Batch requests.\n   * Specified only once processing ends.\n   *\n   * Results in the file are not guaranteed to be in the same order as requests. Use\n   * the `custom_id` field to match results to requests.\n   */\n  results_url: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Message Batches, this is always `\"message_batch\"`.\n   */\n  type: 'message_batch';\n}\n\nexport interface BetaMessageBatchCanceledResult {\n  type: 'canceled';\n}\n\nexport interface BetaMessageBatchErroredResult {\n  error: BetaAPI.BetaErrorResponse;\n\n  type: 'errored';\n}\n\nexport interface BetaMessageBatchExpiredResult {\n  type: 'expired';\n}\n\n/**\n * This is a single line in the response `.jsonl` file and does not represent the\n * response as a whole.\n */\nexport interface BetaMessageBatchIndividualResponse {\n  /**\n   * Developer-provided ID created for each request in a Message Batch. Useful for\n   * matching results to requests, as results may be given out of request order.\n   *\n   * Must be unique for each request within the Message Batch.\n   */\n  custom_id: string;\n\n  /**\n   * Processing result for this request.\n   *\n   * Contains a Message output if processing was successful, an error response if\n   * processing failed, or the reason why processing was not attempted, such as\n   * cancellation or expiration.\n   */\n  result: BetaMessageBatchResult;\n}\n\nexport interface BetaMessageBatchRequestCounts {\n  /**\n   * Number of requests in the Message Batch that have been canceled.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  canceled: number;\n\n  /**\n   * Number of requests in the Message Batch that encountered an error.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  errored: number;\n\n  /**\n   * Number of requests in the Message Batch that have expired.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  expired: number;\n\n  /**\n   * Number of requests in the Message Batch that are processing.\n   */\n  processing: number;\n\n  /**\n   * Number of requests in the Message Batch that have completed successfully.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  succeeded: number;\n}\n\n/**\n * Processing result for this request.\n *\n * Contains a Message output if processing was successful, an error response if\n * processing failed, or the reason why processing was not attempted, such as\n * cancellation or expiration.\n */\nexport type BetaMessageBatchResult =\n  | BetaMessageBatchSucceededResult\n  | BetaMessageBatchErroredResult\n  | BetaMessageBatchCanceledResult\n  | BetaMessageBatchExpiredResult;\n\nexport interface BetaMessageBatchSucceededResult {\n  message: BetaMessagesAPI.BetaMessage;\n\n  type: 'succeeded';\n}\n\nexport interface BatchCreateParams {\n  /**\n   * Body param: List of requests for prompt completion. Each is an individual\n   * request to create a Message.\n   */\n  requests: Array<BatchCreateParams.Request>;\n\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport namespace BatchCreateParams {\n  export interface Request {\n    /**\n     * Developer-provided ID created for each request in a Message Batch. Useful for\n     * matching results to requests, as results may be given out of request order.\n     *\n     * Must be unique for each request within the Message Batch.\n     */\n    custom_id: string;\n\n    /**\n     * Messages API creation parameters for the individual request.\n     *\n     * See the [Messages API reference](/en/api/messages) for full documentation on\n     * available parameters.\n     */\n    params: Omit<BetaMessagesAPI.MessageCreateParamsNonStreaming, 'betas'>;\n  }\n}\n\nexport interface BatchRetrieveParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface BatchListParams extends PageParams {\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface BatchDeleteParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface BatchCancelParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface BatchResultsParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport declare namespace Batches {\n  export {\n    type BetaDeletedMessageBatch as BetaDeletedMessageBatch,\n    type BetaMessageBatch as BetaMessageBatch,\n    type BetaMessageBatchCanceledResult as BetaMessageBatchCanceledResult,\n    type BetaMessageBatchErroredResult as BetaMessageBatchErroredResult,\n    type BetaMessageBatchExpiredResult as BetaMessageBatchExpiredResult,\n    type BetaMessageBatchIndividualResponse as BetaMessageBatchIndividualResponse,\n    type BetaMessageBatchRequestCounts as BetaMessageBatchRequestCounts,\n    type BetaMessageBatchResult as BetaMessageBatchResult,\n    type BetaMessageBatchSucceededResult as BetaMessageBatchSucceededResult,\n    type BetaMessageBatchesPage as BetaMessageBatchesPage,\n    type BatchCreateParams as BatchCreateParams,\n    type BatchRetrieveParams as BatchRetrieveParams,\n    type BatchListParams as BatchListParams,\n    type BatchDeleteParams as BatchDeleteParams,\n    type BatchCancelParams as BatchCancelParams,\n    type BatchResultsParams as BatchResultsParams,\n  };\n}\n", "type Token = {\n  type: string;\n  value: string;\n};\n\nconst tokenize = (input: string): Token[] => {\n    let current = 0;\n    let tokens: Token[] = [];\n\n    while (current < input.length) {\n      let char = input[current];\n\n      if (char === '\\\\') {\n        current++;\n        continue;\n      }\n\n      if (char === '{') {\n        tokens.push({\n          type: 'brace',\n          value: '{',\n        });\n\n        current++;\n        continue;\n      }\n\n      if (char === '}') {\n        tokens.push({\n          type: 'brace',\n          value: '}',\n        });\n\n        current++;\n        continue;\n      }\n\n      if (char === '[') {\n        tokens.push({\n          type: 'paren',\n          value: '[',\n        });\n\n        current++;\n        continue;\n      }\n\n      if (char === ']') {\n        tokens.push({\n          type: 'paren',\n          value: ']',\n        });\n\n        current++;\n        continue;\n      }\n\n      if (char === ':') {\n        tokens.push({\n          type: 'separator',\n          value: ':',\n        });\n\n        current++;\n        continue;\n      }\n\n      if (char === ',') {\n        tokens.push({\n          type: 'delimiter',\n          value: ',',\n        });\n\n        current++;\n        continue;\n      }\n\n      if (char === '\"') {\n        let value = '';\n        let danglingQuote = false;\n\n        char = input[++current];\n\n        while (char !== '\"') {\n          if (current === input.length) {\n            danglingQuote = true;\n            break;\n          }\n\n          if (char === '\\\\') {\n            current++;\n            if (current === input.length) {\n              danglingQuote = true;\n              break;\n            }\n            value += char + input[current];\n            char = input[++current];\n          } else {\n            value += char;\n            char = input[++current];\n          }\n        }\n\n        char = input[++current];\n\n        if (!danglingQuote) {\n          tokens.push({\n            type: 'string',\n            value,\n          });\n        }\n        continue;\n      }\n\n      let WHITESPACE = /\\s/;\n      if (char && WHITESPACE.test(char)) {\n        current++;\n        continue;\n      }\n\n      let NUMBERS = /[0-9]/;\n      if ((char && NUMBERS.test(char)) || char === '-' || char === '.') {\n        let value = '';\n\n        if (char === '-') {\n          value += char;\n          char = input[++current];\n        }\n\n        while ((char && NUMBERS.test(char)) || char === '.') {\n          value += char;\n          char = input[++current];\n        }\n\n        tokens.push({\n          type: 'number',\n          value,\n        });\n        continue;\n      }\n\n      let LETTERS = /[a-z]/i;\n      if (char && LETTERS.test(char)) {\n        let value = '';\n\n        while (char && LETTERS.test(char)) {\n          if (current === input.length) {\n            break;\n          }\n          value += char;\n          char = input[++current];\n        }\n\n        if (value == 'true' || value == 'false' || value === 'null') {\n          tokens.push({\n            type: 'name',\n            value,\n          });\n        } else {\n          // unknown token, e.g. `nul` which isn't quite `null`\n          current++;\n          continue;\n        }\n        continue;\n      }\n\n      current++;\n    }\n\n    return tokens;\n  },\n  strip = (tokens: Token[]): Token[] => {\n    if (tokens.length === 0) {\n      return tokens;\n    }\n\n    let lastToken = tokens[tokens.length - 1]!;\n\n    switch (lastToken.type) {\n      case 'separator':\n        tokens = tokens.slice(0, tokens.length - 1);\n        return strip(tokens);\n        break;\n      case 'number':\n        let lastCharacterOfLastToken = lastToken.value[lastToken.value.length - 1];\n        if (lastCharacterOfLastToken === '.' || lastCharacterOfLastToken === '-') {\n          tokens = tokens.slice(0, tokens.length - 1);\n          return strip(tokens);\n        }\n      case 'string':\n        let tokenBeforeTheLastToken = tokens[tokens.length - 2];\n        if (tokenBeforeTheLastToken?.type === 'delimiter') {\n          tokens = tokens.slice(0, tokens.length - 1);\n          return strip(tokens);\n        } else if (tokenBeforeTheLastToken?.type === 'brace' && tokenBeforeTheLastToken.value === '{') {\n          tokens = tokens.slice(0, tokens.length - 1);\n          return strip(tokens);\n        }\n        break;\n      case 'delimiter':\n        tokens = tokens.slice(0, tokens.length - 1);\n        return strip(tokens);\n        break;\n    }\n\n    return tokens;\n  },\n  unstrip = (tokens: Token[]): Token[] => {\n    let tail: string[] = [];\n\n    tokens.map((token) => {\n      if (token.type === 'brace') {\n        if (token.value === '{') {\n          tail.push('}');\n        } else {\n          tail.splice(tail.lastIndexOf('}'), 1);\n        }\n      }\n      if (token.type === 'paren') {\n        if (token.value === '[') {\n          tail.push(']');\n        } else {\n          tail.splice(tail.lastIndexOf(']'), 1);\n        }\n      }\n    });\n\n    if (tail.length > 0) {\n      tail.reverse().map((item) => {\n        if (item === '}') {\n          tokens.push({\n            type: 'brace',\n            value: '}',\n          });\n        } else if (item === ']') {\n          tokens.push({\n            type: 'paren',\n            value: ']',\n          });\n        }\n      });\n    }\n\n    return tokens;\n  },\n  generate = (tokens: Token[]): string => {\n    let output = '';\n\n    tokens.map((token) => {\n      switch (token.type) {\n        case 'string':\n          output += '\"' + token.value + '\"';\n          break;\n        default:\n          output += token.value;\n          break;\n      }\n    });\n\n    return output;\n  },\n  partialParse = (input: string): unknown => JSON.parse(generate(unstrip(strip(tokenize(input)))));\n\nexport { partialParse };\n", "import { isAbortError } from '../internal/errors';\nimport { AnthropicError, APIUserAbortError } from '../error';\nimport {\n  type BetaContentBlock,\n  Messages as BetaMessages,\n  type BetaMessage,\n  type BetaRawMessageStreamEvent as BetaMessageStreamEvent,\n  type BetaMessageParam,\n  type MessageCreateParams as BetaMessageCreateParams,\n  type MessageCreateParamsBase as BetaMessageCreateParamsBase,\n  type BetaTextBlock,\n  type BetaTextCitation,\n} from '../resources/beta/messages/messages';\nimport { Stream } from '../streaming';\nimport { partialParse } from '../_vendor/partial-json-parser/parser';\nimport { type RequestOptions } from '../internal/request-options';\nimport { type ReadableStream } from '../internal/shim-types';\n\nexport interface MessageStreamEvents {\n  connect: () => void;\n  streamEvent: (event: BetaMessageStreamEvent, snapshot: BetaMessage) => void;\n  text: (textDelta: string, textSnapshot: string) => void;\n  citation: (citation: BetaTextCitation, citationsSnapshot: BetaTextCitation[]) => void;\n  inputJson: (partialJson: string, jsonSnapshot: unknown) => void;\n  thinking: (thinkingDelta: string, thinkingSnapshot: string) => void;\n  signature: (signature: string) => void;\n  message: (message: BetaMessage) => void;\n  contentBlock: (content: BetaContentBlock) => void;\n  finalMessage: (message: BetaMessage) => void;\n  error: (error: AnthropicError) => void;\n  abort: (error: APIUserAbortError) => void;\n  end: () => void;\n}\n\ntype MessageStreamEventListeners<Event extends keyof MessageStreamEvents> = {\n  listener: MessageStreamEvents[Event];\n  once?: boolean;\n}[];\n\nconst JSON_BUF_PROPERTY = '__json_buf';\n\nexport class BetaMessageStream implements AsyncIterable<BetaMessageStreamEvent> {\n  messages: BetaMessageParam[] = [];\n  receivedMessages: BetaMessage[] = [];\n  #currentMessageSnapshot: BetaMessage | undefined;\n\n  controller: AbortController = new AbortController();\n\n  #connectedPromise: Promise<Response | null>;\n  #resolveConnectedPromise: (response: Response | null) => void = () => {};\n  #rejectConnectedPromise: (error: AnthropicError) => void = () => {};\n\n  #endPromise: Promise<void>;\n  #resolveEndPromise: () => void = () => {};\n  #rejectEndPromise: (error: AnthropicError) => void = () => {};\n\n  #listeners: { [Event in keyof MessageStreamEvents]?: MessageStreamEventListeners<Event> } = {};\n\n  #ended = false;\n  #errored = false;\n  #aborted = false;\n  #catchingPromiseCreated = false;\n  #response: Response | null | undefined;\n  #request_id: string | null | undefined;\n\n  constructor() {\n    this.#connectedPromise = new Promise<Response | null>((resolve, reject) => {\n      this.#resolveConnectedPromise = resolve;\n      this.#rejectConnectedPromise = reject;\n    });\n\n    this.#endPromise = new Promise<void>((resolve, reject) => {\n      this.#resolveEndPromise = resolve;\n      this.#rejectEndPromise = reject;\n    });\n\n    // Don't let these promises cause unhandled rejection errors.\n    // we will manually cause an unhandled rejection error later\n    // if the user hasn't registered any error listener or called\n    // any promise-returning method.\n    this.#connectedPromise.catch(() => {});\n    this.#endPromise.catch(() => {});\n  }\n\n  get response(): Response | null | undefined {\n    return this.#response;\n  }\n\n  get request_id(): string | null | undefined {\n    return this.#request_id;\n  }\n\n  /**\n   * Returns the `MessageStream` data, the raw `Response` instance and the ID of the request,\n   * returned vie the `request-id` header which is useful for debugging requests and resporting\n   * issues to Anthropic.\n   *\n   * This is the same as the `APIPromise.withResponse()` method.\n   *\n   * This method will raise an error if you created the stream using `MessageStream.fromReadableStream`\n   * as no `Response` is available.\n   */\n  async withResponse(): Promise<{\n    data: BetaMessageStream;\n    response: Response;\n    request_id: string | null | undefined;\n  }> {\n    const response = await this.#connectedPromise;\n    if (!response) {\n      throw new Error('Could not resolve a `Response` object');\n    }\n\n    return {\n      data: this,\n      response,\n      request_id: response.headers.get('request-id'),\n    };\n  }\n\n  /**\n   * Intended for use on the frontend, consuming a stream produced with\n   * `.toReadableStream()` on the backend.\n   *\n   * Note that messages sent to the model do not appear in `.on('message')`\n   * in this context.\n   */\n  static fromReadableStream(stream: ReadableStream): BetaMessageStream {\n    const runner = new BetaMessageStream();\n    runner._run(() => runner._fromReadableStream(stream));\n    return runner;\n  }\n\n  static createMessage(\n    messages: BetaMessages,\n    params: BetaMessageCreateParamsBase,\n    options?: RequestOptions,\n  ): BetaMessageStream {\n    const runner = new BetaMessageStream();\n    for (const message of params.messages) {\n      runner._addMessageParam(message);\n    }\n    runner._run(() =>\n      runner._createMessage(\n        messages,\n        { ...params, stream: true },\n        { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } },\n      ),\n    );\n    return runner;\n  }\n\n  protected _run(executor: () => Promise<any>) {\n    executor().then(() => {\n      this._emitFinal();\n      this._emit('end');\n    }, this.#handleError);\n  }\n\n  protected _addMessageParam(message: BetaMessageParam) {\n    this.messages.push(message);\n  }\n\n  protected _addMessage(message: BetaMessage, emit = true) {\n    this.receivedMessages.push(message);\n    if (emit) {\n      this._emit('message', message);\n    }\n  }\n\n  protected async _createMessage(\n    messages: BetaMessages,\n    params: BetaMessageCreateParams,\n    options?: RequestOptions,\n  ): Promise<void> {\n    const signal = options?.signal;\n    if (signal) {\n      if (signal.aborted) this.controller.abort();\n      signal.addEventListener('abort', () => this.controller.abort());\n    }\n    this.#beginRequest();\n    const { response, data: stream } = await messages\n      .create({ ...params, stream: true }, { ...options, signal: this.controller.signal })\n      .withResponse();\n    this._connected(response);\n    for await (const event of stream) {\n      this.#addStreamEvent(event);\n    }\n    if (stream.controller.signal?.aborted) {\n      throw new APIUserAbortError();\n    }\n    this.#endRequest();\n  }\n\n  protected _connected(response: Response | null) {\n    if (this.ended) return;\n    this.#response = response;\n    this.#request_id = response?.headers.get('request-id');\n    this.#resolveConnectedPromise(response);\n    this._emit('connect');\n  }\n\n  get ended(): boolean {\n    return this.#ended;\n  }\n\n  get errored(): boolean {\n    return this.#errored;\n  }\n\n  get aborted(): boolean {\n    return this.#aborted;\n  }\n\n  abort() {\n    this.controller.abort();\n  }\n\n  /**\n   * Adds the listener function to the end of the listeners array for the event.\n   * No checks are made to see if the listener has already been added. Multiple calls passing\n   * the same combination of event and listener will result in the listener being added, and\n   * called, multiple times.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  on<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners: MessageStreamEventListeners<Event> =\n      this.#listeners[event] || (this.#listeners[event] = []);\n    listeners.push({ listener });\n    return this;\n  }\n\n  /**\n   * Removes the specified listener from the listener array for the event.\n   * off() will remove, at most, one instance of a listener from the listener array. If any single\n   * listener has been added multiple times to the listener array for the specified event, then\n   * off() must be called multiple times to remove each instance.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  off<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners = this.#listeners[event];\n    if (!listeners) return this;\n    const index = listeners.findIndex((l) => l.listener === listener);\n    if (index >= 0) listeners.splice(index, 1);\n    return this;\n  }\n\n  /**\n   * Adds a one-time listener function for the event. The next time the event is triggered,\n   * this listener is removed and then invoked.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  once<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners: MessageStreamEventListeners<Event> =\n      this.#listeners[event] || (this.#listeners[event] = []);\n    listeners.push({ listener, once: true });\n    return this;\n  }\n\n  /**\n   * This is similar to `.once()`, but returns a Promise that resolves the next time\n   * the event is triggered, instead of calling a listener callback.\n   * @returns a Promise that resolves the next time given event is triggered,\n   * or rejects if an error is emitted.  (If you request the 'error' event,\n   * returns a promise that resolves with the error).\n   *\n   * Example:\n   *\n   *   const message = await stream.emitted('message') // rejects if the stream errors\n   */\n  emitted<Event extends keyof MessageStreamEvents>(\n    event: Event,\n  ): Promise<\n    Parameters<MessageStreamEvents[Event]> extends [infer Param] ? Param\n    : Parameters<MessageStreamEvents[Event]> extends [] ? void\n    : Parameters<MessageStreamEvents[Event]>\n  > {\n    return new Promise((resolve, reject) => {\n      this.#catchingPromiseCreated = true;\n      if (event !== 'error') this.once('error', reject);\n      this.once(event, resolve as any);\n    });\n  }\n\n  async done(): Promise<void> {\n    this.#catchingPromiseCreated = true;\n    await this.#endPromise;\n  }\n\n  get currentMessage(): BetaMessage | undefined {\n    return this.#currentMessageSnapshot;\n  }\n\n  #getFinalMessage(): BetaMessage {\n    if (this.receivedMessages.length === 0) {\n      throw new AnthropicError('stream ended without producing a Message with role=assistant');\n    }\n    return this.receivedMessages.at(-1)!;\n  }\n\n  /**\n   * @returns a promise that resolves with the the final assistant Message response,\n   * or rejects if an error occurred or the stream ended prematurely without producing a Message.\n   */\n  async finalMessage(): Promise<BetaMessage> {\n    await this.done();\n    return this.#getFinalMessage();\n  }\n\n  #getFinalText(): string {\n    if (this.receivedMessages.length === 0) {\n      throw new AnthropicError('stream ended without producing a Message with role=assistant');\n    }\n    const textBlocks = this.receivedMessages\n      .at(-1)!\n      .content.filter((block): block is BetaTextBlock => block.type === 'text')\n      .map((block) => block.text);\n    if (textBlocks.length === 0) {\n      throw new AnthropicError('stream ended without producing a content block with type=text');\n    }\n    return textBlocks.join(' ');\n  }\n\n  /**\n   * @returns a promise that resolves with the the final assistant Message's text response, concatenated\n   * together if there are more than one text blocks.\n   * Rejects if an error occurred or the stream ended prematurely without producing a Message.\n   */\n  async finalText(): Promise<string> {\n    await this.done();\n    return this.#getFinalText();\n  }\n\n  #handleError = (error: unknown) => {\n    this.#errored = true;\n    if (isAbortError(error)) {\n      error = new APIUserAbortError();\n    }\n    if (error instanceof APIUserAbortError) {\n      this.#aborted = true;\n      return this._emit('abort', error);\n    }\n    if (error instanceof AnthropicError) {\n      return this._emit('error', error);\n    }\n    if (error instanceof Error) {\n      const anthropicError: AnthropicError = new AnthropicError(error.message);\n      // @ts-ignore\n      anthropicError.cause = error;\n      return this._emit('error', anthropicError);\n    }\n    return this._emit('error', new AnthropicError(String(error)));\n  };\n\n  protected _emit<Event extends keyof MessageStreamEvents>(\n    event: Event,\n    ...args: Parameters<MessageStreamEvents[Event]>\n  ) {\n    // make sure we don't emit any MessageStreamEvents after end\n    if (this.#ended) return;\n\n    if (event === 'end') {\n      this.#ended = true;\n      this.#resolveEndPromise();\n    }\n\n    const listeners: MessageStreamEventListeners<Event> | undefined = this.#listeners[event];\n    if (listeners) {\n      this.#listeners[event] = listeners.filter((l) => !l.once) as any;\n      listeners.forEach(({ listener }: any) => listener(...args));\n    }\n\n    if (event === 'abort') {\n      const error = args[0] as APIUserAbortError;\n      if (!this.#catchingPromiseCreated && !listeners?.length) {\n        Promise.reject(error);\n      }\n      this.#rejectConnectedPromise(error);\n      this.#rejectEndPromise(error);\n      this._emit('end');\n      return;\n    }\n\n    if (event === 'error') {\n      // NOTE: _emit('error', error) should only be called from #handleError().\n\n      const error = args[0] as AnthropicError;\n      if (!this.#catchingPromiseCreated && !listeners?.length) {\n        // Trigger an unhandled rejection if the user hasn't registered any error handlers.\n        // If you are seeing stack traces here, make sure to handle errors via either:\n        // - runner.on('error', () => ...)\n        // - await runner.done()\n        // - await runner.final...()\n        // - etc.\n        Promise.reject(error);\n      }\n      this.#rejectConnectedPromise(error);\n      this.#rejectEndPromise(error);\n      this._emit('end');\n    }\n  }\n\n  protected _emitFinal() {\n    const finalMessage = this.receivedMessages.at(-1);\n    if (finalMessage) {\n      this._emit('finalMessage', this.#getFinalMessage());\n    }\n  }\n\n  #beginRequest() {\n    if (this.ended) return;\n    this.#currentMessageSnapshot = undefined;\n  }\n  #addStreamEvent(event: BetaMessageStreamEvent) {\n    if (this.ended) return;\n    const messageSnapshot = this.#accumulateMessage(event);\n    this._emit('streamEvent', event, messageSnapshot);\n\n    switch (event.type) {\n      case 'content_block_delta': {\n        const content = messageSnapshot.content.at(-1)!;\n        switch (event.delta.type) {\n          case 'text_delta': {\n            if (content.type === 'text') {\n              this._emit('text', event.delta.text, content.text || '');\n            }\n            break;\n          }\n          case 'citations_delta': {\n            if (content.type === 'text') {\n              this._emit('citation', event.delta.citation, content.citations ?? []);\n            }\n            break;\n          }\n          case 'input_json_delta': {\n            if ((content.type === 'tool_use' || content.type === 'mcp_tool_use') && content.input) {\n              this._emit('inputJson', event.delta.partial_json, content.input);\n            }\n            break;\n          }\n          case 'thinking_delta': {\n            if (content.type === 'thinking') {\n              this._emit('thinking', event.delta.thinking, content.thinking);\n            }\n            break;\n          }\n          case 'signature_delta': {\n            if (content.type === 'thinking') {\n              this._emit('signature', content.signature);\n            }\n            break;\n          }\n          default:\n            checkNever(event.delta);\n        }\n        break;\n      }\n      case 'message_stop': {\n        this._addMessageParam(messageSnapshot);\n        this._addMessage(messageSnapshot, true);\n        break;\n      }\n      case 'content_block_stop': {\n        this._emit('contentBlock', messageSnapshot.content.at(-1)!);\n        break;\n      }\n      case 'message_start': {\n        this.#currentMessageSnapshot = messageSnapshot;\n        break;\n      }\n      case 'content_block_start':\n      case 'message_delta':\n        break;\n    }\n  }\n  #endRequest(): BetaMessage {\n    if (this.ended) {\n      throw new AnthropicError(`stream has ended, this shouldn't happen`);\n    }\n    const snapshot = this.#currentMessageSnapshot;\n    if (!snapshot) {\n      throw new AnthropicError(`request ended without sending any chunks`);\n    }\n    this.#currentMessageSnapshot = undefined;\n    return snapshot;\n  }\n\n  protected async _fromReadableStream(\n    readableStream: ReadableStream,\n    options?: RequestOptions,\n  ): Promise<void> {\n    const signal = options?.signal;\n    if (signal) {\n      if (signal.aborted) this.controller.abort();\n      signal.addEventListener('abort', () => this.controller.abort());\n    }\n    this.#beginRequest();\n    this._connected(null);\n    const stream = Stream.fromReadableStream<BetaMessageStreamEvent>(readableStream, this.controller);\n    for await (const event of stream) {\n      this.#addStreamEvent(event);\n    }\n    if (stream.controller.signal?.aborted) {\n      throw new APIUserAbortError();\n    }\n    this.#endRequest();\n  }\n\n  /**\n   * Mutates this.#currentMessage with the current event. Handling the accumulation of multiple messages\n   * will be needed to be handled by the caller, this method will throw if you try to accumulate for multiple\n   * messages.\n   */\n  #accumulateMessage(event: BetaMessageStreamEvent): BetaMessage {\n    let snapshot = this.#currentMessageSnapshot;\n\n    if (event.type === 'message_start') {\n      if (snapshot) {\n        throw new AnthropicError(`Unexpected event order, got ${event.type} before receiving \"message_stop\"`);\n      }\n      return event.message;\n    }\n\n    if (!snapshot) {\n      throw new AnthropicError(`Unexpected event order, got ${event.type} before \"message_start\"`);\n    }\n\n    switch (event.type) {\n      case 'message_stop':\n        return snapshot;\n      case 'message_delta':\n        snapshot.container = event.delta.container;\n        snapshot.stop_reason = event.delta.stop_reason;\n        snapshot.stop_sequence = event.delta.stop_sequence;\n        snapshot.usage.output_tokens = event.usage.output_tokens;\n\n        if (event.usage.input_tokens != null) {\n          snapshot.usage.input_tokens = event.usage.input_tokens;\n        }\n\n        if (event.usage.cache_creation_input_tokens != null) {\n          snapshot.usage.cache_creation_input_tokens = event.usage.cache_creation_input_tokens;\n        }\n\n        if (event.usage.cache_read_input_tokens != null) {\n          snapshot.usage.cache_read_input_tokens = event.usage.cache_read_input_tokens;\n        }\n\n        if (event.usage.server_tool_use != null) {\n          snapshot.usage.server_tool_use = event.usage.server_tool_use;\n        }\n\n        return snapshot;\n      case 'content_block_start':\n        snapshot.content.push(event.content_block);\n        return snapshot;\n      case 'content_block_delta': {\n        const snapshotContent = snapshot.content.at(event.index);\n\n        switch (event.delta.type) {\n          case 'text_delta': {\n            if (snapshotContent?.type === 'text') {\n              snapshotContent.text += event.delta.text;\n            }\n            break;\n          }\n          case 'citations_delta': {\n            if (snapshotContent?.type === 'text') {\n              snapshotContent.citations ??= [];\n              snapshotContent.citations.push(event.delta.citation);\n            }\n            break;\n          }\n          case 'input_json_delta': {\n            if (snapshotContent?.type === 'tool_use' || snapshotContent?.type === 'mcp_tool_use') {\n              // we need to keep track of the raw JSON string as well so that we can\n              // re-parse it for each delta, for now we just store it as an untyped\n              // non-enumerable property on the snapshot\n              let jsonBuf = (snapshotContent as any)[JSON_BUF_PROPERTY] || '';\n              jsonBuf += event.delta.partial_json;\n\n              Object.defineProperty(snapshotContent, JSON_BUF_PROPERTY, {\n                value: jsonBuf,\n                enumerable: false,\n                writable: true,\n              });\n\n              if (jsonBuf) {\n                snapshotContent.input = partialParse(jsonBuf);\n              }\n            }\n            break;\n          }\n          case 'thinking_delta': {\n            if (snapshotContent?.type === 'thinking') {\n              snapshotContent.thinking += event.delta.thinking;\n            }\n            break;\n          }\n          case 'signature_delta': {\n            if (snapshotContent?.type === 'thinking') {\n              snapshotContent.signature = event.delta.signature;\n            }\n            break;\n          }\n          default:\n            checkNever(event.delta);\n        }\n        return snapshot;\n      }\n      case 'content_block_stop':\n        return snapshot;\n    }\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<BetaMessageStreamEvent> {\n    const pushQueue: BetaMessageStreamEvent[] = [];\n    const readQueue: {\n      resolve: (chunk: BetaMessageStreamEvent | undefined) => void;\n      reject: (error: unknown) => void;\n    }[] = [];\n    let done = false;\n\n    this.on('streamEvent', (event) => {\n      const reader = readQueue.shift();\n      if (reader) {\n        reader.resolve(event);\n      } else {\n        pushQueue.push(event);\n      }\n    });\n\n    this.on('end', () => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.resolve(undefined);\n      }\n      readQueue.length = 0;\n    });\n\n    this.on('abort', (err) => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.reject(err);\n      }\n      readQueue.length = 0;\n    });\n\n    this.on('error', (err) => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.reject(err);\n      }\n      readQueue.length = 0;\n    });\n\n    return {\n      next: async (): Promise<IteratorResult<BetaMessageStreamEvent>> => {\n        if (!pushQueue.length) {\n          if (done) {\n            return { value: undefined, done: true };\n          }\n          return new Promise<BetaMessageStreamEvent | undefined>((resolve, reject) =>\n            readQueue.push({ resolve, reject }),\n          ).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));\n        }\n        const chunk = pushQueue.shift()!;\n        return { value: chunk, done: false };\n      },\n      return: async () => {\n        this.abort();\n        return { value: undefined, done: true };\n      },\n    };\n  }\n\n  toReadableStream(): ReadableStream {\n    const stream = new Stream(this[Symbol.asyncIterator].bind(this), this.controller);\n    return stream.toReadableStream();\n  }\n}\n\n// used to ensure exhaustive case matching without throwing a runtime error\nfunction checkNever(x: never) {}\n", "// File containing shared constants\n\n/**\n * Model-specific timeout constraints for non-streaming requests\n */\nexport const MODEL_NONSTREAMING_TOKENS: Record<string, number> = {\n  'claude-opus-4-20250514': 8192,\n  'claude-opus-4-0': 8192,\n  'claude-4-opus-20250514': 8192,\n  'anthropic.claude-opus-4-20250514-v1:0': 8192,\n  'claude-opus-4@20250514': 8192,\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../../../core/resource';\nimport * as MessagesMessagesAPI from './messages';\nimport * as BetaAPI from '../beta';\nimport * as MessagesAPI from '../../messages/messages';\nimport * as BatchesAPI from './batches';\nimport {\n  BatchCancelParams,\n  BatchCreateParams,\n  BatchDeleteParams,\n  BatchListParams,\n  BatchResultsParams,\n  BatchRetrieveParams,\n  Batches,\n  BetaDeletedMessageBatch,\n  BetaMessageBatch,\n  BetaMessageBatchCanceledResult,\n  BetaMessageBatchErroredResult,\n  BetaMessageBatchExpiredResult,\n  BetaMessageBatchIndividualResponse,\n  BetaMessageBatchRequestCounts,\n  BetaMessageBatchResult,\n  BetaMessageBatchSucceededResult,\n  BetaMessageBatchesPage,\n} from './batches';\nimport { APIPromise } from '../../../core/api-promise';\nimport { Stream } from '../../../core/streaming';\nimport { buildHeaders } from '../../../internal/headers';\nimport { RequestOptions } from '../../../internal/request-options';\nimport type { Model } from '../../messages/messages';\nimport { BetaMessageStream } from '../../../lib/BetaMessageStream';\n\nconst DEPRECATED_MODELS: {\n  [K in Model]?: string;\n} = {\n  'claude-1.3': 'November 6th, 2024',\n  'claude-1.3-100k': 'November 6th, 2024',\n  'claude-instant-1.1': 'November 6th, 2024',\n  'claude-instant-1.1-100k': 'November 6th, 2024',\n  'claude-instant-1.2': 'November 6th, 2024',\n  'claude-3-sonnet-20240229': 'July 21st, 2025',\n  'claude-2.1': 'July 21st, 2025',\n  'claude-2.0': 'July 21st, 2025',\n};\nimport { MODEL_NONSTREAMING_TOKENS } from '../../../internal/constants';\n\nexport class Messages extends APIResource {\n  batches: BatchesAPI.Batches = new BatchesAPI.Batches(this._client);\n\n  /**\n   * Send a structured list of input messages with text and/or image content, and the\n   * model will generate the next message in the conversation.\n   *\n   * The Messages API can be used for either single queries or stateless multi-turn\n   * conversations.\n   *\n   * Learn more about the Messages API in our [user guide](/en/docs/initial-setup)\n   *\n   * @example\n   * ```ts\n   * const betaMessage = await client.beta.messages.create({\n   *   max_tokens: 1024,\n   *   messages: [{ content: 'Hello, world', role: 'user' }],\n   *   model: 'claude-3-7-sonnet-20250219',\n   * });\n   * ```\n   */\n  create(params: MessageCreateParamsNonStreaming, options?: RequestOptions): APIPromise<BetaMessage>;\n  create(\n    params: MessageCreateParamsStreaming,\n    options?: RequestOptions,\n  ): APIPromise<Stream<BetaRawMessageStreamEvent>>;\n  create(\n    params: MessageCreateParamsBase,\n    options?: RequestOptions,\n  ): APIPromise<Stream<BetaRawMessageStreamEvent> | BetaMessage>;\n  create(\n    params: MessageCreateParams,\n    options?: RequestOptions,\n  ): APIPromise<BetaMessage> | APIPromise<Stream<BetaRawMessageStreamEvent>> {\n    const { betas, ...body } = params;\n\n    if (body.model in DEPRECATED_MODELS) {\n      console.warn(\n        `The model '${body.model}' is deprecated and will reach end-of-life on ${\n          DEPRECATED_MODELS[body.model]\n        }\\nPlease migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`,\n      );\n    }\n\n    let timeout = (this._client as any)._options.timeout as number | null;\n    if (!body.stream && timeout == null) {\n      const maxNonstreamingTokens = MODEL_NONSTREAMING_TOKENS[body.model] ?? undefined;\n      timeout = this._client.calculateNonstreamingTimeout(body.max_tokens, maxNonstreamingTokens);\n    }\n    return this._client.post('/v1/messages?beta=true', {\n      body,\n      timeout: timeout ?? 600000,\n      ...options,\n      headers: buildHeaders([\n        { ...(betas?.toString() != null ? { 'anthropic-beta': betas?.toString() } : undefined) },\n        options?.headers,\n      ]),\n      stream: params.stream ?? false,\n    }) as APIPromise<BetaMessage> | APIPromise<Stream<BetaRawMessageStreamEvent>>;\n  }\n\n  /**\n   * Create a Message stream\n   */\n  stream(body: BetaMessageStreamParams, options?: RequestOptions): BetaMessageStream {\n    return BetaMessageStream.createMessage(this, body, options);\n  }\n\n  /**\n   * Count the number of tokens in a Message.\n   *\n   * The Token Count API can be used to count the number of tokens in a Message,\n   * including tools, images, and documents, without creating it.\n   *\n   * Learn more about token counting in our\n   * [user guide](/en/docs/build-with-claude/token-counting)\n   *\n   * @example\n   * ```ts\n   * const betaMessageTokensCount =\n   *   await client.beta.messages.countTokens({\n   *     messages: [{ content: 'string', role: 'user' }],\n   *     model: 'claude-3-7-sonnet-latest',\n   *   });\n   * ```\n   */\n  countTokens(\n    params: MessageCountTokensParams,\n    options?: RequestOptions,\n  ): APIPromise<BetaMessageTokensCount> {\n    const { betas, ...body } = params;\n    return this._client.post('/v1/messages/count_tokens?beta=true', {\n      body,\n      ...options,\n      headers: buildHeaders([\n        { 'anthropic-beta': [...(betas ?? []), 'token-counting-2024-11-01'].toString() },\n        options?.headers,\n      ]),\n    });\n  }\n}\n\nexport type BetaMessageStreamParams = MessageCreateParamsBase;\n\nexport interface BetaBase64ImageSource {\n  data: string;\n\n  media_type: 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp';\n\n  type: 'base64';\n}\n\nexport interface BetaBase64PDFBlock {\n  source:\n    | BetaBase64PDFSource\n    | BetaPlainTextSource\n    | BetaContentBlockSource\n    | BetaURLPDFSource\n    | BetaFileDocumentSource;\n\n  type: 'document';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  citations?: BetaCitationsConfigParam;\n\n  context?: string | null;\n\n  title?: string | null;\n}\n\nexport interface BetaBase64PDFSource {\n  data: string;\n\n  media_type: 'application/pdf';\n\n  type: 'base64';\n}\n\nexport interface BetaCacheControlEphemeral {\n  type: 'ephemeral';\n\n  /**\n   * The time-to-live for the cache control breakpoint.\n   *\n   * This may be one the following values:\n   *\n   * - `5m`: 5 minutes\n   * - `1h`: 1 hour\n   *\n   * Defaults to `5m`.\n   */\n  ttl?: '5m' | '1h';\n}\n\nexport interface BetaCacheCreation {\n  /**\n   * The number of input tokens used to create the 1 hour cache entry.\n   */\n  ephemeral_1h_input_tokens: number;\n\n  /**\n   * The number of input tokens used to create the 5 minute cache entry.\n   */\n  ephemeral_5m_input_tokens: number;\n}\n\nexport interface BetaCitationCharLocation {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_char_index: number;\n\n  start_char_index: number;\n\n  type: 'char_location';\n}\n\nexport interface BetaCitationCharLocationParam {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_char_index: number;\n\n  start_char_index: number;\n\n  type: 'char_location';\n}\n\nexport interface BetaCitationContentBlockLocation {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_block_index: number;\n\n  start_block_index: number;\n\n  type: 'content_block_location';\n}\n\nexport interface BetaCitationContentBlockLocationParam {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_block_index: number;\n\n  start_block_index: number;\n\n  type: 'content_block_location';\n}\n\nexport interface BetaCitationPageLocation {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_page_number: number;\n\n  start_page_number: number;\n\n  type: 'page_location';\n}\n\nexport interface BetaCitationPageLocationParam {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_page_number: number;\n\n  start_page_number: number;\n\n  type: 'page_location';\n}\n\nexport interface BetaCitationWebSearchResultLocationParam {\n  cited_text: string;\n\n  encrypted_index: string;\n\n  title: string | null;\n\n  type: 'web_search_result_location';\n\n  url: string;\n}\n\nexport interface BetaCitationsConfigParam {\n  enabled?: boolean;\n}\n\nexport interface BetaCitationsDelta {\n  citation:\n    | BetaCitationCharLocation\n    | BetaCitationPageLocation\n    | BetaCitationContentBlockLocation\n    | BetaCitationsWebSearchResultLocation;\n\n  type: 'citations_delta';\n}\n\nexport interface BetaCitationsWebSearchResultLocation {\n  cited_text: string;\n\n  encrypted_index: string;\n\n  title: string | null;\n\n  type: 'web_search_result_location';\n\n  url: string;\n}\n\nexport interface BetaCodeExecutionOutputBlock {\n  file_id: string;\n\n  type: 'code_execution_output';\n}\n\nexport interface BetaCodeExecutionOutputBlockParam {\n  file_id: string;\n\n  type: 'code_execution_output';\n}\n\nexport interface BetaCodeExecutionResultBlock {\n  content: Array<BetaCodeExecutionOutputBlock>;\n\n  return_code: number;\n\n  stderr: string;\n\n  stdout: string;\n\n  type: 'code_execution_result';\n}\n\nexport interface BetaCodeExecutionResultBlockParam {\n  content: Array<BetaCodeExecutionOutputBlockParam>;\n\n  return_code: number;\n\n  stderr: string;\n\n  stdout: string;\n\n  type: 'code_execution_result';\n}\n\nexport interface BetaCodeExecutionTool20250522 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'code_execution';\n\n  type: 'code_execution_20250522';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaCodeExecutionToolResultBlock {\n  content: BetaCodeExecutionToolResultBlockContent;\n\n  tool_use_id: string;\n\n  type: 'code_execution_tool_result';\n}\n\nexport type BetaCodeExecutionToolResultBlockContent =\n  | BetaCodeExecutionToolResultError\n  | BetaCodeExecutionResultBlock;\n\nexport interface BetaCodeExecutionToolResultBlockParam {\n  content: BetaCodeExecutionToolResultBlockParamContent;\n\n  tool_use_id: string;\n\n  type: 'code_execution_tool_result';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport type BetaCodeExecutionToolResultBlockParamContent =\n  | BetaCodeExecutionToolResultErrorParam\n  | BetaCodeExecutionResultBlockParam;\n\nexport interface BetaCodeExecutionToolResultError {\n  error_code: BetaCodeExecutionToolResultErrorCode;\n\n  type: 'code_execution_tool_result_error';\n}\n\nexport type BetaCodeExecutionToolResultErrorCode =\n  | 'invalid_tool_input'\n  | 'unavailable'\n  | 'too_many_requests'\n  | 'execution_time_exceeded';\n\nexport interface BetaCodeExecutionToolResultErrorParam {\n  error_code: BetaCodeExecutionToolResultErrorCode;\n\n  type: 'code_execution_tool_result_error';\n}\n\n/**\n * Information about the container used in the request (for the code execution\n * tool)\n */\nexport interface BetaContainer {\n  /**\n   * Identifier for the container used in this request\n   */\n  id: string;\n\n  /**\n   * The time at which the container will expire.\n   */\n  expires_at: string;\n}\n\n/**\n * Response model for a file uploaded to the container.\n */\nexport interface BetaContainerUploadBlock {\n  file_id: string;\n\n  type: 'container_upload';\n}\n\n/**\n * A content block that represents a file to be uploaded to the container Files\n * uploaded via this block will be available in the container's input directory.\n */\nexport interface BetaContainerUploadBlockParam {\n  file_id: string;\n\n  type: 'container_upload';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\n/**\n * Response model for a file uploaded to the container.\n */\nexport type BetaContentBlock =\n  | BetaTextBlock\n  | BetaToolUseBlock\n  | BetaServerToolUseBlock\n  | BetaWebSearchToolResultBlock\n  | BetaCodeExecutionToolResultBlock\n  | BetaMCPToolUseBlock\n  | BetaMCPToolResultBlock\n  | BetaContainerUploadBlock\n  | BetaThinkingBlock\n  | BetaRedactedThinkingBlock;\n\n/**\n * Regular text content.\n */\nexport type BetaContentBlockParam =\n  | BetaServerToolUseBlockParam\n  | BetaWebSearchToolResultBlockParam\n  | BetaCodeExecutionToolResultBlockParam\n  | BetaMCPToolUseBlockParam\n  | BetaRequestMCPToolResultBlockParam\n  | BetaTextBlockParam\n  | BetaImageBlockParam\n  | BetaToolUseBlockParam\n  | BetaToolResultBlockParam\n  | BetaBase64PDFBlock\n  | BetaThinkingBlockParam\n  | BetaRedactedThinkingBlockParam\n  | BetaContainerUploadBlockParam;\n\nexport interface BetaContentBlockSource {\n  content: string | Array<BetaContentBlockSourceContent>;\n\n  type: 'content';\n}\n\nexport type BetaContentBlockSourceContent = BetaTextBlockParam | BetaImageBlockParam;\n\nexport interface BetaFileDocumentSource {\n  file_id: string;\n\n  type: 'file';\n}\n\nexport interface BetaFileImageSource {\n  file_id: string;\n\n  type: 'file';\n}\n\nexport interface BetaImageBlockParam {\n  source: BetaBase64ImageSource | BetaURLImageSource | BetaFileImageSource;\n\n  type: 'image';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaInputJSONDelta {\n  partial_json: string;\n\n  type: 'input_json_delta';\n}\n\nexport interface BetaMCPToolResultBlock {\n  content: string | Array<BetaTextBlock>;\n\n  is_error: boolean;\n\n  tool_use_id: string;\n\n  type: 'mcp_tool_result';\n}\n\nexport interface BetaMCPToolUseBlock {\n  id: string;\n\n  input: unknown;\n\n  /**\n   * The name of the MCP tool\n   */\n  name: string;\n\n  /**\n   * The name of the MCP server\n   */\n  server_name: string;\n\n  type: 'mcp_tool_use';\n}\n\nexport interface BetaMCPToolUseBlockParam {\n  id: string;\n\n  input: unknown;\n\n  name: string;\n\n  /**\n   * The name of the MCP server\n   */\n  server_name: string;\n\n  type: 'mcp_tool_use';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaMessage {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * Information about the container used in the request (for the code execution\n   * tool)\n   */\n  container: BetaContainer | null;\n\n  /**\n   * Content generated by the model.\n   *\n   * This is an array of content blocks, each of which has a `type` that determines\n   * its shape.\n   *\n   * Example:\n   *\n   * ```json\n   * [{ \"type\": \"text\", \"text\": \"Hi, I'm Claude.\" }]\n   * ```\n   *\n   * If the request input `messages` ended with an `assistant` turn, then the\n   * response `content` will continue directly from that last turn. You can use this\n   * to constrain the model's output.\n   *\n   * For example, if the input `messages` were:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Then the response `content` might be:\n   *\n   * ```json\n   * [{ \"type\": \"text\", \"text\": \"B)\" }]\n   * ```\n   */\n  content: Array<BetaContentBlock>;\n\n  /**\n   * The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: MessagesAPI.Model;\n\n  /**\n   * Conversational role of the generated message.\n   *\n   * This will always be `\"assistant\"`.\n   */\n  role: 'assistant';\n\n  /**\n   * The reason that we stopped.\n   *\n   * This may be one the following values:\n   *\n   * - `\"end_turn\"`: the model reached a natural stopping point\n   * - `\"max_tokens\"`: we exceeded the requested `max_tokens` or the model's maximum\n   * - `\"stop_sequence\"`: one of your provided custom `stop_sequences` was generated\n   * - `\"tool_use\"`: the model invoked one or more tools\n   *\n   * In non-streaming mode this value is always non-null. In streaming mode, it is\n   * null in the `message_start` event and non-null otherwise.\n   */\n  stop_reason: BetaStopReason | null;\n\n  /**\n   * Which custom stop sequence was generated, if any.\n   *\n   * This value will be a non-null string if one of your custom stop sequences was\n   * generated.\n   */\n  stop_sequence: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Messages, this is always `\"message\"`.\n   */\n  type: 'message';\n\n  /**\n   * Billing and rate-limit usage.\n   *\n   * Anthropic's API bills and rate-limits by token counts, as tokens represent the\n   * underlying cost to our systems.\n   *\n   * Under the hood, the API transforms requests into a format suitable for the\n   * model. The model's output then goes through a parsing stage before becoming an\n   * API response. As a result, the token counts in `usage` will not match one-to-one\n   * with the exact visible content of an API request or response.\n   *\n   * For example, `output_tokens` will be non-zero, even for an empty string response\n   * from Claude.\n   *\n   * Total input tokens in a request is the summation of `input_tokens`,\n   * `cache_creation_input_tokens`, and `cache_read_input_tokens`.\n   */\n  usage: BetaUsage;\n}\n\nexport interface BetaMessageDeltaUsage {\n  /**\n   * The cumulative number of input tokens used to create the cache entry.\n   */\n  cache_creation_input_tokens: number | null;\n\n  /**\n   * The cumulative number of input tokens read from the cache.\n   */\n  cache_read_input_tokens: number | null;\n\n  /**\n   * The cumulative number of input tokens which were used.\n   */\n  input_tokens: number | null;\n\n  /**\n   * The cumulative number of output tokens which were used.\n   */\n  output_tokens: number;\n\n  /**\n   * The number of server tool requests.\n   */\n  server_tool_use: BetaServerToolUsage | null;\n}\n\nexport interface BetaMessageParam {\n  content: string | Array<BetaContentBlockParam>;\n\n  role: 'user' | 'assistant';\n}\n\nexport interface BetaMessageTokensCount {\n  /**\n   * The total number of tokens across the provided list of messages, system prompt,\n   * and tools.\n   */\n  input_tokens: number;\n}\n\nexport interface BetaMetadata {\n  /**\n   * An external identifier for the user who is associated with the request.\n   *\n   * This should be a uuid, hash value, or other opaque identifier. Anthropic may use\n   * this id to help detect abuse. Do not include any identifying information such as\n   * name, email address, or phone number.\n   */\n  user_id?: string | null;\n}\n\nexport interface BetaPlainTextSource {\n  data: string;\n\n  media_type: 'text/plain';\n\n  type: 'text';\n}\n\nexport type BetaRawContentBlockDelta =\n  | BetaTextDelta\n  | BetaInputJSONDelta\n  | BetaCitationsDelta\n  | BetaThinkingDelta\n  | BetaSignatureDelta;\n\nexport interface BetaRawContentBlockDeltaEvent {\n  delta: BetaRawContentBlockDelta;\n\n  index: number;\n\n  type: 'content_block_delta';\n}\n\nexport interface BetaRawContentBlockStartEvent {\n  /**\n   * Response model for a file uploaded to the container.\n   */\n  content_block:\n    | BetaTextBlock\n    | BetaToolUseBlock\n    | BetaServerToolUseBlock\n    | BetaWebSearchToolResultBlock\n    | BetaCodeExecutionToolResultBlock\n    | BetaMCPToolUseBlock\n    | BetaMCPToolResultBlock\n    | BetaContainerUploadBlock\n    | BetaThinkingBlock\n    | BetaRedactedThinkingBlock;\n\n  index: number;\n\n  type: 'content_block_start';\n}\n\nexport interface BetaRawContentBlockStopEvent {\n  index: number;\n\n  type: 'content_block_stop';\n}\n\nexport interface BetaRawMessageDeltaEvent {\n  delta: BetaRawMessageDeltaEvent.Delta;\n\n  type: 'message_delta';\n\n  /**\n   * Billing and rate-limit usage.\n   *\n   * Anthropic's API bills and rate-limits by token counts, as tokens represent the\n   * underlying cost to our systems.\n   *\n   * Under the hood, the API transforms requests into a format suitable for the\n   * model. The model's output then goes through a parsing stage before becoming an\n   * API response. As a result, the token counts in `usage` will not match one-to-one\n   * with the exact visible content of an API request or response.\n   *\n   * For example, `output_tokens` will be non-zero, even for an empty string response\n   * from Claude.\n   *\n   * Total input tokens in a request is the summation of `input_tokens`,\n   * `cache_creation_input_tokens`, and `cache_read_input_tokens`.\n   */\n  usage: BetaMessageDeltaUsage;\n}\n\nexport namespace BetaRawMessageDeltaEvent {\n  export interface Delta {\n    /**\n     * Information about the container used in the request (for the code execution\n     * tool)\n     */\n    container: MessagesMessagesAPI.BetaContainer | null;\n\n    stop_reason: MessagesMessagesAPI.BetaStopReason | null;\n\n    stop_sequence: string | null;\n  }\n}\n\nexport interface BetaRawMessageStartEvent {\n  message: BetaMessage;\n\n  type: 'message_start';\n}\n\nexport interface BetaRawMessageStopEvent {\n  type: 'message_stop';\n}\n\nexport type BetaRawMessageStreamEvent =\n  | BetaRawMessageStartEvent\n  | BetaRawMessageDeltaEvent\n  | BetaRawMessageStopEvent\n  | BetaRawContentBlockStartEvent\n  | BetaRawContentBlockDeltaEvent\n  | BetaRawContentBlockStopEvent;\n\nexport interface BetaRedactedThinkingBlock {\n  data: string;\n\n  type: 'redacted_thinking';\n}\n\nexport interface BetaRedactedThinkingBlockParam {\n  data: string;\n\n  type: 'redacted_thinking';\n}\n\nexport interface BetaRequestMCPServerToolConfiguration {\n  allowed_tools?: Array<string> | null;\n\n  enabled?: boolean | null;\n}\n\nexport interface BetaRequestMCPServerURLDefinition {\n  name: string;\n\n  type: 'url';\n\n  url: string;\n\n  authorization_token?: string | null;\n\n  tool_configuration?: BetaRequestMCPServerToolConfiguration | null;\n}\n\nexport interface BetaRequestMCPToolResultBlockParam {\n  tool_use_id: string;\n\n  type: 'mcp_tool_result';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  content?: string | Array<BetaTextBlockParam>;\n\n  is_error?: boolean;\n}\n\nexport interface BetaServerToolUsage {\n  /**\n   * The number of web search tool requests.\n   */\n  web_search_requests: number;\n}\n\nexport interface BetaServerToolUseBlock {\n  id: string;\n\n  input: unknown;\n\n  name: 'web_search' | 'code_execution';\n\n  type: 'server_tool_use';\n}\n\nexport interface BetaServerToolUseBlockParam {\n  id: string;\n\n  input: unknown;\n\n  name: 'web_search' | 'code_execution';\n\n  type: 'server_tool_use';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaSignatureDelta {\n  signature: string;\n\n  type: 'signature_delta';\n}\n\nexport type BetaStopReason =\n  | 'end_turn'\n  | 'max_tokens'\n  | 'stop_sequence'\n  | 'tool_use'\n  | 'pause_turn'\n  | 'refusal';\n\nexport interface BetaTextBlock {\n  /**\n   * Citations supporting the text block.\n   *\n   * The type of citation returned will depend on the type of document being cited.\n   * Citing a PDF results in `page_location`, plain text results in `char_location`,\n   * and content document results in `content_block_location`.\n   */\n  citations: Array<BetaTextCitation> | null;\n\n  text: string;\n\n  type: 'text';\n}\n\nexport interface BetaTextBlockParam {\n  text: string;\n\n  type: 'text';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  citations?: Array<BetaTextCitationParam> | null;\n}\n\nexport type BetaTextCitation =\n  | BetaCitationCharLocation\n  | BetaCitationPageLocation\n  | BetaCitationContentBlockLocation\n  | BetaCitationsWebSearchResultLocation;\n\nexport type BetaTextCitationParam =\n  | BetaCitationCharLocationParam\n  | BetaCitationPageLocationParam\n  | BetaCitationContentBlockLocationParam\n  | BetaCitationWebSearchResultLocationParam;\n\nexport interface BetaTextDelta {\n  text: string;\n\n  type: 'text_delta';\n}\n\nexport interface BetaThinkingBlock {\n  signature: string;\n\n  thinking: string;\n\n  type: 'thinking';\n}\n\nexport interface BetaThinkingBlockParam {\n  signature: string;\n\n  thinking: string;\n\n  type: 'thinking';\n}\n\nexport interface BetaThinkingConfigDisabled {\n  type: 'disabled';\n}\n\nexport interface BetaThinkingConfigEnabled {\n  /**\n   * Determines how many tokens Claude can use for its internal reasoning process.\n   * Larger budgets can enable more thorough analysis for complex problems, improving\n   * response quality.\n   *\n   * Must be ≥1024 and less than `max_tokens`.\n   *\n   * See\n   * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n   * for details.\n   */\n  budget_tokens: number;\n\n  type: 'enabled';\n}\n\n/**\n * Configuration for enabling Claude's extended thinking.\n *\n * When enabled, responses include `thinking` content blocks showing Claude's\n * thinking process before the final answer. Requires a minimum budget of 1,024\n * tokens and counts towards your `max_tokens` limit.\n *\n * See\n * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n * for details.\n */\nexport type BetaThinkingConfigParam = BetaThinkingConfigEnabled | BetaThinkingConfigDisabled;\n\nexport interface BetaThinkingDelta {\n  thinking: string;\n\n  type: 'thinking_delta';\n}\n\nexport interface BetaTool {\n  /**\n   * [JSON schema](https://json-schema.org/draft/2020-12) for this tool's input.\n   *\n   * This defines the shape of the `input` that your tool accepts and that the model\n   * will produce.\n   */\n  input_schema: BetaTool.InputSchema;\n\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: string;\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  /**\n   * Description of what this tool does.\n   *\n   * Tool descriptions should be as detailed as possible. The more information that\n   * the model has about what the tool is and how to use it, the better it will\n   * perform. You can use natural language descriptions to reinforce important\n   * aspects of the tool input JSON schema.\n   */\n  description?: string;\n\n  type?: 'custom' | null;\n}\n\nexport namespace BetaTool {\n  /**\n   * [JSON schema](https://json-schema.org/draft/2020-12) for this tool's input.\n   *\n   * This defines the shape of the `input` that your tool accepts and that the model\n   * will produce.\n   */\n  export interface InputSchema {\n    type: 'object';\n\n    properties?: unknown | null;\n\n    [k: string]: unknown;\n  }\n}\n\nexport interface BetaToolBash20241022 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'bash';\n\n  type: 'bash_20241022';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaToolBash20250124 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'bash';\n\n  type: 'bash_20250124';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\n/**\n * How the model should use the provided tools. The model can use a specific tool,\n * any available tool, decide by itself, or not use tools at all.\n */\nexport type BetaToolChoice = BetaToolChoiceAuto | BetaToolChoiceAny | BetaToolChoiceTool | BetaToolChoiceNone;\n\n/**\n * The model will use any available tools.\n */\nexport interface BetaToolChoiceAny {\n  type: 'any';\n\n  /**\n   * Whether to disable parallel tool use.\n   *\n   * Defaults to `false`. If set to `true`, the model will output exactly one tool\n   * use.\n   */\n  disable_parallel_tool_use?: boolean;\n}\n\n/**\n * The model will automatically decide whether to use tools.\n */\nexport interface BetaToolChoiceAuto {\n  type: 'auto';\n\n  /**\n   * Whether to disable parallel tool use.\n   *\n   * Defaults to `false`. If set to `true`, the model will output at most one tool\n   * use.\n   */\n  disable_parallel_tool_use?: boolean;\n}\n\n/**\n * The model will not be allowed to use tools.\n */\nexport interface BetaToolChoiceNone {\n  type: 'none';\n}\n\n/**\n * The model will use the specified tool with `tool_choice.name`.\n */\nexport interface BetaToolChoiceTool {\n  /**\n   * The name of the tool to use.\n   */\n  name: string;\n\n  type: 'tool';\n\n  /**\n   * Whether to disable parallel tool use.\n   *\n   * Defaults to `false`. If set to `true`, the model will output exactly one tool\n   * use.\n   */\n  disable_parallel_tool_use?: boolean;\n}\n\nexport interface BetaToolComputerUse20241022 {\n  /**\n   * The height of the display in pixels.\n   */\n  display_height_px: number;\n\n  /**\n   * The width of the display in pixels.\n   */\n  display_width_px: number;\n\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'computer';\n\n  type: 'computer_20241022';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  /**\n   * The X11 display number (e.g. 0, 1) for the display.\n   */\n  display_number?: number | null;\n}\n\nexport interface BetaToolComputerUse20250124 {\n  /**\n   * The height of the display in pixels.\n   */\n  display_height_px: number;\n\n  /**\n   * The width of the display in pixels.\n   */\n  display_width_px: number;\n\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'computer';\n\n  type: 'computer_20250124';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  /**\n   * The X11 display number (e.g. 0, 1) for the display.\n   */\n  display_number?: number | null;\n}\n\nexport interface BetaToolResultBlockParam {\n  tool_use_id: string;\n\n  type: 'tool_result';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  content?: string | Array<BetaTextBlockParam | BetaImageBlockParam>;\n\n  is_error?: boolean;\n}\n\nexport interface BetaToolTextEditor20241022 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'str_replace_editor';\n\n  type: 'text_editor_20241022';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaToolTextEditor20250124 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'str_replace_editor';\n\n  type: 'text_editor_20250124';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaToolTextEditor20250429 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'str_replace_based_edit_tool';\n\n  type: 'text_editor_20250429';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport type BetaToolUnion =\n  | BetaTool\n  | BetaToolComputerUse20241022\n  | BetaToolBash20241022\n  | BetaToolTextEditor20241022\n  | BetaToolComputerUse20250124\n  | BetaToolBash20250124\n  | BetaToolTextEditor20250124\n  | BetaToolTextEditor20250429\n  | BetaWebSearchTool20250305\n  | BetaCodeExecutionTool20250522;\n\nexport interface BetaToolUseBlock {\n  id: string;\n\n  input: unknown;\n\n  name: string;\n\n  type: 'tool_use';\n}\n\nexport interface BetaToolUseBlockParam {\n  id: string;\n\n  input: unknown;\n\n  name: string;\n\n  type: 'tool_use';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport interface BetaURLImageSource {\n  type: 'url';\n\n  url: string;\n}\n\nexport interface BetaURLPDFSource {\n  type: 'url';\n\n  url: string;\n}\n\nexport interface BetaUsage {\n  /**\n   * Breakdown of cached tokens by TTL\n   */\n  cache_creation: BetaCacheCreation | null;\n\n  /**\n   * The number of input tokens used to create the cache entry.\n   */\n  cache_creation_input_tokens: number | null;\n\n  /**\n   * The number of input tokens read from the cache.\n   */\n  cache_read_input_tokens: number | null;\n\n  /**\n   * The number of input tokens which were used.\n   */\n  input_tokens: number;\n\n  /**\n   * The number of output tokens which were used.\n   */\n  output_tokens: number;\n\n  /**\n   * The number of server tool requests.\n   */\n  server_tool_use: BetaServerToolUsage | null;\n\n  /**\n   * If the request used the priority, standard, or batch tier.\n   */\n  service_tier: 'standard' | 'priority' | 'batch' | null;\n}\n\nexport interface BetaWebSearchResultBlock {\n  encrypted_content: string;\n\n  page_age: string | null;\n\n  title: string;\n\n  type: 'web_search_result';\n\n  url: string;\n}\n\nexport interface BetaWebSearchResultBlockParam {\n  encrypted_content: string;\n\n  title: string;\n\n  type: 'web_search_result';\n\n  url: string;\n\n  page_age?: string | null;\n}\n\nexport interface BetaWebSearchTool20250305 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'web_search';\n\n  type: 'web_search_20250305';\n\n  /**\n   * If provided, only these domains will be included in results. Cannot be used\n   * alongside `blocked_domains`.\n   */\n  allowed_domains?: Array<string> | null;\n\n  /**\n   * If provided, these domains will never appear in results. Cannot be used\n   * alongside `allowed_domains`.\n   */\n  blocked_domains?: Array<string> | null;\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n\n  /**\n   * Maximum number of times the tool can be used in the API request.\n   */\n  max_uses?: number | null;\n\n  /**\n   * Parameters for the user's location. Used to provide more relevant search\n   * results.\n   */\n  user_location?: BetaWebSearchTool20250305.UserLocation | null;\n}\n\nexport namespace BetaWebSearchTool20250305 {\n  /**\n   * Parameters for the user's location. Used to provide more relevant search\n   * results.\n   */\n  export interface UserLocation {\n    type: 'approximate';\n\n    /**\n     * The city of the user.\n     */\n    city?: string | null;\n\n    /**\n     * The two letter\n     * [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the\n     * user.\n     */\n    country?: string | null;\n\n    /**\n     * The region of the user.\n     */\n    region?: string | null;\n\n    /**\n     * The [IANA timezone](https://nodatime.org/TimeZones) of the user.\n     */\n    timezone?: string | null;\n  }\n}\n\nexport interface BetaWebSearchToolRequestError {\n  error_code: BetaWebSearchToolResultErrorCode;\n\n  type: 'web_search_tool_result_error';\n}\n\nexport interface BetaWebSearchToolResultBlock {\n  content: BetaWebSearchToolResultBlockContent;\n\n  tool_use_id: string;\n\n  type: 'web_search_tool_result';\n}\n\nexport type BetaWebSearchToolResultBlockContent =\n  | BetaWebSearchToolResultError\n  | Array<BetaWebSearchResultBlock>;\n\nexport interface BetaWebSearchToolResultBlockParam {\n  content: BetaWebSearchToolResultBlockParamContent;\n\n  tool_use_id: string;\n\n  type: 'web_search_tool_result';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: BetaCacheControlEphemeral | null;\n}\n\nexport type BetaWebSearchToolResultBlockParamContent =\n  | Array<BetaWebSearchResultBlockParam>\n  | BetaWebSearchToolRequestError;\n\nexport interface BetaWebSearchToolResultError {\n  error_code: BetaWebSearchToolResultErrorCode;\n\n  type: 'web_search_tool_result_error';\n}\n\nexport type BetaWebSearchToolResultErrorCode =\n  | 'invalid_tool_input'\n  | 'unavailable'\n  | 'max_uses_exceeded'\n  | 'too_many_requests'\n  | 'query_too_long';\n\nexport type MessageCreateParams = MessageCreateParamsNonStreaming | MessageCreateParamsStreaming;\n\nexport interface MessageCreateParamsBase {\n  /**\n   * Body param: The maximum number of tokens to generate before stopping.\n   *\n   * Note that our models may stop _before_ reaching this maximum. This parameter\n   * only specifies the absolute maximum number of tokens to generate.\n   *\n   * Different models have different maximum values for this parameter. See\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for details.\n   */\n  max_tokens: number;\n\n  /**\n   * Body param: Input messages.\n   *\n   * Our models are trained to operate on alternating `user` and `assistant`\n   * conversational turns. When creating a new `Message`, you specify the prior\n   * conversational turns with the `messages` parameter, and the model then generates\n   * the next `Message` in the conversation. Consecutive `user` or `assistant` turns\n   * in your request will be combined into a single turn.\n   *\n   * Each input message must be an object with a `role` and `content`. You can\n   * specify a single `user`-role message, or you can include multiple `user` and\n   * `assistant` messages.\n   *\n   * If the final message uses the `assistant` role, the response content will\n   * continue immediately from the content in that message. This can be used to\n   * constrain part of the model's response.\n   *\n   * Example with a single `user` message:\n   *\n   * ```json\n   * [{ \"role\": \"user\", \"content\": \"Hello, Claude\" }]\n   * ```\n   *\n   * Example with multiple conversational turns:\n   *\n   * ```json\n   * [\n   *   { \"role\": \"user\", \"content\": \"Hello there.\" },\n   *   { \"role\": \"assistant\", \"content\": \"Hi, I'm Claude. How can I help you?\" },\n   *   { \"role\": \"user\", \"content\": \"Can you explain LLMs in plain English?\" }\n   * ]\n   * ```\n   *\n   * Example with a partially-filled response from Claude:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Each input message `content` may be either a single `string` or an array of\n   * content blocks, where each block has a specific `type`. Using a `string` for\n   * `content` is shorthand for an array of one content block of type `\"text\"`. The\n   * following input messages are equivalent:\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": \"Hello, Claude\" }\n   * ```\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": [{ \"type\": \"text\", \"text\": \"Hello, Claude\" }] }\n   * ```\n   *\n   * Starting with Claude 3 models, you can also send image content blocks:\n   *\n   * ```json\n   * {\n   *   \"role\": \"user\",\n   *   \"content\": [\n   *     {\n   *       \"type\": \"image\",\n   *       \"source\": {\n   *         \"type\": \"base64\",\n   *         \"media_type\": \"image/jpeg\",\n   *         \"data\": \"/9j/4AAQSkZJRg...\"\n   *       }\n   *     },\n   *     { \"type\": \"text\", \"text\": \"What is in this image?\" }\n   *   ]\n   * }\n   * ```\n   *\n   * We currently support the `base64` source type for images, and the `image/jpeg`,\n   * `image/png`, `image/gif`, and `image/webp` media types.\n   *\n   * See [examples](https://docs.anthropic.com/en/api/messages-examples#vision) for\n   * more input examples.\n   *\n   * Note that if you want to include a\n   * [system prompt](https://docs.anthropic.com/en/docs/system-prompts), you can use\n   * the top-level `system` parameter — there is no `\"system\"` role for input\n   * messages in the Messages API.\n   *\n   * There is a limit of 100000 messages in a single request.\n   */\n  messages: Array<BetaMessageParam>;\n\n  /**\n   * Body param: The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: MessagesAPI.Model;\n\n  /**\n   * Body param: Container identifier for reuse across requests.\n   */\n  container?: string | null;\n\n  /**\n   * Body param: MCP servers to be utilized in this request\n   */\n  mcp_servers?: Array<BetaRequestMCPServerURLDefinition>;\n\n  /**\n   * Body param: An object describing metadata about the request.\n   */\n  metadata?: BetaMetadata;\n\n  /**\n   * Body param: Determines whether to use priority capacity (if available) or\n   * standard capacity for this request.\n   *\n   * Anthropic offers different levels of service for your API requests. See\n   * [service-tiers](https://docs.anthropic.com/en/api/service-tiers) for details.\n   */\n  service_tier?: 'auto' | 'standard_only';\n\n  /**\n   * Body param: Custom text sequences that will cause the model to stop generating.\n   *\n   * Our models will normally stop when they have naturally completed their turn,\n   * which will result in a response `stop_reason` of `\"end_turn\"`.\n   *\n   * If you want the model to stop generating when it encounters custom strings of\n   * text, you can use the `stop_sequences` parameter. If the model encounters one of\n   * the custom sequences, the response `stop_reason` value will be `\"stop_sequence\"`\n   * and the response `stop_sequence` value will contain the matched stop sequence.\n   */\n  stop_sequences?: Array<string>;\n\n  /**\n   * Body param: Whether to incrementally stream the response using server-sent\n   * events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/messages-streaming) for\n   * details.\n   */\n  stream?: boolean;\n\n  /**\n   * Body param: System prompt.\n   *\n   * A system prompt is a way of providing context and instructions to Claude, such\n   * as specifying a particular goal or role. See our\n   * [guide to system prompts](https://docs.anthropic.com/en/docs/system-prompts).\n   */\n  system?: string | Array<BetaTextBlockParam>;\n\n  /**\n   * Body param: Amount of randomness injected into the response.\n   *\n   * Defaults to `1.0`. Ranges from `0.0` to `1.0`. Use `temperature` closer to `0.0`\n   * for analytical / multiple choice, and closer to `1.0` for creative and\n   * generative tasks.\n   *\n   * Note that even with `temperature` of `0.0`, the results will not be fully\n   * deterministic.\n   */\n  temperature?: number;\n\n  /**\n   * Body param: Configuration for enabling Claude's extended thinking.\n   *\n   * When enabled, responses include `thinking` content blocks showing Claude's\n   * thinking process before the final answer. Requires a minimum budget of 1,024\n   * tokens and counts towards your `max_tokens` limit.\n   *\n   * See\n   * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n   * for details.\n   */\n  thinking?: BetaThinkingConfigParam;\n\n  /**\n   * Body param: How the model should use the provided tools. The model can use a\n   * specific tool, any available tool, decide by itself, or not use tools at all.\n   */\n  tool_choice?: BetaToolChoice;\n\n  /**\n   * Body param: Definitions of tools that the model may use.\n   *\n   * If you include `tools` in your API request, the model may return `tool_use`\n   * content blocks that represent the model's use of those tools. You can then run\n   * those tools using the tool input generated by the model and then optionally\n   * return results back to the model using `tool_result` content blocks.\n   *\n   * Each tool definition includes:\n   *\n   * - `name`: Name of the tool.\n   * - `description`: Optional, but strongly-recommended description of the tool.\n   * - `input_schema`: [JSON schema](https://json-schema.org/draft/2020-12) for the\n   *   tool `input` shape that the model will produce in `tool_use` output content\n   *   blocks.\n   *\n   * For example, if you defined `tools` as:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"name\": \"get_stock_price\",\n   *     \"description\": \"Get the current stock price for a given ticker symbol.\",\n   *     \"input_schema\": {\n   *       \"type\": \"object\",\n   *       \"properties\": {\n   *         \"ticker\": {\n   *           \"type\": \"string\",\n   *           \"description\": \"The stock ticker symbol, e.g. AAPL for Apple Inc.\"\n   *         }\n   *       },\n   *       \"required\": [\"ticker\"]\n   *     }\n   *   }\n   * ]\n   * ```\n   *\n   * And then asked the model \"What's the S&P 500 at today?\", the model might produce\n   * `tool_use` content blocks in the response like this:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_use\",\n   *     \"id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"name\": \"get_stock_price\",\n   *     \"input\": { \"ticker\": \"^GSPC\" }\n   *   }\n   * ]\n   * ```\n   *\n   * You might then run your `get_stock_price` tool with `{\"ticker\": \"^GSPC\"}` as an\n   * input, and return the following back to the model in a subsequent `user`\n   * message:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_result\",\n   *     \"tool_use_id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"content\": \"259.75 USD\"\n   *   }\n   * ]\n   * ```\n   *\n   * Tools can be used for workflows that include running client-side tools and\n   * functions, or more generally whenever you want the model to produce a particular\n   * JSON structure of output.\n   *\n   * See our [guide](https://docs.anthropic.com/en/docs/tool-use) for more details.\n   */\n  tools?: Array<BetaToolUnion>;\n\n  /**\n   * Body param: Only sample from the top K options for each subsequent token.\n   *\n   * Used to remove \"long tail\" low probability responses.\n   * [Learn more technical details here](https://towardsdatascience.com/how-to-sample-from-language-models-682bceb97277).\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_k?: number;\n\n  /**\n   * Body param: Use nucleus sampling.\n   *\n   * In nucleus sampling, we compute the cumulative distribution over all the options\n   * for each subsequent token in decreasing probability order and cut it off once it\n   * reaches a particular probability specified by `top_p`. You should either alter\n   * `temperature` or `top_p`, but not both.\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_p?: number;\n\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport namespace MessageCreateParams {\n  export type MessageCreateParamsNonStreaming = MessagesMessagesAPI.MessageCreateParamsNonStreaming;\n  export type MessageCreateParamsStreaming = MessagesMessagesAPI.MessageCreateParamsStreaming;\n}\n\nexport interface MessageCreateParamsNonStreaming extends MessageCreateParamsBase {\n  /**\n   * Body param: Whether to incrementally stream the response using server-sent\n   * events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/messages-streaming) for\n   * details.\n   */\n  stream?: false;\n}\n\nexport interface MessageCreateParamsStreaming extends MessageCreateParamsBase {\n  /**\n   * Body param: Whether to incrementally stream the response using server-sent\n   * events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/messages-streaming) for\n   * details.\n   */\n  stream: true;\n}\n\nexport interface MessageCountTokensParams {\n  /**\n   * Body param: Input messages.\n   *\n   * Our models are trained to operate on alternating `user` and `assistant`\n   * conversational turns. When creating a new `Message`, you specify the prior\n   * conversational turns with the `messages` parameter, and the model then generates\n   * the next `Message` in the conversation. Consecutive `user` or `assistant` turns\n   * in your request will be combined into a single turn.\n   *\n   * Each input message must be an object with a `role` and `content`. You can\n   * specify a single `user`-role message, or you can include multiple `user` and\n   * `assistant` messages.\n   *\n   * If the final message uses the `assistant` role, the response content will\n   * continue immediately from the content in that message. This can be used to\n   * constrain part of the model's response.\n   *\n   * Example with a single `user` message:\n   *\n   * ```json\n   * [{ \"role\": \"user\", \"content\": \"Hello, Claude\" }]\n   * ```\n   *\n   * Example with multiple conversational turns:\n   *\n   * ```json\n   * [\n   *   { \"role\": \"user\", \"content\": \"Hello there.\" },\n   *   { \"role\": \"assistant\", \"content\": \"Hi, I'm Claude. How can I help you?\" },\n   *   { \"role\": \"user\", \"content\": \"Can you explain LLMs in plain English?\" }\n   * ]\n   * ```\n   *\n   * Example with a partially-filled response from Claude:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Each input message `content` may be either a single `string` or an array of\n   * content blocks, where each block has a specific `type`. Using a `string` for\n   * `content` is shorthand for an array of one content block of type `\"text\"`. The\n   * following input messages are equivalent:\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": \"Hello, Claude\" }\n   * ```\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": [{ \"type\": \"text\", \"text\": \"Hello, Claude\" }] }\n   * ```\n   *\n   * Starting with Claude 3 models, you can also send image content blocks:\n   *\n   * ```json\n   * {\n   *   \"role\": \"user\",\n   *   \"content\": [\n   *     {\n   *       \"type\": \"image\",\n   *       \"source\": {\n   *         \"type\": \"base64\",\n   *         \"media_type\": \"image/jpeg\",\n   *         \"data\": \"/9j/4AAQSkZJRg...\"\n   *       }\n   *     },\n   *     { \"type\": \"text\", \"text\": \"What is in this image?\" }\n   *   ]\n   * }\n   * ```\n   *\n   * We currently support the `base64` source type for images, and the `image/jpeg`,\n   * `image/png`, `image/gif`, and `image/webp` media types.\n   *\n   * See [examples](https://docs.anthropic.com/en/api/messages-examples#vision) for\n   * more input examples.\n   *\n   * Note that if you want to include a\n   * [system prompt](https://docs.anthropic.com/en/docs/system-prompts), you can use\n   * the top-level `system` parameter — there is no `\"system\"` role for input\n   * messages in the Messages API.\n   *\n   * There is a limit of 100000 messages in a single request.\n   */\n  messages: Array<BetaMessageParam>;\n\n  /**\n   * Body param: The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: MessagesAPI.Model;\n\n  /**\n   * Body param: MCP servers to be utilized in this request\n   */\n  mcp_servers?: Array<BetaRequestMCPServerURLDefinition>;\n\n  /**\n   * Body param: System prompt.\n   *\n   * A system prompt is a way of providing context and instructions to Claude, such\n   * as specifying a particular goal or role. See our\n   * [guide to system prompts](https://docs.anthropic.com/en/docs/system-prompts).\n   */\n  system?: string | Array<BetaTextBlockParam>;\n\n  /**\n   * Body param: Configuration for enabling Claude's extended thinking.\n   *\n   * When enabled, responses include `thinking` content blocks showing Claude's\n   * thinking process before the final answer. Requires a minimum budget of 1,024\n   * tokens and counts towards your `max_tokens` limit.\n   *\n   * See\n   * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n   * for details.\n   */\n  thinking?: BetaThinkingConfigParam;\n\n  /**\n   * Body param: How the model should use the provided tools. The model can use a\n   * specific tool, any available tool, decide by itself, or not use tools at all.\n   */\n  tool_choice?: BetaToolChoice;\n\n  /**\n   * Body param: Definitions of tools that the model may use.\n   *\n   * If you include `tools` in your API request, the model may return `tool_use`\n   * content blocks that represent the model's use of those tools. You can then run\n   * those tools using the tool input generated by the model and then optionally\n   * return results back to the model using `tool_result` content blocks.\n   *\n   * Each tool definition includes:\n   *\n   * - `name`: Name of the tool.\n   * - `description`: Optional, but strongly-recommended description of the tool.\n   * - `input_schema`: [JSON schema](https://json-schema.org/draft/2020-12) for the\n   *   tool `input` shape that the model will produce in `tool_use` output content\n   *   blocks.\n   *\n   * For example, if you defined `tools` as:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"name\": \"get_stock_price\",\n   *     \"description\": \"Get the current stock price for a given ticker symbol.\",\n   *     \"input_schema\": {\n   *       \"type\": \"object\",\n   *       \"properties\": {\n   *         \"ticker\": {\n   *           \"type\": \"string\",\n   *           \"description\": \"The stock ticker symbol, e.g. AAPL for Apple Inc.\"\n   *         }\n   *       },\n   *       \"required\": [\"ticker\"]\n   *     }\n   *   }\n   * ]\n   * ```\n   *\n   * And then asked the model \"What's the S&P 500 at today?\", the model might produce\n   * `tool_use` content blocks in the response like this:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_use\",\n   *     \"id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"name\": \"get_stock_price\",\n   *     \"input\": { \"ticker\": \"^GSPC\" }\n   *   }\n   * ]\n   * ```\n   *\n   * You might then run your `get_stock_price` tool with `{\"ticker\": \"^GSPC\"}` as an\n   * input, and return the following back to the model in a subsequent `user`\n   * message:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_result\",\n   *     \"tool_use_id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"content\": \"259.75 USD\"\n   *   }\n   * ]\n   * ```\n   *\n   * Tools can be used for workflows that include running client-side tools and\n   * functions, or more generally whenever you want the model to produce a particular\n   * JSON structure of output.\n   *\n   * See our [guide](https://docs.anthropic.com/en/docs/tool-use) for more details.\n   */\n  tools?: Array<\n    | BetaTool\n    | BetaToolComputerUse20241022\n    | BetaToolBash20241022\n    | BetaToolTextEditor20241022\n    | BetaToolComputerUse20250124\n    | BetaToolBash20250124\n    | BetaToolTextEditor20250124\n    | BetaToolTextEditor20250429\n    | BetaWebSearchTool20250305\n    | BetaCodeExecutionTool20250522\n  >;\n\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nMessages.Batches = Batches;\n\nexport declare namespace Messages {\n  export {\n    type BetaBase64ImageSource as BetaBase64ImageSource,\n    type BetaBase64PDFBlock as BetaBase64PDFBlock,\n    type BetaBase64PDFSource as BetaBase64PDFSource,\n    type BetaCacheControlEphemeral as BetaCacheControlEphemeral,\n    type BetaCacheCreation as BetaCacheCreation,\n    type BetaCitationCharLocation as BetaCitationCharLocation,\n    type BetaCitationCharLocationParam as BetaCitationCharLocationParam,\n    type BetaCitationContentBlockLocation as BetaCitationContentBlockLocation,\n    type BetaCitationContentBlockLocationParam as BetaCitationContentBlockLocationParam,\n    type BetaCitationPageLocation as BetaCitationPageLocation,\n    type BetaCitationPageLocationParam as BetaCitationPageLocationParam,\n    type BetaCitationWebSearchResultLocationParam as BetaCitationWebSearchResultLocationParam,\n    type BetaCitationsConfigParam as BetaCitationsConfigParam,\n    type BetaCitationsDelta as BetaCitationsDelta,\n    type BetaCitationsWebSearchResultLocation as BetaCitationsWebSearchResultLocation,\n    type BetaCodeExecutionOutputBlock as BetaCodeExecutionOutputBlock,\n    type BetaCodeExecutionOutputBlockParam as BetaCodeExecutionOutputBlockParam,\n    type BetaCodeExecutionResultBlock as BetaCodeExecutionResultBlock,\n    type BetaCodeExecutionResultBlockParam as BetaCodeExecutionResultBlockParam,\n    type BetaCodeExecutionTool20250522 as BetaCodeExecutionTool20250522,\n    type BetaCodeExecutionToolResultBlock as BetaCodeExecutionToolResultBlock,\n    type BetaCodeExecutionToolResultBlockContent as BetaCodeExecutionToolResultBlockContent,\n    type BetaCodeExecutionToolResultBlockParam as BetaCodeExecutionToolResultBlockParam,\n    type BetaCodeExecutionToolResultBlockParamContent as BetaCodeExecutionToolResultBlockParamContent,\n    type BetaCodeExecutionToolResultError as BetaCodeExecutionToolResultError,\n    type BetaCodeExecutionToolResultErrorCode as BetaCodeExecutionToolResultErrorCode,\n    type BetaCodeExecutionToolResultErrorParam as BetaCodeExecutionToolResultErrorParam,\n    type BetaContainer as BetaContainer,\n    type BetaContainerUploadBlock as BetaContainerUploadBlock,\n    type BetaContainerUploadBlockParam as BetaContainerUploadBlockParam,\n    type BetaContentBlock as BetaContentBlock,\n    type BetaContentBlockParam as BetaContentBlockParam,\n    type BetaContentBlockSource as BetaContentBlockSource,\n    type BetaContentBlockSourceContent as BetaContentBlockSourceContent,\n    type BetaFileDocumentSource as BetaFileDocumentSource,\n    type BetaFileImageSource as BetaFileImageSource,\n    type BetaImageBlockParam as BetaImageBlockParam,\n    type BetaInputJSONDelta as BetaInputJSONDelta,\n    type BetaMCPToolResultBlock as BetaMCPToolResultBlock,\n    type BetaMCPToolUseBlock as BetaMCPToolUseBlock,\n    type BetaMCPToolUseBlockParam as BetaMCPToolUseBlockParam,\n    type BetaMessage as BetaMessage,\n    type BetaMessageDeltaUsage as BetaMessageDeltaUsage,\n    type BetaMessageParam as BetaMessageParam,\n    type BetaMessageTokensCount as BetaMessageTokensCount,\n    type BetaMetadata as BetaMetadata,\n    type BetaPlainTextSource as BetaPlainTextSource,\n    type BetaRawContentBlockDelta as BetaRawContentBlockDelta,\n    type BetaRawContentBlockDeltaEvent as BetaRawContentBlockDeltaEvent,\n    type BetaRawContentBlockStartEvent as BetaRawContentBlockStartEvent,\n    type BetaRawContentBlockStopEvent as BetaRawContentBlockStopEvent,\n    type BetaRawMessageDeltaEvent as BetaRawMessageDeltaEvent,\n    type BetaRawMessageStartEvent as BetaRawMessageStartEvent,\n    type BetaRawMessageStopEvent as BetaRawMessageStopEvent,\n    type BetaRawMessageStreamEvent as BetaRawMessageStreamEvent,\n    type BetaRedactedThinkingBlock as BetaRedactedThinkingBlock,\n    type BetaRedactedThinkingBlockParam as BetaRedactedThinkingBlockParam,\n    type BetaRequestMCPServerToolConfiguration as BetaRequestMCPServerToolConfiguration,\n    type BetaRequestMCPServerURLDefinition as BetaRequestMCPServerURLDefinition,\n    type BetaRequestMCPToolResultBlockParam as BetaRequestMCPToolResultBlockParam,\n    type BetaServerToolUsage as BetaServerToolUsage,\n    type BetaServerToolUseBlock as BetaServerToolUseBlock,\n    type BetaServerToolUseBlockParam as BetaServerToolUseBlockParam,\n    type BetaSignatureDelta as BetaSignatureDelta,\n    type BetaStopReason as BetaStopReason,\n    type BetaTextBlock as BetaTextBlock,\n    type BetaTextBlockParam as BetaTextBlockParam,\n    type BetaTextCitation as BetaTextCitation,\n    type BetaTextCitationParam as BetaTextCitationParam,\n    type BetaTextDelta as BetaTextDelta,\n    type BetaThinkingBlock as BetaThinkingBlock,\n    type BetaThinkingBlockParam as BetaThinkingBlockParam,\n    type BetaThinkingConfigDisabled as BetaThinkingConfigDisabled,\n    type BetaThinkingConfigEnabled as BetaThinkingConfigEnabled,\n    type BetaThinkingConfigParam as BetaThinkingConfigParam,\n    type BetaThinkingDelta as BetaThinkingDelta,\n    type BetaTool as BetaTool,\n    type BetaToolBash20241022 as BetaToolBash20241022,\n    type BetaToolBash20250124 as BetaToolBash20250124,\n    type BetaToolChoice as BetaToolChoice,\n    type BetaToolChoiceAny as BetaToolChoiceAny,\n    type BetaToolChoiceAuto as BetaToolChoiceAuto,\n    type BetaToolChoiceNone as BetaToolChoiceNone,\n    type BetaToolChoiceTool as BetaToolChoiceTool,\n    type BetaToolComputerUse20241022 as BetaToolComputerUse20241022,\n    type BetaToolComputerUse20250124 as BetaToolComputerUse20250124,\n    type BetaToolResultBlockParam as BetaToolResultBlockParam,\n    type BetaToolTextEditor20241022 as BetaToolTextEditor20241022,\n    type BetaToolTextEditor20250124 as BetaToolTextEditor20250124,\n    type BetaToolTextEditor20250429 as BetaToolTextEditor20250429,\n    type BetaToolUnion as BetaToolUnion,\n    type BetaToolUseBlock as BetaToolUseBlock,\n    type BetaToolUseBlockParam as BetaToolUseBlockParam,\n    type BetaURLImageSource as BetaURLImageSource,\n    type BetaURLPDFSource as BetaURLPDFSource,\n    type BetaUsage as BetaUsage,\n    type BetaWebSearchResultBlock as BetaWebSearchResultBlock,\n    type BetaWebSearchResultBlockParam as BetaWebSearchResultBlockParam,\n    type BetaWebSearchTool20250305 as BetaWebSearchTool20250305,\n    type BetaWebSearchToolRequestError as BetaWebSearchToolRequestError,\n    type BetaWebSearchToolResultBlock as BetaWebSearchToolResultBlock,\n    type BetaWebSearchToolResultBlockContent as BetaWebSearchToolResultBlockContent,\n    type BetaWebSearchToolResultBlockParam as BetaWebSearchToolResultBlockParam,\n    type BetaWebSearchToolResultBlockParamContent as BetaWebSearchToolResultBlockParamContent,\n    type BetaWebSearchToolResultError as BetaWebSearchToolResultError,\n    type BetaWebSearchToolResultErrorCode as BetaWebSearchToolResultErrorCode,\n    type MessageCreateParams as MessageCreateParams,\n    type MessageCreateParamsNonStreaming as MessageCreateParamsNonStreaming,\n    type MessageCreateParamsStreaming as MessageCreateParamsStreaming,\n    type MessageCountTokensParams as MessageCountTokensParams,\n  };\n\n  export {\n    Batches as Batches,\n    type BetaDeletedMessageBatch as BetaDeletedMessageBatch,\n    type BetaMessageBatch as BetaMessageBatch,\n    type BetaMessageBatchCanceledResult as BetaMessageBatchCanceledResult,\n    type BetaMessageBatchErroredResult as BetaMessageBatchErroredResult,\n    type BetaMessageBatchExpiredResult as BetaMessageBatchExpiredResult,\n    type BetaMessageBatchIndividualResponse as BetaMessageBatchIndividualResponse,\n    type BetaMessageBatchRequestCounts as BetaMessageBatchRequestCounts,\n    type BetaMessageBatchResult as BetaMessageBatchResult,\n    type BetaMessageBatchSucceededResult as BetaMessageBatchSucceededResult,\n    type BetaMessageBatchesPage as BetaMessageBatchesPage,\n    type BatchCreateParams as BatchCreateParams,\n    type BatchRetrieveParams as BatchRetrieveParams,\n    type BatchListParams as BatchListParams,\n    type BatchDeleteParams as BatchDeleteParams,\n    type BatchCancelParams as BatchCancelParams,\n    type BatchResultsParams as BatchResultsParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../../core/resource';\nimport * as FilesAPI from './files';\nimport {\n  DeletedFile,\n  FileDeleteParams,\n  FileDownloadParams,\n  FileListParams,\n  FileMetadata,\n  FileMetadataPage,\n  FileRetrieveMetadataParams,\n  FileUploadParams,\n  Files,\n} from './files';\nimport * as ModelsAPI from './models';\nimport { BetaModelInfo, BetaModelInfosPage, ModelListParams, ModelRetrieveParams, Models } from './models';\nimport * as MessagesAPI from './messages/messages';\nimport {\n  BetaBase64ImageSource,\n  BetaBase64PDFBlock,\n  BetaBase64PDFSource,\n  BetaCacheControlEphemeral,\n  BetaCacheCreation,\n  BetaCitationCharLocation,\n  BetaCitationCharLocationParam,\n  BetaCitationContentBlockLocation,\n  BetaCitationContentBlockLocationParam,\n  BetaCitationPageLocation,\n  BetaCitationPageLocationParam,\n  BetaCitationWebSearchResultLocationParam,\n  BetaCitationsConfigParam,\n  BetaCitationsDelta,\n  BetaCitationsWebSearchResultLocation,\n  BetaCodeExecutionOutputBlock,\n  BetaCodeExecutionOutputBlockParam,\n  BetaCodeExecutionResultBlock,\n  BetaCodeExecutionResultBlockParam,\n  BetaCodeExecutionTool20250522,\n  BetaCodeExecutionToolResultBlock,\n  BetaCodeExecutionToolResultBlockContent,\n  BetaCodeExecutionToolResultBlockParam,\n  BetaCodeExecutionToolResultBlockParamContent,\n  BetaCodeExecutionToolResultError,\n  BetaCodeExecutionToolResultErrorCode,\n  BetaCodeExecutionToolResultErrorParam,\n  BetaContainer,\n  BetaContainerUploadBlock,\n  BetaContainerUploadBlockParam,\n  BetaContentBlock,\n  BetaContentBlockParam,\n  BetaContentBlockSource,\n  BetaContentBlockSourceContent,\n  BetaFileDocumentSource,\n  BetaFileImageSource,\n  BetaImageBlockParam,\n  BetaInputJSONDelta,\n  BetaMCPToolResultBlock,\n  BetaMCPToolUseBlock,\n  BetaMCPToolUseBlockParam,\n  BetaMessage,\n  BetaMessageDeltaUsage,\n  BetaMessageParam,\n  BetaMessageTokensCount,\n  BetaMetadata,\n  BetaPlainTextSource,\n  BetaRawContentBlockDelta,\n  BetaRawContentBlockDeltaEvent,\n  BetaRawContentBlockStartEvent,\n  BetaRawContentBlockStopEvent,\n  BetaRawMessageDeltaEvent,\n  BetaRawMessageStartEvent,\n  BetaRawMessageStopEvent,\n  BetaRawMessageStreamEvent,\n  BetaRedactedThinkingBlock,\n  BetaRedactedThinkingBlockParam,\n  BetaRequestMCPServerToolConfiguration,\n  BetaRequestMCPServerURLDefinition,\n  BetaRequestMCPToolResultBlockParam,\n  BetaServerToolUsage,\n  BetaServerToolUseBlock,\n  BetaServerToolUseBlockParam,\n  BetaSignatureDelta,\n  BetaStopReason,\n  BetaTextBlock,\n  BetaTextBlockParam,\n  BetaTextCitation,\n  BetaTextCitationParam,\n  BetaTextDelta,\n  BetaThinkingBlock,\n  BetaThinkingBlockParam,\n  BetaThinkingConfigDisabled,\n  BetaThinkingConfigEnabled,\n  BetaThinkingConfigParam,\n  BetaThinkingDelta,\n  BetaTool,\n  BetaToolBash20241022,\n  BetaToolBash20250124,\n  BetaToolChoice,\n  BetaToolChoiceAny,\n  BetaToolChoiceAuto,\n  BetaToolChoiceNone,\n  BetaToolChoiceTool,\n  BetaToolComputerUse20241022,\n  BetaToolComputerUse20250124,\n  BetaToolResultBlockParam,\n  BetaToolTextEditor20241022,\n  BetaToolTextEditor20250124,\n  BetaToolTextEditor20250429,\n  BetaToolUnion,\n  BetaToolUseBlock,\n  BetaToolUseBlockParam,\n  BetaURLImageSource,\n  BetaURLPDFSource,\n  BetaUsage,\n  BetaWebSearchResultBlock,\n  BetaWebSearchResultBlockParam,\n  BetaWebSearchTool20250305,\n  BetaWebSearchToolRequestError,\n  BetaWebSearchToolResultBlock,\n  BetaWebSearchToolResultBlockContent,\n  BetaWebSearchToolResultBlockParam,\n  BetaWebSearchToolResultBlockParamContent,\n  BetaWebSearchToolResultError,\n  BetaWebSearchToolResultErrorCode,\n  MessageCountTokensParams,\n  MessageCreateParams,\n  MessageCreateParamsNonStreaming,\n  MessageCreateParamsStreaming,\n  Messages,\n} from './messages/messages';\n\nexport class Beta extends APIResource {\n  models: ModelsAPI.Models = new ModelsAPI.Models(this._client);\n  messages: MessagesAPI.Messages = new MessagesAPI.Messages(this._client);\n  files: FilesAPI.Files = new FilesAPI.Files(this._client);\n}\n\nexport type AnthropicBeta =\n  | (string & {})\n  | 'message-batches-2024-09-24'\n  | 'prompt-caching-2024-07-31'\n  | 'computer-use-2024-10-22'\n  | 'computer-use-2025-01-24'\n  | 'pdfs-2024-09-25'\n  | 'token-counting-2024-11-01'\n  | 'token-efficient-tools-2025-02-19'\n  | 'output-128k-2025-02-19'\n  | 'files-api-2025-04-14'\n  | 'mcp-client-2025-04-04'\n  | 'dev-full-thinking-2025-05-14'\n  | 'interleaved-thinking-2025-05-14'\n  | 'code-execution-2025-05-22'\n  | 'extended-cache-ttl-2025-04-11';\n\nexport interface BetaAPIError {\n  message: string;\n\n  type: 'api_error';\n}\n\nexport interface BetaAuthenticationError {\n  message: string;\n\n  type: 'authentication_error';\n}\n\nexport interface BetaBillingError {\n  message: string;\n\n  type: 'billing_error';\n}\n\nexport type BetaError =\n  | BetaInvalidRequestError\n  | BetaAuthenticationError\n  | BetaBillingError\n  | BetaPermissionError\n  | BetaNotFoundError\n  | BetaRateLimitError\n  | BetaGatewayTimeoutError\n  | BetaAPIError\n  | BetaOverloadedError;\n\nexport interface BetaErrorResponse {\n  error: BetaError;\n\n  type: 'error';\n}\n\nexport interface BetaGatewayTimeoutError {\n  message: string;\n\n  type: 'timeout_error';\n}\n\nexport interface BetaInvalidRequestError {\n  message: string;\n\n  type: 'invalid_request_error';\n}\n\nexport interface BetaNotFoundError {\n  message: string;\n\n  type: 'not_found_error';\n}\n\nexport interface BetaOverloadedError {\n  message: string;\n\n  type: 'overloaded_error';\n}\n\nexport interface BetaPermissionError {\n  message: string;\n\n  type: 'permission_error';\n}\n\nexport interface BetaRateLimitError {\n  message: string;\n\n  type: 'rate_limit_error';\n}\n\nBeta.Models = Models;\nBeta.Messages = Messages;\nBeta.Files = Files;\n\nexport declare namespace Beta {\n  export {\n    type AnthropicBeta as AnthropicBeta,\n    type BetaAPIError as BetaAPIError,\n    type BetaAuthenticationError as BetaAuthenticationError,\n    type BetaBillingError as BetaBillingError,\n    type BetaError as BetaError,\n    type BetaErrorResponse as BetaErrorResponse,\n    type BetaGatewayTimeoutError as BetaGatewayTimeoutError,\n    type BetaInvalidRequestError as BetaInvalidRequestError,\n    type BetaNotFoundError as BetaNotFoundError,\n    type BetaOverloadedError as BetaOverloadedError,\n    type BetaPermissionError as BetaPermissionError,\n    type BetaRateLimitError as BetaRateLimitError,\n  };\n\n  export {\n    Models as Models,\n    type BetaModelInfo as BetaModelInfo,\n    type BetaModelInfosPage as BetaModelInfosPage,\n    type ModelRetrieveParams as ModelRetrieveParams,\n    type ModelListParams as ModelListParams,\n  };\n\n  export {\n    Messages as Messages,\n    type BetaBase64ImageSource as BetaBase64ImageSource,\n    type BetaBase64PDFBlock as BetaBase64PDFBlock,\n    type BetaBase64PDFSource as BetaBase64PDFSource,\n    type BetaCacheControlEphemeral as BetaCacheControlEphemeral,\n    type BetaCacheCreation as BetaCacheCreation,\n    type BetaCitationCharLocation as BetaCitationCharLocation,\n    type BetaCitationCharLocationParam as BetaCitationCharLocationParam,\n    type BetaCitationContentBlockLocation as BetaCitationContentBlockLocation,\n    type BetaCitationContentBlockLocationParam as BetaCitationContentBlockLocationParam,\n    type BetaCitationPageLocation as BetaCitationPageLocation,\n    type BetaCitationPageLocationParam as BetaCitationPageLocationParam,\n    type BetaCitationWebSearchResultLocationParam as BetaCitationWebSearchResultLocationParam,\n    type BetaCitationsConfigParam as BetaCitationsConfigParam,\n    type BetaCitationsDelta as BetaCitationsDelta,\n    type BetaCitationsWebSearchResultLocation as BetaCitationsWebSearchResultLocation,\n    type BetaCodeExecutionOutputBlock as BetaCodeExecutionOutputBlock,\n    type BetaCodeExecutionOutputBlockParam as BetaCodeExecutionOutputBlockParam,\n    type BetaCodeExecutionResultBlock as BetaCodeExecutionResultBlock,\n    type BetaCodeExecutionResultBlockParam as BetaCodeExecutionResultBlockParam,\n    type BetaCodeExecutionTool20250522 as BetaCodeExecutionTool20250522,\n    type BetaCodeExecutionToolResultBlock as BetaCodeExecutionToolResultBlock,\n    type BetaCodeExecutionToolResultBlockContent as BetaCodeExecutionToolResultBlockContent,\n    type BetaCodeExecutionToolResultBlockParam as BetaCodeExecutionToolResultBlockParam,\n    type BetaCodeExecutionToolResultBlockParamContent as BetaCodeExecutionToolResultBlockParamContent,\n    type BetaCodeExecutionToolResultError as BetaCodeExecutionToolResultError,\n    type BetaCodeExecutionToolResultErrorCode as BetaCodeExecutionToolResultErrorCode,\n    type BetaCodeExecutionToolResultErrorParam as BetaCodeExecutionToolResultErrorParam,\n    type BetaContainer as BetaContainer,\n    type BetaContainerUploadBlock as BetaContainerUploadBlock,\n    type BetaContainerUploadBlockParam as BetaContainerUploadBlockParam,\n    type BetaContentBlock as BetaContentBlock,\n    type BetaContentBlockParam as BetaContentBlockParam,\n    type BetaContentBlockSource as BetaContentBlockSource,\n    type BetaContentBlockSourceContent as BetaContentBlockSourceContent,\n    type BetaFileDocumentSource as BetaFileDocumentSource,\n    type BetaFileImageSource as BetaFileImageSource,\n    type BetaImageBlockParam as BetaImageBlockParam,\n    type BetaInputJSONDelta as BetaInputJSONDelta,\n    type BetaMCPToolResultBlock as BetaMCPToolResultBlock,\n    type BetaMCPToolUseBlock as BetaMCPToolUseBlock,\n    type BetaMCPToolUseBlockParam as BetaMCPToolUseBlockParam,\n    type BetaMessage as BetaMessage,\n    type BetaMessageDeltaUsage as BetaMessageDeltaUsage,\n    type BetaMessageParam as BetaMessageParam,\n    type BetaMessageTokensCount as BetaMessageTokensCount,\n    type BetaMetadata as BetaMetadata,\n    type BetaPlainTextSource as BetaPlainTextSource,\n    type BetaRawContentBlockDelta as BetaRawContentBlockDelta,\n    type BetaRawContentBlockDeltaEvent as BetaRawContentBlockDeltaEvent,\n    type BetaRawContentBlockStartEvent as BetaRawContentBlockStartEvent,\n    type BetaRawContentBlockStopEvent as BetaRawContentBlockStopEvent,\n    type BetaRawMessageDeltaEvent as BetaRawMessageDeltaEvent,\n    type BetaRawMessageStartEvent as BetaRawMessageStartEvent,\n    type BetaRawMessageStopEvent as BetaRawMessageStopEvent,\n    type BetaRawMessageStreamEvent as BetaRawMessageStreamEvent,\n    type BetaRedactedThinkingBlock as BetaRedactedThinkingBlock,\n    type BetaRedactedThinkingBlockParam as BetaRedactedThinkingBlockParam,\n    type BetaRequestMCPServerToolConfiguration as BetaRequestMCPServerToolConfiguration,\n    type BetaRequestMCPServerURLDefinition as BetaRequestMCPServerURLDefinition,\n    type BetaRequestMCPToolResultBlockParam as BetaRequestMCPToolResultBlockParam,\n    type BetaServerToolUsage as BetaServerToolUsage,\n    type BetaServerToolUseBlock as BetaServerToolUseBlock,\n    type BetaServerToolUseBlockParam as BetaServerToolUseBlockParam,\n    type BetaSignatureDelta as BetaSignatureDelta,\n    type BetaStopReason as BetaStopReason,\n    type BetaTextBlock as BetaTextBlock,\n    type BetaTextBlockParam as BetaTextBlockParam,\n    type BetaTextCitation as BetaTextCitation,\n    type BetaTextCitationParam as BetaTextCitationParam,\n    type BetaTextDelta as BetaTextDelta,\n    type BetaThinkingBlock as BetaThinkingBlock,\n    type BetaThinkingBlockParam as BetaThinkingBlockParam,\n    type BetaThinkingConfigDisabled as BetaThinkingConfigDisabled,\n    type BetaThinkingConfigEnabled as BetaThinkingConfigEnabled,\n    type BetaThinkingConfigParam as BetaThinkingConfigParam,\n    type BetaThinkingDelta as BetaThinkingDelta,\n    type BetaTool as BetaTool,\n    type BetaToolBash20241022 as BetaToolBash20241022,\n    type BetaToolBash20250124 as BetaToolBash20250124,\n    type BetaToolChoice as BetaToolChoice,\n    type BetaToolChoiceAny as BetaToolChoiceAny,\n    type BetaToolChoiceAuto as BetaToolChoiceAuto,\n    type BetaToolChoiceNone as BetaToolChoiceNone,\n    type BetaToolChoiceTool as BetaToolChoiceTool,\n    type BetaToolComputerUse20241022 as BetaToolComputerUse20241022,\n    type BetaToolComputerUse20250124 as BetaToolComputerUse20250124,\n    type BetaToolResultBlockParam as BetaToolResultBlockParam,\n    type BetaToolTextEditor20241022 as BetaToolTextEditor20241022,\n    type BetaToolTextEditor20250124 as BetaToolTextEditor20250124,\n    type BetaToolTextEditor20250429 as BetaToolTextEditor20250429,\n    type BetaToolUnion as BetaToolUnion,\n    type BetaToolUseBlock as BetaToolUseBlock,\n    type BetaToolUseBlockParam as BetaToolUseBlockParam,\n    type BetaURLImageSource as BetaURLImageSource,\n    type BetaURLPDFSource as BetaURLPDFSource,\n    type BetaUsage as BetaUsage,\n    type BetaWebSearchResultBlock as BetaWebSearchResultBlock,\n    type BetaWebSearchResultBlockParam as BetaWebSearchResultBlockParam,\n    type BetaWebSearchTool20250305 as BetaWebSearchTool20250305,\n    type BetaWebSearchToolRequestError as BetaWebSearchToolRequestError,\n    type BetaWebSearchToolResultBlock as BetaWebSearchToolResultBlock,\n    type BetaWebSearchToolResultBlockContent as BetaWebSearchToolResultBlockContent,\n    type BetaWebSearchToolResultBlockParam as BetaWebSearchToolResultBlockParam,\n    type BetaWebSearchToolResultBlockParamContent as BetaWebSearchToolResultBlockParamContent,\n    type BetaWebSearchToolResultError as BetaWebSearchToolResultError,\n    type BetaWebSearchToolResultErrorCode as BetaWebSearchToolResultErrorCode,\n    type MessageCreateParams as MessageCreateParams,\n    type MessageCreateParamsNonStreaming as MessageCreateParamsNonStreaming,\n    type MessageCreateParamsStreaming as MessageCreateParamsStreaming,\n    type MessageCountTokensParams as MessageCountTokensParams,\n  };\n\n  export {\n    Files as Files,\n    type DeletedFile as DeletedFile,\n    type FileMetadata as FileMetadata,\n    type FileMetadataPage as FileMetadataPage,\n    type FileListParams as FileListParams,\n    type FileDeleteParams as FileDeleteParams,\n    type FileDownloadParams as FileDownloadParams,\n    type FileRetrieveMetadataParams as FileRetrieveMetadataParams,\n    type FileUploadParams as FileUploadParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../core/resource';\nimport * as CompletionsAPI from './completions';\nimport * as BetaAPI from './beta/beta';\nimport * as MessagesAPI from './messages/messages';\nimport { APIPromise } from '../core/api-promise';\nimport { Stream } from '../core/streaming';\nimport { buildHeaders } from '../internal/headers';\nimport { RequestOptions } from '../internal/request-options';\n\nexport class Completions extends APIResource {\n  /**\n   * [Legacy] Create a Text Completion.\n   *\n   * The Text Completions API is a legacy API. We recommend using the\n   * [Messages API](https://docs.anthropic.com/en/api/messages) going forward.\n   *\n   * Future models and features will not be compatible with Text Completions. See our\n   * [migration guide](https://docs.anthropic.com/en/api/migrating-from-text-completions-to-messages)\n   * for guidance in migrating from Text Completions to Messages.\n   *\n   * @example\n   * ```ts\n   * const completion = await client.completions.create({\n   *   max_tokens_to_sample: 256,\n   *   model: 'claude-3-7-sonnet-latest',\n   *   prompt: '\\n\\nHuman: Hello, world!\\n\\nAssistant:',\n   * });\n   * ```\n   */\n  create(params: CompletionCreateParamsNonStreaming, options?: RequestOptions): APIPromise<Completion>;\n  create(params: CompletionCreateParamsStreaming, options?: RequestOptions): APIPromise<Stream<Completion>>;\n  create(\n    params: CompletionCreateParamsBase,\n    options?: RequestOptions,\n  ): APIPromise<Stream<Completion> | Completion>;\n  create(\n    params: CompletionCreateParams,\n    options?: RequestOptions,\n  ): APIPromise<Completion> | APIPromise<Stream<Completion>> {\n    const { betas, ...body } = params;\n    return this._client.post('/v1/complete', {\n      body,\n      timeout: (this._client as any)._options.timeout ?? 600000,\n      ...options,\n      headers: buildHeaders([\n        { ...(betas?.toString() != null ? { 'anthropic-beta': betas?.toString() } : undefined) },\n        options?.headers,\n      ]),\n      stream: params.stream ?? false,\n    }) as APIPromise<Completion> | APIPromise<Stream<Completion>>;\n  }\n}\n\nexport interface Completion {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * The resulting completion up to and excluding the stop sequences.\n   */\n  completion: string;\n\n  /**\n   * The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: MessagesAPI.Model;\n\n  /**\n   * The reason that we stopped.\n   *\n   * This may be one the following values:\n   *\n   * - `\"stop_sequence\"`: we reached a stop sequence — either provided by you via the\n   *   `stop_sequences` parameter, or a stop sequence built into the model\n   * - `\"max_tokens\"`: we exceeded `max_tokens_to_sample` or the model's maximum\n   */\n  stop_reason: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Text Completions, this is always `\"completion\"`.\n   */\n  type: 'completion';\n}\n\nexport type CompletionCreateParams = CompletionCreateParamsNonStreaming | CompletionCreateParamsStreaming;\n\nexport interface CompletionCreateParamsBase {\n  /**\n   * Body param: The maximum number of tokens to generate before stopping.\n   *\n   * Note that our models may stop _before_ reaching this maximum. This parameter\n   * only specifies the absolute maximum number of tokens to generate.\n   */\n  max_tokens_to_sample: number;\n\n  /**\n   * Body param: The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: MessagesAPI.Model;\n\n  /**\n   * Body param: The prompt that you want Claude to complete.\n   *\n   * For proper response generation you will need to format your prompt using\n   * alternating `\\n\\nHuman:` and `\\n\\nAssistant:` conversational turns. For example:\n   *\n   * ```\n   * \"\\n\\nHuman: {userQuestion}\\n\\nAssistant:\"\n   * ```\n   *\n   * See [prompt validation](https://docs.anthropic.com/en/api/prompt-validation) and\n   * our guide to\n   * [prompt design](https://docs.anthropic.com/en/docs/intro-to-prompting) for more\n   * details.\n   */\n  prompt: string;\n\n  /**\n   * Body param: An object describing metadata about the request.\n   */\n  metadata?: MessagesAPI.Metadata;\n\n  /**\n   * Body param: Sequences that will cause the model to stop generating.\n   *\n   * Our models stop on `\"\\n\\nHuman:\"`, and may include additional built-in stop\n   * sequences in the future. By providing the stop_sequences parameter, you may\n   * include additional strings that will cause the model to stop generating.\n   */\n  stop_sequences?: Array<string>;\n\n  /**\n   * Body param: Whether to incrementally stream the response using server-sent\n   * events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/streaming) for details.\n   */\n  stream?: boolean;\n\n  /**\n   * Body param: Amount of randomness injected into the response.\n   *\n   * Defaults to `1.0`. Ranges from `0.0` to `1.0`. Use `temperature` closer to `0.0`\n   * for analytical / multiple choice, and closer to `1.0` for creative and\n   * generative tasks.\n   *\n   * Note that even with `temperature` of `0.0`, the results will not be fully\n   * deterministic.\n   */\n  temperature?: number;\n\n  /**\n   * Body param: Only sample from the top K options for each subsequent token.\n   *\n   * Used to remove \"long tail\" low probability responses.\n   * [Learn more technical details here](https://towardsdatascience.com/how-to-sample-from-language-models-682bceb97277).\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_k?: number;\n\n  /**\n   * Body param: Use nucleus sampling.\n   *\n   * In nucleus sampling, we compute the cumulative distribution over all the options\n   * for each subsequent token in decreasing probability order and cut it off once it\n   * reaches a particular probability specified by `top_p`. You should either alter\n   * `temperature` or `top_p`, but not both.\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_p?: number;\n\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport namespace CompletionCreateParams {\n  /**\n   * @deprecated use `Anthropic.Messages.Metadata` instead\n   */\n  export type Metadata = MessagesAPI.Metadata;\n\n  export type CompletionCreateParamsNonStreaming = CompletionsAPI.CompletionCreateParamsNonStreaming;\n  export type CompletionCreateParamsStreaming = CompletionsAPI.CompletionCreateParamsStreaming;\n}\n\nexport interface CompletionCreateParamsNonStreaming extends CompletionCreateParamsBase {\n  /**\n   * Body param: Whether to incrementally stream the response using server-sent\n   * events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/streaming) for details.\n   */\n  stream?: false;\n}\n\nexport interface CompletionCreateParamsStreaming extends CompletionCreateParamsBase {\n  /**\n   * Body param: Whether to incrementally stream the response using server-sent\n   * events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/streaming) for details.\n   */\n  stream: true;\n}\n\nexport declare namespace Completions {\n  export {\n    type Completion as Completion,\n    type CompletionCreateParams as CompletionCreateParams,\n    type CompletionCreateParamsNonStreaming as CompletionCreateParamsNonStreaming,\n    type CompletionCreateParamsStreaming as CompletionCreateParamsStreaming,\n  };\n}\n", "import { isAbortError } from '../internal/errors';\nimport { AnthropicError, APIUserAbortError } from '../error';\nimport {\n  type ContentBlock,\n  Messages,\n  type Message,\n  type MessageStreamEvent,\n  type MessageParam,\n  type MessageCreateParams,\n  type MessageCreateParamsBase,\n  type TextBlock,\n  type TextCitation,\n} from '../resources/messages';\nimport { Stream } from '../streaming';\nimport { partialParse } from '../_vendor/partial-json-parser/parser';\nimport { RequestOptions } from '../internal/request-options';\nimport { type ReadableStream } from '../internal/shim-types';\n\nexport interface MessageStreamEvents {\n  connect: () => void;\n  streamEvent: (event: MessageStreamEvent, snapshot: Message) => void;\n  text: (textDelta: string, textSnapshot: string) => void;\n  citation: (citation: TextCitation, citationsSnapshot: TextCitation[]) => void;\n  inputJson: (partialJson: string, jsonSnapshot: unknown) => void;\n  thinking: (thinkingDelta: string, thinkingSnapshot: string) => void;\n  signature: (signature: string) => void;\n  message: (message: Message) => void;\n  contentBlock: (content: ContentBlock) => void;\n  finalMessage: (message: Message) => void;\n  error: (error: AnthropicError) => void;\n  abort: (error: APIUserAbortError) => void;\n  end: () => void;\n}\n\ntype MessageStreamEventListeners<Event extends keyof MessageStreamEvents> = {\n  listener: MessageStreamEvents[Event];\n  once?: boolean;\n}[];\n\nconst JSON_BUF_PROPERTY = '__json_buf';\n\nexport class MessageStream implements AsyncIterable<MessageStreamEvent> {\n  messages: MessageParam[] = [];\n  receivedMessages: Message[] = [];\n  #currentMessageSnapshot: Message | undefined;\n\n  controller: AbortController = new AbortController();\n\n  #connectedPromise: Promise<Response | null>;\n  #resolveConnectedPromise: (response: Response | null) => void = () => {};\n  #rejectConnectedPromise: (error: AnthropicError) => void = () => {};\n\n  #endPromise: Promise<void>;\n  #resolveEndPromise: () => void = () => {};\n  #rejectEndPromise: (error: AnthropicError) => void = () => {};\n\n  #listeners: { [Event in keyof MessageStreamEvents]?: MessageStreamEventListeners<Event> } = {};\n\n  #ended = false;\n  #errored = false;\n  #aborted = false;\n  #catchingPromiseCreated = false;\n  #response: Response | null | undefined;\n  #request_id: string | null | undefined;\n\n  constructor() {\n    this.#connectedPromise = new Promise<Response | null>((resolve, reject) => {\n      this.#resolveConnectedPromise = resolve;\n      this.#rejectConnectedPromise = reject;\n    });\n\n    this.#endPromise = new Promise<void>((resolve, reject) => {\n      this.#resolveEndPromise = resolve;\n      this.#rejectEndPromise = reject;\n    });\n\n    // Don't let these promises cause unhandled rejection errors.\n    // we will manually cause an unhandled rejection error later\n    // if the user hasn't registered any error listener or called\n    // any promise-returning method.\n    this.#connectedPromise.catch(() => {});\n    this.#endPromise.catch(() => {});\n  }\n\n  get response(): Response | null | undefined {\n    return this.#response;\n  }\n\n  get request_id(): string | null | undefined {\n    return this.#request_id;\n  }\n\n  /**\n   * Returns the `MessageStream` data, the raw `Response` instance and the ID of the request,\n   * returned vie the `request-id` header which is useful for debugging requests and resporting\n   * issues to Anthropic.\n   *\n   * This is the same as the `APIPromise.withResponse()` method.\n   *\n   * This method will raise an error if you created the stream using `MessageStream.fromReadableStream`\n   * as no `Response` is available.\n   */\n  async withResponse(): Promise<{\n    data: MessageStream;\n    response: Response;\n    request_id: string | null | undefined;\n  }> {\n    const response = await this.#connectedPromise;\n    if (!response) {\n      throw new Error('Could not resolve a `Response` object');\n    }\n\n    return {\n      data: this,\n      response,\n      request_id: response.headers.get('request-id'),\n    };\n  }\n\n  /**\n   * Intended for use on the frontend, consuming a stream produced with\n   * `.toReadableStream()` on the backend.\n   *\n   * Note that messages sent to the model do not appear in `.on('message')`\n   * in this context.\n   */\n  static fromReadableStream(stream: ReadableStream): MessageStream {\n    const runner = new MessageStream();\n    runner._run(() => runner._fromReadableStream(stream));\n    return runner;\n  }\n\n  static createMessage(\n    messages: Messages,\n    params: MessageCreateParamsBase,\n    options?: RequestOptions,\n  ): MessageStream {\n    const runner = new MessageStream();\n    for (const message of params.messages) {\n      runner._addMessageParam(message);\n    }\n    runner._run(() =>\n      runner._createMessage(\n        messages,\n        { ...params, stream: true },\n        { ...options, headers: { ...options?.headers, 'X-Stainless-Helper-Method': 'stream' } },\n      ),\n    );\n    return runner;\n  }\n\n  protected _run(executor: () => Promise<any>) {\n    executor().then(() => {\n      this._emitFinal();\n      this._emit('end');\n    }, this.#handleError);\n  }\n\n  protected _addMessageParam(message: MessageParam) {\n    this.messages.push(message);\n  }\n\n  protected _addMessage(message: Message, emit = true) {\n    this.receivedMessages.push(message);\n    if (emit) {\n      this._emit('message', message);\n    }\n  }\n\n  protected async _createMessage(\n    messages: Messages,\n    params: MessageCreateParams,\n    options?: RequestOptions,\n  ): Promise<void> {\n    const signal = options?.signal;\n    if (signal) {\n      if (signal.aborted) this.controller.abort();\n      signal.addEventListener('abort', () => this.controller.abort());\n    }\n    this.#beginRequest();\n    const { response, data: stream } = await messages\n      .create({ ...params, stream: true }, { ...options, signal: this.controller.signal })\n      .withResponse();\n    this._connected(response);\n    for await (const event of stream) {\n      this.#addStreamEvent(event);\n    }\n    if (stream.controller.signal?.aborted) {\n      throw new APIUserAbortError();\n    }\n    this.#endRequest();\n  }\n\n  protected _connected(response: Response | null) {\n    if (this.ended) return;\n    this.#response = response;\n    this.#request_id = response?.headers.get('request-id');\n    this.#resolveConnectedPromise(response);\n    this._emit('connect');\n  }\n\n  get ended(): boolean {\n    return this.#ended;\n  }\n\n  get errored(): boolean {\n    return this.#errored;\n  }\n\n  get aborted(): boolean {\n    return this.#aborted;\n  }\n\n  abort() {\n    this.controller.abort();\n  }\n\n  /**\n   * Adds the listener function to the end of the listeners array for the event.\n   * No checks are made to see if the listener has already been added. Multiple calls passing\n   * the same combination of event and listener will result in the listener being added, and\n   * called, multiple times.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  on<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners: MessageStreamEventListeners<Event> =\n      this.#listeners[event] || (this.#listeners[event] = []);\n    listeners.push({ listener });\n    return this;\n  }\n\n  /**\n   * Removes the specified listener from the listener array for the event.\n   * off() will remove, at most, one instance of a listener from the listener array. If any single\n   * listener has been added multiple times to the listener array for the specified event, then\n   * off() must be called multiple times to remove each instance.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  off<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners = this.#listeners[event];\n    if (!listeners) return this;\n    const index = listeners.findIndex((l) => l.listener === listener);\n    if (index >= 0) listeners.splice(index, 1);\n    return this;\n  }\n\n  /**\n   * Adds a one-time listener function for the event. The next time the event is triggered,\n   * this listener is removed and then invoked.\n   * @returns this MessageStream, so that calls can be chained\n   */\n  once<Event extends keyof MessageStreamEvents>(event: Event, listener: MessageStreamEvents[Event]): this {\n    const listeners: MessageStreamEventListeners<Event> =\n      this.#listeners[event] || (this.#listeners[event] = []);\n    listeners.push({ listener, once: true });\n    return this;\n  }\n\n  /**\n   * This is similar to `.once()`, but returns a Promise that resolves the next time\n   * the event is triggered, instead of calling a listener callback.\n   * @returns a Promise that resolves the next time given event is triggered,\n   * or rejects if an error is emitted.  (If you request the 'error' event,\n   * returns a promise that resolves with the error).\n   *\n   * Example:\n   *\n   *   const message = await stream.emitted('message') // rejects if the stream errors\n   */\n  emitted<Event extends keyof MessageStreamEvents>(\n    event: Event,\n  ): Promise<\n    Parameters<MessageStreamEvents[Event]> extends [infer Param] ? Param\n    : Parameters<MessageStreamEvents[Event]> extends [] ? void\n    : Parameters<MessageStreamEvents[Event]>\n  > {\n    return new Promise((resolve, reject) => {\n      this.#catchingPromiseCreated = true;\n      if (event !== 'error') this.once('error', reject);\n      this.once(event, resolve as any);\n    });\n  }\n\n  async done(): Promise<void> {\n    this.#catchingPromiseCreated = true;\n    await this.#endPromise;\n  }\n\n  get currentMessage(): Message | undefined {\n    return this.#currentMessageSnapshot;\n  }\n\n  #getFinalMessage(): Message {\n    if (this.receivedMessages.length === 0) {\n      throw new AnthropicError('stream ended without producing a Message with role=assistant');\n    }\n    return this.receivedMessages.at(-1)!;\n  }\n\n  /**\n   * @returns a promise that resolves with the the final assistant Message response,\n   * or rejects if an error occurred or the stream ended prematurely without producing a Message.\n   */\n  async finalMessage(): Promise<Message> {\n    await this.done();\n    return this.#getFinalMessage();\n  }\n\n  #getFinalText(): string {\n    if (this.receivedMessages.length === 0) {\n      throw new AnthropicError('stream ended without producing a Message with role=assistant');\n    }\n    const textBlocks = this.receivedMessages\n      .at(-1)!\n      .content.filter((block): block is TextBlock => block.type === 'text')\n      .map((block) => block.text);\n    if (textBlocks.length === 0) {\n      throw new AnthropicError('stream ended without producing a content block with type=text');\n    }\n    return textBlocks.join(' ');\n  }\n\n  /**\n   * @returns a promise that resolves with the the final assistant Message's text response, concatenated\n   * together if there are more than one text blocks.\n   * Rejects if an error occurred or the stream ended prematurely without producing a Message.\n   */\n  async finalText(): Promise<string> {\n    await this.done();\n    return this.#getFinalText();\n  }\n\n  #handleError = (error: unknown) => {\n    this.#errored = true;\n    if (isAbortError(error)) {\n      error = new APIUserAbortError();\n    }\n    if (error instanceof APIUserAbortError) {\n      this.#aborted = true;\n      return this._emit('abort', error);\n    }\n    if (error instanceof AnthropicError) {\n      return this._emit('error', error);\n    }\n    if (error instanceof Error) {\n      const anthropicError: AnthropicError = new AnthropicError(error.message);\n      // @ts-ignore\n      anthropicError.cause = error;\n      return this._emit('error', anthropicError);\n    }\n    return this._emit('error', new AnthropicError(String(error)));\n  };\n\n  protected _emit<Event extends keyof MessageStreamEvents>(\n    event: Event,\n    ...args: Parameters<MessageStreamEvents[Event]>\n  ) {\n    // make sure we don't emit any MessageStreamEvents after end\n    if (this.#ended) return;\n\n    if (event === 'end') {\n      this.#ended = true;\n      this.#resolveEndPromise();\n    }\n\n    const listeners: MessageStreamEventListeners<Event> | undefined = this.#listeners[event];\n    if (listeners) {\n      this.#listeners[event] = listeners.filter((l) => !l.once) as any;\n      listeners.forEach(({ listener }: any) => listener(...args));\n    }\n\n    if (event === 'abort') {\n      const error = args[0] as APIUserAbortError;\n      if (!this.#catchingPromiseCreated && !listeners?.length) {\n        Promise.reject(error);\n      }\n      this.#rejectConnectedPromise(error);\n      this.#rejectEndPromise(error);\n      this._emit('end');\n      return;\n    }\n\n    if (event === 'error') {\n      // NOTE: _emit('error', error) should only be called from #handleError().\n\n      const error = args[0] as AnthropicError;\n      if (!this.#catchingPromiseCreated && !listeners?.length) {\n        // Trigger an unhandled rejection if the user hasn't registered any error handlers.\n        // If you are seeing stack traces here, make sure to handle errors via either:\n        // - runner.on('error', () => ...)\n        // - await runner.done()\n        // - await runner.final...()\n        // - etc.\n        Promise.reject(error);\n      }\n      this.#rejectConnectedPromise(error);\n      this.#rejectEndPromise(error);\n      this._emit('end');\n    }\n  }\n\n  protected _emitFinal() {\n    const finalMessage = this.receivedMessages.at(-1);\n    if (finalMessage) {\n      this._emit('finalMessage', this.#getFinalMessage());\n    }\n  }\n\n  #beginRequest() {\n    if (this.ended) return;\n    this.#currentMessageSnapshot = undefined;\n  }\n  #addStreamEvent(event: MessageStreamEvent) {\n    if (this.ended) return;\n    const messageSnapshot = this.#accumulateMessage(event);\n    this._emit('streamEvent', event, messageSnapshot);\n\n    switch (event.type) {\n      case 'content_block_delta': {\n        const content = messageSnapshot.content.at(-1)!;\n        switch (event.delta.type) {\n          case 'text_delta': {\n            if (content.type === 'text') {\n              this._emit('text', event.delta.text, content.text || '');\n            }\n            break;\n          }\n          case 'citations_delta': {\n            if (content.type === 'text') {\n              this._emit('citation', event.delta.citation, content.citations ?? []);\n            }\n            break;\n          }\n          case 'input_json_delta': {\n            if (content.type === 'tool_use' && content.input) {\n              this._emit('inputJson', event.delta.partial_json, content.input);\n            }\n            break;\n          }\n          case 'thinking_delta': {\n            if (content.type === 'thinking') {\n              this._emit('thinking', event.delta.thinking, content.thinking);\n            }\n            break;\n          }\n          case 'signature_delta': {\n            if (content.type === 'thinking') {\n              this._emit('signature', content.signature);\n            }\n            break;\n          }\n          default:\n            checkNever(event.delta);\n        }\n        break;\n      }\n      case 'message_stop': {\n        this._addMessageParam(messageSnapshot);\n        this._addMessage(messageSnapshot, true);\n        break;\n      }\n      case 'content_block_stop': {\n        this._emit('contentBlock', messageSnapshot.content.at(-1)!);\n        break;\n      }\n      case 'message_start': {\n        this.#currentMessageSnapshot = messageSnapshot;\n        break;\n      }\n      case 'content_block_start':\n      case 'message_delta':\n        break;\n    }\n  }\n  #endRequest(): Message {\n    if (this.ended) {\n      throw new AnthropicError(`stream has ended, this shouldn't happen`);\n    }\n    const snapshot = this.#currentMessageSnapshot;\n    if (!snapshot) {\n      throw new AnthropicError(`request ended without sending any chunks`);\n    }\n    this.#currentMessageSnapshot = undefined;\n    return snapshot;\n  }\n\n  protected async _fromReadableStream(\n    readableStream: ReadableStream,\n    options?: RequestOptions,\n  ): Promise<void> {\n    const signal = options?.signal;\n    if (signal) {\n      if (signal.aborted) this.controller.abort();\n      signal.addEventListener('abort', () => this.controller.abort());\n    }\n    this.#beginRequest();\n    this._connected(null);\n    const stream = Stream.fromReadableStream<MessageStreamEvent>(readableStream, this.controller);\n    for await (const event of stream) {\n      this.#addStreamEvent(event);\n    }\n    if (stream.controller.signal?.aborted) {\n      throw new APIUserAbortError();\n    }\n    this.#endRequest();\n  }\n\n  /**\n   * Mutates this.#currentMessage with the current event. Handling the accumulation of multiple messages\n   * will be needed to be handled by the caller, this method will throw if you try to accumulate for multiple\n   * messages.\n   */\n  #accumulateMessage(event: MessageStreamEvent): Message {\n    let snapshot = this.#currentMessageSnapshot;\n\n    if (event.type === 'message_start') {\n      if (snapshot) {\n        throw new AnthropicError(`Unexpected event order, got ${event.type} before receiving \"message_stop\"`);\n      }\n      return event.message;\n    }\n\n    if (!snapshot) {\n      throw new AnthropicError(`Unexpected event order, got ${event.type} before \"message_start\"`);\n    }\n\n    switch (event.type) {\n      case 'message_stop':\n        return snapshot;\n      case 'message_delta':\n        snapshot.stop_reason = event.delta.stop_reason;\n        snapshot.stop_sequence = event.delta.stop_sequence;\n        snapshot.usage.output_tokens = event.usage.output_tokens;\n\n        // Update other usage fields if they exist in the event\n        if (event.usage.input_tokens != null) {\n          snapshot.usage.input_tokens = event.usage.input_tokens;\n        }\n\n        if (event.usage.cache_creation_input_tokens != null) {\n          snapshot.usage.cache_creation_input_tokens = event.usage.cache_creation_input_tokens;\n        }\n\n        if (event.usage.cache_read_input_tokens != null) {\n          snapshot.usage.cache_read_input_tokens = event.usage.cache_read_input_tokens;\n        }\n\n        if (event.usage.server_tool_use != null) {\n          snapshot.usage.server_tool_use = event.usage.server_tool_use;\n        }\n\n        return snapshot;\n      case 'content_block_start':\n        snapshot.content.push(event.content_block);\n        return snapshot;\n      case 'content_block_delta': {\n        const snapshotContent = snapshot.content.at(event.index);\n\n        switch (event.delta.type) {\n          case 'text_delta': {\n            if (snapshotContent?.type === 'text') {\n              snapshotContent.text += event.delta.text;\n            }\n            break;\n          }\n          case 'citations_delta': {\n            if (snapshotContent?.type === 'text') {\n              snapshotContent.citations ??= [];\n              snapshotContent.citations.push(event.delta.citation);\n            }\n            break;\n          }\n          case 'input_json_delta': {\n            if (snapshotContent?.type === 'tool_use') {\n              // we need to keep track of the raw JSON string as well so that we can\n              // re-parse it for each delta, for now we just store it as an untyped\n              // non-enumerable property on the snapshot\n              let jsonBuf = (snapshotContent as any)[JSON_BUF_PROPERTY] || '';\n              jsonBuf += event.delta.partial_json;\n\n              Object.defineProperty(snapshotContent, JSON_BUF_PROPERTY, {\n                value: jsonBuf,\n                enumerable: false,\n                writable: true,\n              });\n\n              if (jsonBuf) {\n                snapshotContent.input = partialParse(jsonBuf);\n              }\n            }\n            break;\n          }\n          case 'thinking_delta': {\n            if (snapshotContent?.type === 'thinking') {\n              snapshotContent.thinking += event.delta.thinking;\n            }\n            break;\n          }\n          case 'signature_delta': {\n            if (snapshotContent?.type === 'thinking') {\n              snapshotContent.signature = event.delta.signature;\n            }\n            break;\n          }\n          default:\n            checkNever(event.delta);\n        }\n\n        return snapshot;\n      }\n      case 'content_block_stop':\n        return snapshot;\n    }\n  }\n\n  [Symbol.asyncIterator](): AsyncIterator<MessageStreamEvent> {\n    const pushQueue: MessageStreamEvent[] = [];\n    const readQueue: {\n      resolve: (chunk: MessageStreamEvent | undefined) => void;\n      reject: (error: unknown) => void;\n    }[] = [];\n    let done = false;\n\n    this.on('streamEvent', (event) => {\n      const reader = readQueue.shift();\n      if (reader) {\n        reader.resolve(event);\n      } else {\n        pushQueue.push(event);\n      }\n    });\n\n    this.on('end', () => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.resolve(undefined);\n      }\n      readQueue.length = 0;\n    });\n\n    this.on('abort', (err) => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.reject(err);\n      }\n      readQueue.length = 0;\n    });\n\n    this.on('error', (err) => {\n      done = true;\n      for (const reader of readQueue) {\n        reader.reject(err);\n      }\n      readQueue.length = 0;\n    });\n\n    return {\n      next: async (): Promise<IteratorResult<MessageStreamEvent>> => {\n        if (!pushQueue.length) {\n          if (done) {\n            return { value: undefined, done: true };\n          }\n          return new Promise<MessageStreamEvent | undefined>((resolve, reject) =>\n            readQueue.push({ resolve, reject }),\n          ).then((chunk) => (chunk ? { value: chunk, done: false } : { value: undefined, done: true }));\n        }\n        const chunk = pushQueue.shift()!;\n        return { value: chunk, done: false };\n      },\n      return: async () => {\n        this.abort();\n        return { value: undefined, done: true };\n      },\n    };\n  }\n\n  toReadableStream(): ReadableStream {\n    const stream = new Stream(this[Symbol.asyncIterator].bind(this), this.controller);\n    return stream.toReadableStream();\n  }\n}\n\n// used to ensure exhaustive case matching without throwing a runtime error\nfunction checkNever(x: never) {}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../../core/resource';\nimport * as Shared from '../shared';\nimport * as MessagesAPI from './messages';\nimport { APIPromise } from '../../core/api-promise';\nimport { Page, type PageParams, PagePromise } from '../../core/pagination';\nimport { buildHeaders } from '../../internal/headers';\nimport { RequestOptions } from '../../internal/request-options';\nimport { JSONLDecoder } from '../../internal/decoders/jsonl';\nimport { AnthropicError } from '../../error';\nimport { path } from '../../internal/utils/path';\n\nexport class Batches extends APIResource {\n  /**\n   * Send a batch of Message creation requests.\n   *\n   * The Message Batches API can be used to process multiple Messages API requests at\n   * once. Once a Message Batch is created, it begins processing immediately. Batches\n   * can take up to 24 hours to complete.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const messageBatch = await client.messages.batches.create({\n   *   requests: [\n   *     {\n   *       custom_id: 'my-custom-id-1',\n   *       params: {\n   *         max_tokens: 1024,\n   *         messages: [\n   *           { content: 'Hello, world', role: 'user' },\n   *         ],\n   *         model: 'claude-3-7-sonnet-20250219',\n   *       },\n   *     },\n   *   ],\n   * });\n   * ```\n   */\n  create(body: BatchCreateParams, options?: RequestOptions): APIPromise<MessageBatch> {\n    return this._client.post('/v1/messages/batches', { body, ...options });\n  }\n\n  /**\n   * This endpoint is idempotent and can be used to poll for Message Batch\n   * completion. To access the results of a Message Batch, make a request to the\n   * `results_url` field in the response.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const messageBatch = await client.messages.batches.retrieve(\n   *   'message_batch_id',\n   * );\n   * ```\n   */\n  retrieve(messageBatchID: string, options?: RequestOptions): APIPromise<MessageBatch> {\n    return this._client.get(path`/v1/messages/batches/${messageBatchID}`, options);\n  }\n\n  /**\n   * List all Message Batches within a Workspace. Most recently created batches are\n   * returned first.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * // Automatically fetches more pages as needed.\n   * for await (const messageBatch of client.messages.batches.list()) {\n   *   // ...\n   * }\n   * ```\n   */\n  list(\n    query: BatchListParams | null | undefined = {},\n    options?: RequestOptions,\n  ): PagePromise<MessageBatchesPage, MessageBatch> {\n    return this._client.getAPIList('/v1/messages/batches', Page<MessageBatch>, { query, ...options });\n  }\n\n  /**\n   * Delete a Message Batch.\n   *\n   * Message Batches can only be deleted once they've finished processing. If you'd\n   * like to delete an in-progress batch, you must first cancel it.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const deletedMessageBatch =\n   *   await client.messages.batches.delete('message_batch_id');\n   * ```\n   */\n  delete(messageBatchID: string, options?: RequestOptions): APIPromise<DeletedMessageBatch> {\n    return this._client.delete(path`/v1/messages/batches/${messageBatchID}`, options);\n  }\n\n  /**\n   * Batches may be canceled any time before processing ends. Once cancellation is\n   * initiated, the batch enters a `canceling` state, at which time the system may\n   * complete any in-progress, non-interruptible requests before finalizing\n   * cancellation.\n   *\n   * The number of canceled requests is specified in `request_counts`. To determine\n   * which requests were canceled, check the individual results within the batch.\n   * Note that cancellation may not result in any canceled requests if they were\n   * non-interruptible.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const messageBatch = await client.messages.batches.cancel(\n   *   'message_batch_id',\n   * );\n   * ```\n   */\n  cancel(messageBatchID: string, options?: RequestOptions): APIPromise<MessageBatch> {\n    return this._client.post(path`/v1/messages/batches/${messageBatchID}/cancel`, options);\n  }\n\n  /**\n   * Streams the results of a Message Batch as a `.jsonl` file.\n   *\n   * Each line in the file is a JSON object containing the result of a single request\n   * in the Message Batch. Results are not guaranteed to be in the same order as\n   * requests. Use the `custom_id` field to match results to requests.\n   *\n   * Learn more about the Message Batches API in our\n   * [user guide](/en/docs/build-with-claude/batch-processing)\n   *\n   * @example\n   * ```ts\n   * const messageBatchIndividualResponse =\n   *   await client.messages.batches.results('message_batch_id');\n   * ```\n   */\n  async results(\n    messageBatchID: string,\n    options?: RequestOptions,\n  ): Promise<JSONLDecoder<MessageBatchIndividualResponse>> {\n    const batch = await this.retrieve(messageBatchID);\n    if (!batch.results_url) {\n      throw new AnthropicError(\n        `No batch \\`results_url\\`; Has it finished processing? ${batch.processing_status} - ${batch.id}`,\n      );\n    }\n\n    return this._client\n      .get(batch.results_url, {\n        ...options,\n        headers: buildHeaders([{ Accept: 'application/binary' }, options?.headers]),\n        stream: true,\n        __binaryResponse: true,\n      })\n      ._thenUnwrap((_, props) => JSONLDecoder.fromResponse(props.response, props.controller)) as APIPromise<\n      JSONLDecoder<MessageBatchIndividualResponse>\n    >;\n  }\n}\n\nexport type MessageBatchesPage = Page<MessageBatch>;\n\nexport interface DeletedMessageBatch {\n  /**\n   * ID of the Message Batch.\n   */\n  id: string;\n\n  /**\n   * Deleted object type.\n   *\n   * For Message Batches, this is always `\"message_batch_deleted\"`.\n   */\n  type: 'message_batch_deleted';\n}\n\nexport interface MessageBatch {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the Message Batch was\n   * archived and its results became unavailable.\n   */\n  archived_at: string | null;\n\n  /**\n   * RFC 3339 datetime string representing the time at which cancellation was\n   * initiated for the Message Batch. Specified only if cancellation was initiated.\n   */\n  cancel_initiated_at: string | null;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the Message Batch was\n   * created.\n   */\n  created_at: string;\n\n  /**\n   * RFC 3339 datetime string representing the time at which processing for the\n   * Message Batch ended. Specified only once processing ends.\n   *\n   * Processing ends when every request in a Message Batch has either succeeded,\n   * errored, canceled, or expired.\n   */\n  ended_at: string | null;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the Message Batch will\n   * expire and end processing, which is 24 hours after creation.\n   */\n  expires_at: string;\n\n  /**\n   * Processing status of the Message Batch.\n   */\n  processing_status: 'in_progress' | 'canceling' | 'ended';\n\n  /**\n   * Tallies requests within the Message Batch, categorized by their status.\n   *\n   * Requests start as `processing` and move to one of the other statuses only once\n   * processing of the entire batch ends. The sum of all values always matches the\n   * total number of requests in the batch.\n   */\n  request_counts: MessageBatchRequestCounts;\n\n  /**\n   * URL to a `.jsonl` file containing the results of the Message Batch requests.\n   * Specified only once processing ends.\n   *\n   * Results in the file are not guaranteed to be in the same order as requests. Use\n   * the `custom_id` field to match results to requests.\n   */\n  results_url: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Message Batches, this is always `\"message_batch\"`.\n   */\n  type: 'message_batch';\n}\n\nexport interface MessageBatchCanceledResult {\n  type: 'canceled';\n}\n\nexport interface MessageBatchErroredResult {\n  error: Shared.ErrorResponse;\n\n  type: 'errored';\n}\n\nexport interface MessageBatchExpiredResult {\n  type: 'expired';\n}\n\n/**\n * This is a single line in the response `.jsonl` file and does not represent the\n * response as a whole.\n */\nexport interface MessageBatchIndividualResponse {\n  /**\n   * Developer-provided ID created for each request in a Message Batch. Useful for\n   * matching results to requests, as results may be given out of request order.\n   *\n   * Must be unique for each request within the Message Batch.\n   */\n  custom_id: string;\n\n  /**\n   * Processing result for this request.\n   *\n   * Contains a Message output if processing was successful, an error response if\n   * processing failed, or the reason why processing was not attempted, such as\n   * cancellation or expiration.\n   */\n  result: MessageBatchResult;\n}\n\nexport interface MessageBatchRequestCounts {\n  /**\n   * Number of requests in the Message Batch that have been canceled.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  canceled: number;\n\n  /**\n   * Number of requests in the Message Batch that encountered an error.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  errored: number;\n\n  /**\n   * Number of requests in the Message Batch that have expired.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  expired: number;\n\n  /**\n   * Number of requests in the Message Batch that are processing.\n   */\n  processing: number;\n\n  /**\n   * Number of requests in the Message Batch that have completed successfully.\n   *\n   * This is zero until processing of the entire Message Batch has ended.\n   */\n  succeeded: number;\n}\n\n/**\n * Processing result for this request.\n *\n * Contains a Message output if processing was successful, an error response if\n * processing failed, or the reason why processing was not attempted, such as\n * cancellation or expiration.\n */\nexport type MessageBatchResult =\n  | MessageBatchSucceededResult\n  | MessageBatchErroredResult\n  | MessageBatchCanceledResult\n  | MessageBatchExpiredResult;\n\nexport interface MessageBatchSucceededResult {\n  message: MessagesAPI.Message;\n\n  type: 'succeeded';\n}\n\nexport interface BatchCreateParams {\n  /**\n   * List of requests for prompt completion. Each is an individual request to create\n   * a Message.\n   */\n  requests: Array<BatchCreateParams.Request>;\n}\n\nexport namespace BatchCreateParams {\n  export interface Request {\n    /**\n     * Developer-provided ID created for each request in a Message Batch. Useful for\n     * matching results to requests, as results may be given out of request order.\n     *\n     * Must be unique for each request within the Message Batch.\n     */\n    custom_id: string;\n\n    /**\n     * Messages API creation parameters for the individual request.\n     *\n     * See the [Messages API reference](/en/api/messages) for full documentation on\n     * available parameters.\n     */\n    params: MessagesAPI.MessageCreateParamsNonStreaming;\n  }\n}\n\nexport interface BatchListParams extends PageParams {}\n\nexport declare namespace Batches {\n  export {\n    type DeletedMessageBatch as DeletedMessageBatch,\n    type MessageBatch as MessageBatch,\n    type MessageBatchCanceledResult as MessageBatchCanceledResult,\n    type MessageBatchErroredResult as MessageBatchErroredResult,\n    type MessageBatchExpiredResult as MessageBatchExpiredResult,\n    type MessageBatchIndividualResponse as MessageBatchIndividualResponse,\n    type MessageBatchRequestCounts as MessageBatchRequestCounts,\n    type MessageBatchResult as MessageBatchResult,\n    type MessageBatchSucceededResult as MessageBatchSucceededResult,\n    type MessageBatchesPage as MessageBatchesPage,\n    type BatchCreateParams as BatchCreateParams,\n    type BatchListParams as BatchListParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIPromise } from '../../core/api-promise';\nimport { APIResource } from '../../core/resource';\nimport { Stream } from '../../core/streaming';\nimport { RequestOptions } from '../../internal/request-options';\nimport { MessageStream } from '../../lib/MessageStream';\nimport * as BatchesAPI from './batches';\nimport {\n  BatchCreateParams,\n  BatchListParams,\n  Batches,\n  DeletedMessageBatch,\n  MessageBatch,\n  MessageBatchCanceledResult,\n  MessageBatchErroredResult,\n  MessageBatchExpiredResult,\n  MessageBatchIndividualResponse,\n  MessageBatchRequestCounts,\n  MessageBatchResult,\n  MessageBatchSucceededResult,\n  MessageBatchesPage,\n} from './batches';\nimport * as MessagesAPI from './messages';\n\nimport { MODEL_NONSTREAMING_TOKENS } from '../../internal/constants';\n\nexport class Messages extends APIResource {\n  batches: BatchesAPI.Batches = new BatchesAPI.Batches(this._client);\n\n  /**\n   * Send a structured list of input messages with text and/or image content, and the\n   * model will generate the next message in the conversation.\n   *\n   * The Messages API can be used for either single queries or stateless multi-turn\n   * conversations.\n   *\n   * Learn more about the Messages API in our [user guide](/en/docs/initial-setup)\n   *\n   * @example\n   * ```ts\n   * const message = await client.messages.create({\n   *   max_tokens: 1024,\n   *   messages: [{ content: 'Hello, world', role: 'user' }],\n   *   model: 'claude-3-7-sonnet-20250219',\n   * });\n   * ```\n   */\n  create(body: MessageCreateParamsNonStreaming, options?: RequestOptions): APIPromise<Message>;\n  create(\n    body: MessageCreateParamsStreaming,\n    options?: RequestOptions,\n  ): APIPromise<Stream<RawMessageStreamEvent>>;\n  create(\n    body: MessageCreateParamsBase,\n    options?: RequestOptions,\n  ): APIPromise<Stream<RawMessageStreamEvent> | Message>;\n  create(\n    body: MessageCreateParams,\n    options?: RequestOptions,\n  ): APIPromise<Message> | APIPromise<Stream<RawMessageStreamEvent>> {\n    if (body.model in DEPRECATED_MODELS) {\n      console.warn(\n        `The model '${body.model}' is deprecated and will reach end-of-life on ${\n          DEPRECATED_MODELS[body.model]\n        }\\nPlease migrate to a newer model. Visit https://docs.anthropic.com/en/docs/resources/model-deprecations for more information.`,\n      );\n    }\n    let timeout = (this._client as any)._options.timeout as number | null;\n    if (!body.stream && timeout == null) {\n      const maxNonstreamingTokens = MODEL_NONSTREAMING_TOKENS[body.model] ?? undefined;\n      timeout = this._client.calculateNonstreamingTimeout(body.max_tokens, maxNonstreamingTokens);\n    }\n    return this._client.post('/v1/messages', {\n      body,\n      timeout: timeout ?? 600000,\n      ...options,\n      stream: body.stream ?? false,\n    }) as APIPromise<Message> | APIPromise<Stream<RawMessageStreamEvent>>;\n  }\n\n  /**\n   * Create a Message stream\n   */\n  stream(body: MessageStreamParams, options?: RequestOptions): MessageStream {\n    return MessageStream.createMessage(this, body, options);\n  }\n\n  /**\n   * Count the number of tokens in a Message.\n   *\n   * The Token Count API can be used to count the number of tokens in a Message,\n   * including tools, images, and documents, without creating it.\n   *\n   * Learn more about token counting in our\n   * [user guide](/en/docs/build-with-claude/token-counting)\n   *\n   * @example\n   * ```ts\n   * const messageTokensCount =\n   *   await client.messages.countTokens({\n   *     messages: [{ content: 'string', role: 'user' }],\n   *     model: 'claude-3-7-sonnet-latest',\n   *   });\n   * ```\n   */\n  countTokens(body: MessageCountTokensParams, options?: RequestOptions): APIPromise<MessageTokensCount> {\n    return this._client.post('/v1/messages/count_tokens', { body, ...options });\n  }\n}\n\nexport interface Base64ImageSource {\n  data: string;\n\n  media_type: 'image/jpeg' | 'image/png' | 'image/gif' | 'image/webp';\n\n  type: 'base64';\n}\n\nexport interface Base64PDFSource {\n  data: string;\n\n  media_type: 'application/pdf';\n\n  type: 'base64';\n}\n\nexport interface CacheControlEphemeral {\n  type: 'ephemeral';\n}\n\nexport interface CitationCharLocation {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_char_index: number;\n\n  start_char_index: number;\n\n  type: 'char_location';\n}\n\nexport interface CitationCharLocationParam {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_char_index: number;\n\n  start_char_index: number;\n\n  type: 'char_location';\n}\n\nexport interface CitationContentBlockLocation {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_block_index: number;\n\n  start_block_index: number;\n\n  type: 'content_block_location';\n}\n\nexport interface CitationContentBlockLocationParam {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_block_index: number;\n\n  start_block_index: number;\n\n  type: 'content_block_location';\n}\n\nexport interface CitationPageLocation {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_page_number: number;\n\n  start_page_number: number;\n\n  type: 'page_location';\n}\n\nexport interface CitationPageLocationParam {\n  cited_text: string;\n\n  document_index: number;\n\n  document_title: string | null;\n\n  end_page_number: number;\n\n  start_page_number: number;\n\n  type: 'page_location';\n}\n\nexport interface CitationWebSearchResultLocationParam {\n  cited_text: string;\n\n  encrypted_index: string;\n\n  title: string | null;\n\n  type: 'web_search_result_location';\n\n  url: string;\n}\n\nexport interface CitationsConfigParam {\n  enabled?: boolean;\n}\n\nexport interface CitationsDelta {\n  citation:\n    | CitationCharLocation\n    | CitationPageLocation\n    | CitationContentBlockLocation\n    | CitationsWebSearchResultLocation;\n\n  type: 'citations_delta';\n}\n\nexport interface CitationsWebSearchResultLocation {\n  cited_text: string;\n\n  encrypted_index: string;\n\n  title: string | null;\n\n  type: 'web_search_result_location';\n\n  url: string;\n}\n\nexport type ContentBlock =\n  | TextBlock\n  | ToolUseBlock\n  | ServerToolUseBlock\n  | WebSearchToolResultBlock\n  | ThinkingBlock\n  | RedactedThinkingBlock;\n\n/**\n * Regular text content.\n */\nexport type ContentBlockParam =\n  | ServerToolUseBlockParam\n  | WebSearchToolResultBlockParam\n  | TextBlockParam\n  | ImageBlockParam\n  | ToolUseBlockParam\n  | ToolResultBlockParam\n  | DocumentBlockParam\n  | ThinkingBlockParam\n  | RedactedThinkingBlockParam;\n\nexport interface ContentBlockSource {\n  content: string | Array<ContentBlockSourceContent>;\n\n  type: 'content';\n}\n\nexport type ContentBlockSourceContent = TextBlockParam | ImageBlockParam;\n\nexport interface DocumentBlockParam {\n  source: Base64PDFSource | PlainTextSource | ContentBlockSource | URLPDFSource;\n\n  type: 'document';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n\n  citations?: CitationsConfigParam;\n\n  context?: string | null;\n\n  title?: string | null;\n}\n\nexport interface ImageBlockParam {\n  source: Base64ImageSource | URLImageSource;\n\n  type: 'image';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n}\n\nexport interface InputJSONDelta {\n  partial_json: string;\n\n  type: 'input_json_delta';\n}\n\nexport interface Message {\n  /**\n   * Unique object identifier.\n   *\n   * The format and length of IDs may change over time.\n   */\n  id: string;\n\n  /**\n   * Content generated by the model.\n   *\n   * This is an array of content blocks, each of which has a `type` that determines\n   * its shape.\n   *\n   * Example:\n   *\n   * ```json\n   * [{ \"type\": \"text\", \"text\": \"Hi, I'm Claude.\" }]\n   * ```\n   *\n   * If the request input `messages` ended with an `assistant` turn, then the\n   * response `content` will continue directly from that last turn. You can use this\n   * to constrain the model's output.\n   *\n   * For example, if the input `messages` were:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Then the response `content` might be:\n   *\n   * ```json\n   * [{ \"type\": \"text\", \"text\": \"B)\" }]\n   * ```\n   */\n  content: Array<ContentBlock>;\n\n  /**\n   * The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: Model;\n\n  /**\n   * Conversational role of the generated message.\n   *\n   * This will always be `\"assistant\"`.\n   */\n  role: 'assistant';\n\n  /**\n   * The reason that we stopped.\n   *\n   * This may be one the following values:\n   *\n   * - `\"end_turn\"`: the model reached a natural stopping point\n   * - `\"max_tokens\"`: we exceeded the requested `max_tokens` or the model's maximum\n   * - `\"stop_sequence\"`: one of your provided custom `stop_sequences` was generated\n   * - `\"tool_use\"`: the model invoked one or more tools\n   *\n   * In non-streaming mode this value is always non-null. In streaming mode, it is\n   * null in the `message_start` event and non-null otherwise.\n   */\n  stop_reason: StopReason | null;\n\n  /**\n   * Which custom stop sequence was generated, if any.\n   *\n   * This value will be a non-null string if one of your custom stop sequences was\n   * generated.\n   */\n  stop_sequence: string | null;\n\n  /**\n   * Object type.\n   *\n   * For Messages, this is always `\"message\"`.\n   */\n  type: 'message';\n\n  /**\n   * Billing and rate-limit usage.\n   *\n   * Anthropic's API bills and rate-limits by token counts, as tokens represent the\n   * underlying cost to our systems.\n   *\n   * Under the hood, the API transforms requests into a format suitable for the\n   * model. The model's output then goes through a parsing stage before becoming an\n   * API response. As a result, the token counts in `usage` will not match one-to-one\n   * with the exact visible content of an API request or response.\n   *\n   * For example, `output_tokens` will be non-zero, even for an empty string response\n   * from Claude.\n   *\n   * Total input tokens in a request is the summation of `input_tokens`,\n   * `cache_creation_input_tokens`, and `cache_read_input_tokens`.\n   */\n  usage: Usage;\n}\n\nexport type MessageCountTokensTool = Tool | ToolBash20250124 | ToolTextEditor20250124 | WebSearchTool20250305;\n\nexport interface MessageDeltaUsage {\n  /**\n   * The cumulative number of input tokens used to create the cache entry.\n   */\n  cache_creation_input_tokens: number | null;\n\n  /**\n   * The cumulative number of input tokens read from the cache.\n   */\n  cache_read_input_tokens: number | null;\n\n  /**\n   * The cumulative number of input tokens which were used.\n   */\n  input_tokens: number | null;\n\n  /**\n   * The cumulative number of output tokens which were used.\n   */\n  output_tokens: number;\n\n  /**\n   * The number of server tool requests.\n   */\n  server_tool_use: ServerToolUsage | null;\n}\n\nexport interface MessageParam {\n  content: string | Array<ContentBlockParam>;\n\n  role: 'user' | 'assistant';\n}\n\nexport interface MessageTokensCount {\n  /**\n   * The total number of tokens across the provided list of messages, system prompt,\n   * and tools.\n   */\n  input_tokens: number;\n}\n\nexport interface Metadata {\n  /**\n   * An external identifier for the user who is associated with the request.\n   *\n   * This should be a uuid, hash value, or other opaque identifier. Anthropic may use\n   * this id to help detect abuse. Do not include any identifying information such as\n   * name, email address, or phone number.\n   */\n  user_id?: string | null;\n}\n\n/**\n * The model that will complete your prompt.\\n\\nSee\n * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n * details and options.\n */\nexport type Model =\n  | 'claude-3-7-sonnet-latest'\n  | 'claude-3-7-sonnet-20250219'\n  | 'claude-3-5-haiku-latest'\n  | 'claude-3-5-haiku-20241022'\n  | 'claude-sonnet-4-20250514'\n  | 'claude-sonnet-4-0'\n  | 'claude-4-sonnet-20250514'\n  | 'claude-3-5-sonnet-latest'\n  | 'claude-3-5-sonnet-20241022'\n  | 'claude-3-5-sonnet-20240620'\n  | 'claude-opus-4-0'\n  | 'claude-opus-4-20250514'\n  | 'claude-4-opus-20250514'\n  | 'claude-3-opus-latest'\n  | 'claude-3-opus-20240229'\n  | 'claude-3-sonnet-20240229'\n  | 'claude-3-haiku-20240307'\n  | 'claude-2.1'\n  | 'claude-2.0'\n  | (string & {});\n\nconst DEPRECATED_MODELS: {\n  [K in Model]?: string;\n} = {\n  'claude-1.3': 'November 6th, 2024',\n  'claude-1.3-100k': 'November 6th, 2024',\n  'claude-instant-1.1': 'November 6th, 2024',\n  'claude-instant-1.1-100k': 'November 6th, 2024',\n  'claude-instant-1.2': 'November 6th, 2024',\n  'claude-3-sonnet-20240229': 'July 21st, 2025',\n  'claude-2.1': 'July 21st, 2025',\n  'claude-2.0': 'July 21st, 2025',\n};\n\nexport interface PlainTextSource {\n  data: string;\n\n  media_type: 'text/plain';\n\n  type: 'text';\n}\n\nexport type RawContentBlockDelta =\n  | TextDelta\n  | InputJSONDelta\n  | CitationsDelta\n  | ThinkingDelta\n  | SignatureDelta;\n\nexport interface RawContentBlockDeltaEvent {\n  delta: RawContentBlockDelta;\n\n  index: number;\n\n  type: 'content_block_delta';\n}\n\nexport interface RawContentBlockStartEvent {\n  content_block:\n    | TextBlock\n    | ToolUseBlock\n    | ServerToolUseBlock\n    | WebSearchToolResultBlock\n    | ThinkingBlock\n    | RedactedThinkingBlock;\n\n  index: number;\n\n  type: 'content_block_start';\n}\n\nexport interface RawContentBlockStopEvent {\n  index: number;\n\n  type: 'content_block_stop';\n}\n\nexport interface RawMessageDeltaEvent {\n  delta: RawMessageDeltaEvent.Delta;\n\n  type: 'message_delta';\n\n  /**\n   * Billing and rate-limit usage.\n   *\n   * Anthropic's API bills and rate-limits by token counts, as tokens represent the\n   * underlying cost to our systems.\n   *\n   * Under the hood, the API transforms requests into a format suitable for the\n   * model. The model's output then goes through a parsing stage before becoming an\n   * API response. As a result, the token counts in `usage` will not match one-to-one\n   * with the exact visible content of an API request or response.\n   *\n   * For example, `output_tokens` will be non-zero, even for an empty string response\n   * from Claude.\n   *\n   * Total input tokens in a request is the summation of `input_tokens`,\n   * `cache_creation_input_tokens`, and `cache_read_input_tokens`.\n   */\n  usage: MessageDeltaUsage;\n}\n\nexport namespace RawMessageDeltaEvent {\n  export interface Delta {\n    stop_reason: MessagesAPI.StopReason | null;\n\n    stop_sequence: string | null;\n  }\n}\n\nexport interface RawMessageStartEvent {\n  message: Message;\n\n  type: 'message_start';\n}\n\nexport interface RawMessageStopEvent {\n  type: 'message_stop';\n}\n\nexport type RawMessageStreamEvent =\n  | RawMessageStartEvent\n  | RawMessageDeltaEvent\n  | RawMessageStopEvent\n  | RawContentBlockStartEvent\n  | RawContentBlockDeltaEvent\n  | RawContentBlockStopEvent;\n\nexport interface RedactedThinkingBlock {\n  data: string;\n\n  type: 'redacted_thinking';\n}\n\nexport interface RedactedThinkingBlockParam {\n  data: string;\n\n  type: 'redacted_thinking';\n}\n\nexport interface ServerToolUsage {\n  /**\n   * The number of web search tool requests.\n   */\n  web_search_requests: number;\n}\n\nexport interface ServerToolUseBlock {\n  id: string;\n\n  input: unknown;\n\n  name: 'web_search';\n\n  type: 'server_tool_use';\n}\n\nexport interface ServerToolUseBlockParam {\n  id: string;\n\n  input: unknown;\n\n  name: 'web_search';\n\n  type: 'server_tool_use';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n}\n\nexport interface SignatureDelta {\n  signature: string;\n\n  type: 'signature_delta';\n}\n\nexport type StopReason = 'end_turn' | 'max_tokens' | 'stop_sequence' | 'tool_use' | 'pause_turn' | 'refusal';\n\nexport interface TextBlock {\n  /**\n   * Citations supporting the text block.\n   *\n   * The type of citation returned will depend on the type of document being cited.\n   * Citing a PDF results in `page_location`, plain text results in `char_location`,\n   * and content document results in `content_block_location`.\n   */\n  citations: Array<TextCitation> | null;\n\n  text: string;\n\n  type: 'text';\n}\n\nexport interface TextBlockParam {\n  text: string;\n\n  type: 'text';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n\n  citations?: Array<TextCitationParam> | null;\n}\n\nexport type TextCitation =\n  | CitationCharLocation\n  | CitationPageLocation\n  | CitationContentBlockLocation\n  | CitationsWebSearchResultLocation;\n\nexport type TextCitationParam =\n  | CitationCharLocationParam\n  | CitationPageLocationParam\n  | CitationContentBlockLocationParam\n  | CitationWebSearchResultLocationParam;\n\nexport interface TextDelta {\n  text: string;\n\n  type: 'text_delta';\n}\n\nexport interface ThinkingBlock {\n  signature: string;\n\n  thinking: string;\n\n  type: 'thinking';\n}\n\nexport interface ThinkingBlockParam {\n  signature: string;\n\n  thinking: string;\n\n  type: 'thinking';\n}\n\nexport interface ThinkingConfigDisabled {\n  type: 'disabled';\n}\n\nexport interface ThinkingConfigEnabled {\n  /**\n   * Determines how many tokens Claude can use for its internal reasoning process.\n   * Larger budgets can enable more thorough analysis for complex problems, improving\n   * response quality.\n   *\n   * Must be ≥1024 and less than `max_tokens`.\n   *\n   * See\n   * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n   * for details.\n   */\n  budget_tokens: number;\n\n  type: 'enabled';\n}\n\n/**\n * Configuration for enabling Claude's extended thinking.\n *\n * When enabled, responses include `thinking` content blocks showing Claude's\n * thinking process before the final answer. Requires a minimum budget of 1,024\n * tokens and counts towards your `max_tokens` limit.\n *\n * See\n * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n * for details.\n */\nexport type ThinkingConfigParam = ThinkingConfigEnabled | ThinkingConfigDisabled;\n\nexport interface ThinkingDelta {\n  thinking: string;\n\n  type: 'thinking_delta';\n}\n\nexport interface Tool {\n  /**\n   * [JSON schema](https://json-schema.org/draft/2020-12) for this tool's input.\n   *\n   * This defines the shape of the `input` that your tool accepts and that the model\n   * will produce.\n   */\n  input_schema: Tool.InputSchema;\n\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: string;\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n\n  /**\n   * Description of what this tool does.\n   *\n   * Tool descriptions should be as detailed as possible. The more information that\n   * the model has about what the tool is and how to use it, the better it will\n   * perform. You can use natural language descriptions to reinforce important\n   * aspects of the tool input JSON schema.\n   */\n  description?: string;\n\n  type?: 'custom' | null;\n}\n\nexport namespace Tool {\n  /**\n   * [JSON schema](https://json-schema.org/draft/2020-12) for this tool's input.\n   *\n   * This defines the shape of the `input` that your tool accepts and that the model\n   * will produce.\n   */\n  export interface InputSchema {\n    type: 'object';\n\n    properties?: unknown | null;\n\n    [k: string]: unknown;\n  }\n}\n\nexport interface ToolBash20250124 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'bash';\n\n  type: 'bash_20250124';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n}\n\n/**\n * How the model should use the provided tools. The model can use a specific tool,\n * any available tool, decide by itself, or not use tools at all.\n */\nexport type ToolChoice = ToolChoiceAuto | ToolChoiceAny | ToolChoiceTool | ToolChoiceNone;\n\n/**\n * The model will use any available tools.\n */\nexport interface ToolChoiceAny {\n  type: 'any';\n\n  /**\n   * Whether to disable parallel tool use.\n   *\n   * Defaults to `false`. If set to `true`, the model will output exactly one tool\n   * use.\n   */\n  disable_parallel_tool_use?: boolean;\n}\n\n/**\n * The model will automatically decide whether to use tools.\n */\nexport interface ToolChoiceAuto {\n  type: 'auto';\n\n  /**\n   * Whether to disable parallel tool use.\n   *\n   * Defaults to `false`. If set to `true`, the model will output at most one tool\n   * use.\n   */\n  disable_parallel_tool_use?: boolean;\n}\n\n/**\n * The model will not be allowed to use tools.\n */\nexport interface ToolChoiceNone {\n  type: 'none';\n}\n\n/**\n * The model will use the specified tool with `tool_choice.name`.\n */\nexport interface ToolChoiceTool {\n  /**\n   * The name of the tool to use.\n   */\n  name: string;\n\n  type: 'tool';\n\n  /**\n   * Whether to disable parallel tool use.\n   *\n   * Defaults to `false`. If set to `true`, the model will output exactly one tool\n   * use.\n   */\n  disable_parallel_tool_use?: boolean;\n}\n\nexport interface ToolResultBlockParam {\n  tool_use_id: string;\n\n  type: 'tool_result';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n\n  content?: string | Array<TextBlockParam | ImageBlockParam>;\n\n  is_error?: boolean;\n}\n\nexport interface ToolTextEditor20250124 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'str_replace_editor';\n\n  type: 'text_editor_20250124';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n}\n\nexport type ToolUnion = Tool | ToolBash20250124 | ToolTextEditor20250124 | WebSearchTool20250305;\n\nexport interface ToolUseBlock {\n  id: string;\n\n  input: unknown;\n\n  name: string;\n\n  type: 'tool_use';\n}\n\nexport interface ToolUseBlockParam {\n  id: string;\n\n  input: unknown;\n\n  name: string;\n\n  type: 'tool_use';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n}\n\nexport interface URLImageSource {\n  type: 'url';\n\n  url: string;\n}\n\nexport interface URLPDFSource {\n  type: 'url';\n\n  url: string;\n}\n\nexport interface Usage {\n  /**\n   * The number of input tokens used to create the cache entry.\n   */\n  cache_creation_input_tokens: number | null;\n\n  /**\n   * The number of input tokens read from the cache.\n   */\n  cache_read_input_tokens: number | null;\n\n  /**\n   * The number of input tokens which were used.\n   */\n  input_tokens: number;\n\n  /**\n   * The number of output tokens which were used.\n   */\n  output_tokens: number;\n\n  /**\n   * The number of server tool requests.\n   */\n  server_tool_use: ServerToolUsage | null;\n\n  /**\n   * If the request used the priority, standard, or batch tier.\n   */\n  service_tier: 'standard' | 'priority' | 'batch' | null;\n}\n\nexport interface WebSearchResultBlock {\n  encrypted_content: string;\n\n  page_age: string | null;\n\n  title: string;\n\n  type: 'web_search_result';\n\n  url: string;\n}\n\nexport interface WebSearchResultBlockParam {\n  encrypted_content: string;\n\n  title: string;\n\n  type: 'web_search_result';\n\n  url: string;\n\n  page_age?: string | null;\n}\n\nexport interface WebSearchTool20250305 {\n  /**\n   * Name of the tool.\n   *\n   * This is how the tool will be called by the model and in `tool_use` blocks.\n   */\n  name: 'web_search';\n\n  type: 'web_search_20250305';\n\n  /**\n   * If provided, only these domains will be included in results. Cannot be used\n   * alongside `blocked_domains`.\n   */\n  allowed_domains?: Array<string> | null;\n\n  /**\n   * If provided, these domains will never appear in results. Cannot be used\n   * alongside `allowed_domains`.\n   */\n  blocked_domains?: Array<string> | null;\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n\n  /**\n   * Maximum number of times the tool can be used in the API request.\n   */\n  max_uses?: number | null;\n\n  /**\n   * Parameters for the user's location. Used to provide more relevant search\n   * results.\n   */\n  user_location?: WebSearchTool20250305.UserLocation | null;\n}\n\nexport namespace WebSearchTool20250305 {\n  /**\n   * Parameters for the user's location. Used to provide more relevant search\n   * results.\n   */\n  export interface UserLocation {\n    type: 'approximate';\n\n    /**\n     * The city of the user.\n     */\n    city?: string | null;\n\n    /**\n     * The two letter\n     * [ISO country code](https://en.wikipedia.org/wiki/ISO_3166-1_alpha-2) of the\n     * user.\n     */\n    country?: string | null;\n\n    /**\n     * The region of the user.\n     */\n    region?: string | null;\n\n    /**\n     * The [IANA timezone](https://nodatime.org/TimeZones) of the user.\n     */\n    timezone?: string | null;\n  }\n}\n\nexport interface WebSearchToolRequestError {\n  error_code:\n    | 'invalid_tool_input'\n    | 'unavailable'\n    | 'max_uses_exceeded'\n    | 'too_many_requests'\n    | 'query_too_long';\n\n  type: 'web_search_tool_result_error';\n}\n\nexport interface WebSearchToolResultBlock {\n  content: WebSearchToolResultBlockContent;\n\n  tool_use_id: string;\n\n  type: 'web_search_tool_result';\n}\n\nexport type WebSearchToolResultBlockContent = WebSearchToolResultError | Array<WebSearchResultBlock>;\n\nexport interface WebSearchToolResultBlockParam {\n  content: WebSearchToolResultBlockParamContent;\n\n  tool_use_id: string;\n\n  type: 'web_search_tool_result';\n\n  /**\n   * Create a cache control breakpoint at this content block.\n   */\n  cache_control?: CacheControlEphemeral | null;\n}\n\nexport type WebSearchToolResultBlockParamContent =\n  | Array<WebSearchResultBlockParam>\n  | WebSearchToolRequestError;\n\nexport interface WebSearchToolResultError {\n  error_code:\n    | 'invalid_tool_input'\n    | 'unavailable'\n    | 'max_uses_exceeded'\n    | 'too_many_requests'\n    | 'query_too_long';\n\n  type: 'web_search_tool_result_error';\n}\n\nexport type MessageStreamEvent = RawMessageStreamEvent;\n\nexport type MessageStartEvent = RawMessageStartEvent;\n\nexport type MessageDeltaEvent = RawMessageDeltaEvent;\n\nexport type MessageStopEvent = RawMessageStopEvent;\n\nexport type ContentBlockStartEvent = RawContentBlockStartEvent;\n\nexport type ContentBlockDeltaEvent = RawContentBlockDeltaEvent;\n\nexport type ContentBlockStopEvent = RawContentBlockStopEvent;\n\nexport type MessageCreateParams = MessageCreateParamsNonStreaming | MessageCreateParamsStreaming;\n\nexport interface MessageCreateParamsBase {\n  /**\n   * The maximum number of tokens to generate before stopping.\n   *\n   * Note that our models may stop _before_ reaching this maximum. This parameter\n   * only specifies the absolute maximum number of tokens to generate.\n   *\n   * Different models have different maximum values for this parameter. See\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for details.\n   */\n  max_tokens: number;\n\n  /**\n   * Input messages.\n   *\n   * Our models are trained to operate on alternating `user` and `assistant`\n   * conversational turns. When creating a new `Message`, you specify the prior\n   * conversational turns with the `messages` parameter, and the model then generates\n   * the next `Message` in the conversation. Consecutive `user` or `assistant` turns\n   * in your request will be combined into a single turn.\n   *\n   * Each input message must be an object with a `role` and `content`. You can\n   * specify a single `user`-role message, or you can include multiple `user` and\n   * `assistant` messages.\n   *\n   * If the final message uses the `assistant` role, the response content will\n   * continue immediately from the content in that message. This can be used to\n   * constrain part of the model's response.\n   *\n   * Example with a single `user` message:\n   *\n   * ```json\n   * [{ \"role\": \"user\", \"content\": \"Hello, Claude\" }]\n   * ```\n   *\n   * Example with multiple conversational turns:\n   *\n   * ```json\n   * [\n   *   { \"role\": \"user\", \"content\": \"Hello there.\" },\n   *   { \"role\": \"assistant\", \"content\": \"Hi, I'm Claude. How can I help you?\" },\n   *   { \"role\": \"user\", \"content\": \"Can you explain LLMs in plain English?\" }\n   * ]\n   * ```\n   *\n   * Example with a partially-filled response from Claude:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Each input message `content` may be either a single `string` or an array of\n   * content blocks, where each block has a specific `type`. Using a `string` for\n   * `content` is shorthand for an array of one content block of type `\"text\"`. The\n   * following input messages are equivalent:\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": \"Hello, Claude\" }\n   * ```\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": [{ \"type\": \"text\", \"text\": \"Hello, Claude\" }] }\n   * ```\n   *\n   * Starting with Claude 3 models, you can also send image content blocks:\n   *\n   * ```json\n   * {\n   *   \"role\": \"user\",\n   *   \"content\": [\n   *     {\n   *       \"type\": \"image\",\n   *       \"source\": {\n   *         \"type\": \"base64\",\n   *         \"media_type\": \"image/jpeg\",\n   *         \"data\": \"/9j/4AAQSkZJRg...\"\n   *       }\n   *     },\n   *     { \"type\": \"text\", \"text\": \"What is in this image?\" }\n   *   ]\n   * }\n   * ```\n   *\n   * We currently support the `base64` source type for images, and the `image/jpeg`,\n   * `image/png`, `image/gif`, and `image/webp` media types.\n   *\n   * See [examples](https://docs.anthropic.com/en/api/messages-examples#vision) for\n   * more input examples.\n   *\n   * Note that if you want to include a\n   * [system prompt](https://docs.anthropic.com/en/docs/system-prompts), you can use\n   * the top-level `system` parameter — there is no `\"system\"` role for input\n   * messages in the Messages API.\n   *\n   * There is a limit of 100000 messages in a single request.\n   */\n  messages: Array<MessageParam>;\n\n  /**\n   * The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: Model;\n\n  /**\n   * An object describing metadata about the request.\n   */\n  metadata?: Metadata;\n\n  /**\n   * Determines whether to use priority capacity (if available) or standard capacity\n   * for this request.\n   *\n   * Anthropic offers different levels of service for your API requests. See\n   * [service-tiers](https://docs.anthropic.com/en/api/service-tiers) for details.\n   */\n  service_tier?: 'auto' | 'standard_only';\n\n  /**\n   * Custom text sequences that will cause the model to stop generating.\n   *\n   * Our models will normally stop when they have naturally completed their turn,\n   * which will result in a response `stop_reason` of `\"end_turn\"`.\n   *\n   * If you want the model to stop generating when it encounters custom strings of\n   * text, you can use the `stop_sequences` parameter. If the model encounters one of\n   * the custom sequences, the response `stop_reason` value will be `\"stop_sequence\"`\n   * and the response `stop_sequence` value will contain the matched stop sequence.\n   */\n  stop_sequences?: Array<string>;\n\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/messages-streaming) for\n   * details.\n   */\n  stream?: boolean;\n\n  /**\n   * System prompt.\n   *\n   * A system prompt is a way of providing context and instructions to Claude, such\n   * as specifying a particular goal or role. See our\n   * [guide to system prompts](https://docs.anthropic.com/en/docs/system-prompts).\n   */\n  system?: string | Array<TextBlockParam>;\n\n  /**\n   * Amount of randomness injected into the response.\n   *\n   * Defaults to `1.0`. Ranges from `0.0` to `1.0`. Use `temperature` closer to `0.0`\n   * for analytical / multiple choice, and closer to `1.0` for creative and\n   * generative tasks.\n   *\n   * Note that even with `temperature` of `0.0`, the results will not be fully\n   * deterministic.\n   */\n  temperature?: number;\n\n  /**\n   * Configuration for enabling Claude's extended thinking.\n   *\n   * When enabled, responses include `thinking` content blocks showing Claude's\n   * thinking process before the final answer. Requires a minimum budget of 1,024\n   * tokens and counts towards your `max_tokens` limit.\n   *\n   * See\n   * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n   * for details.\n   */\n  thinking?: ThinkingConfigParam;\n\n  /**\n   * How the model should use the provided tools. The model can use a specific tool,\n   * any available tool, decide by itself, or not use tools at all.\n   */\n  tool_choice?: ToolChoice;\n\n  /**\n   * Definitions of tools that the model may use.\n   *\n   * If you include `tools` in your API request, the model may return `tool_use`\n   * content blocks that represent the model's use of those tools. You can then run\n   * those tools using the tool input generated by the model and then optionally\n   * return results back to the model using `tool_result` content blocks.\n   *\n   * Each tool definition includes:\n   *\n   * - `name`: Name of the tool.\n   * - `description`: Optional, but strongly-recommended description of the tool.\n   * - `input_schema`: [JSON schema](https://json-schema.org/draft/2020-12) for the\n   *   tool `input` shape that the model will produce in `tool_use` output content\n   *   blocks.\n   *\n   * For example, if you defined `tools` as:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"name\": \"get_stock_price\",\n   *     \"description\": \"Get the current stock price for a given ticker symbol.\",\n   *     \"input_schema\": {\n   *       \"type\": \"object\",\n   *       \"properties\": {\n   *         \"ticker\": {\n   *           \"type\": \"string\",\n   *           \"description\": \"The stock ticker symbol, e.g. AAPL for Apple Inc.\"\n   *         }\n   *       },\n   *       \"required\": [\"ticker\"]\n   *     }\n   *   }\n   * ]\n   * ```\n   *\n   * And then asked the model \"What's the S&P 500 at today?\", the model might produce\n   * `tool_use` content blocks in the response like this:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_use\",\n   *     \"id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"name\": \"get_stock_price\",\n   *     \"input\": { \"ticker\": \"^GSPC\" }\n   *   }\n   * ]\n   * ```\n   *\n   * You might then run your `get_stock_price` tool with `{\"ticker\": \"^GSPC\"}` as an\n   * input, and return the following back to the model in a subsequent `user`\n   * message:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_result\",\n   *     \"tool_use_id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"content\": \"259.75 USD\"\n   *   }\n   * ]\n   * ```\n   *\n   * Tools can be used for workflows that include running client-side tools and\n   * functions, or more generally whenever you want the model to produce a particular\n   * JSON structure of output.\n   *\n   * See our [guide](https://docs.anthropic.com/en/docs/tool-use) for more details.\n   */\n  tools?: Array<ToolUnion>;\n\n  /**\n   * Only sample from the top K options for each subsequent token.\n   *\n   * Used to remove \"long tail\" low probability responses.\n   * [Learn more technical details here](https://towardsdatascience.com/how-to-sample-from-language-models-682bceb97277).\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_k?: number;\n\n  /**\n   * Use nucleus sampling.\n   *\n   * In nucleus sampling, we compute the cumulative distribution over all the options\n   * for each subsequent token in decreasing probability order and cut it off once it\n   * reaches a particular probability specified by `top_p`. You should either alter\n   * `temperature` or `top_p`, but not both.\n   *\n   * Recommended for advanced use cases only. You usually only need to use\n   * `temperature`.\n   */\n  top_p?: number;\n}\n\nexport namespace MessageCreateParams {\n  export type MessageCreateParamsNonStreaming = MessagesAPI.MessageCreateParamsNonStreaming;\n  export type MessageCreateParamsStreaming = MessagesAPI.MessageCreateParamsStreaming;\n}\n\nexport interface MessageCreateParamsNonStreaming extends MessageCreateParamsBase {\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/messages-streaming) for\n   * details.\n   */\n  stream?: false;\n}\n\nexport interface MessageCreateParamsStreaming extends MessageCreateParamsBase {\n  /**\n   * Whether to incrementally stream the response using server-sent events.\n   *\n   * See [streaming](https://docs.anthropic.com/en/api/messages-streaming) for\n   * details.\n   */\n  stream: true;\n}\n\nexport type MessageStreamParams = MessageCreateParamsBase;\n\nexport interface MessageCountTokensParams {\n  /**\n   * Input messages.\n   *\n   * Our models are trained to operate on alternating `user` and `assistant`\n   * conversational turns. When creating a new `Message`, you specify the prior\n   * conversational turns with the `messages` parameter, and the model then generates\n   * the next `Message` in the conversation. Consecutive `user` or `assistant` turns\n   * in your request will be combined into a single turn.\n   *\n   * Each input message must be an object with a `role` and `content`. You can\n   * specify a single `user`-role message, or you can include multiple `user` and\n   * `assistant` messages.\n   *\n   * If the final message uses the `assistant` role, the response content will\n   * continue immediately from the content in that message. This can be used to\n   * constrain part of the model's response.\n   *\n   * Example with a single `user` message:\n   *\n   * ```json\n   * [{ \"role\": \"user\", \"content\": \"Hello, Claude\" }]\n   * ```\n   *\n   * Example with multiple conversational turns:\n   *\n   * ```json\n   * [\n   *   { \"role\": \"user\", \"content\": \"Hello there.\" },\n   *   { \"role\": \"assistant\", \"content\": \"Hi, I'm Claude. How can I help you?\" },\n   *   { \"role\": \"user\", \"content\": \"Can you explain LLMs in plain English?\" }\n   * ]\n   * ```\n   *\n   * Example with a partially-filled response from Claude:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"role\": \"user\",\n   *     \"content\": \"What's the Greek name for Sun? (A) Sol (B) Helios (C) Sun\"\n   *   },\n   *   { \"role\": \"assistant\", \"content\": \"The best answer is (\" }\n   * ]\n   * ```\n   *\n   * Each input message `content` may be either a single `string` or an array of\n   * content blocks, where each block has a specific `type`. Using a `string` for\n   * `content` is shorthand for an array of one content block of type `\"text\"`. The\n   * following input messages are equivalent:\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": \"Hello, Claude\" }\n   * ```\n   *\n   * ```json\n   * { \"role\": \"user\", \"content\": [{ \"type\": \"text\", \"text\": \"Hello, Claude\" }] }\n   * ```\n   *\n   * Starting with Claude 3 models, you can also send image content blocks:\n   *\n   * ```json\n   * {\n   *   \"role\": \"user\",\n   *   \"content\": [\n   *     {\n   *       \"type\": \"image\",\n   *       \"source\": {\n   *         \"type\": \"base64\",\n   *         \"media_type\": \"image/jpeg\",\n   *         \"data\": \"/9j/4AAQSkZJRg...\"\n   *       }\n   *     },\n   *     { \"type\": \"text\", \"text\": \"What is in this image?\" }\n   *   ]\n   * }\n   * ```\n   *\n   * We currently support the `base64` source type for images, and the `image/jpeg`,\n   * `image/png`, `image/gif`, and `image/webp` media types.\n   *\n   * See [examples](https://docs.anthropic.com/en/api/messages-examples#vision) for\n   * more input examples.\n   *\n   * Note that if you want to include a\n   * [system prompt](https://docs.anthropic.com/en/docs/system-prompts), you can use\n   * the top-level `system` parameter — there is no `\"system\"` role for input\n   * messages in the Messages API.\n   *\n   * There is a limit of 100000 messages in a single request.\n   */\n  messages: Array<MessageParam>;\n\n  /**\n   * The model that will complete your prompt.\\n\\nSee\n   * [models](https://docs.anthropic.com/en/docs/models-overview) for additional\n   * details and options.\n   */\n  model: Model;\n\n  /**\n   * System prompt.\n   *\n   * A system prompt is a way of providing context and instructions to Claude, such\n   * as specifying a particular goal or role. See our\n   * [guide to system prompts](https://docs.anthropic.com/en/docs/system-prompts).\n   */\n  system?: string | Array<TextBlockParam>;\n\n  /**\n   * Configuration for enabling Claude's extended thinking.\n   *\n   * When enabled, responses include `thinking` content blocks showing Claude's\n   * thinking process before the final answer. Requires a minimum budget of 1,024\n   * tokens and counts towards your `max_tokens` limit.\n   *\n   * See\n   * [extended thinking](https://docs.anthropic.com/en/docs/build-with-claude/extended-thinking)\n   * for details.\n   */\n  thinking?: ThinkingConfigParam;\n\n  /**\n   * How the model should use the provided tools. The model can use a specific tool,\n   * any available tool, decide by itself, or not use tools at all.\n   */\n  tool_choice?: ToolChoice;\n\n  /**\n   * Definitions of tools that the model may use.\n   *\n   * If you include `tools` in your API request, the model may return `tool_use`\n   * content blocks that represent the model's use of those tools. You can then run\n   * those tools using the tool input generated by the model and then optionally\n   * return results back to the model using `tool_result` content blocks.\n   *\n   * Each tool definition includes:\n   *\n   * - `name`: Name of the tool.\n   * - `description`: Optional, but strongly-recommended description of the tool.\n   * - `input_schema`: [JSON schema](https://json-schema.org/draft/2020-12) for the\n   *   tool `input` shape that the model will produce in `tool_use` output content\n   *   blocks.\n   *\n   * For example, if you defined `tools` as:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"name\": \"get_stock_price\",\n   *     \"description\": \"Get the current stock price for a given ticker symbol.\",\n   *     \"input_schema\": {\n   *       \"type\": \"object\",\n   *       \"properties\": {\n   *         \"ticker\": {\n   *           \"type\": \"string\",\n   *           \"description\": \"The stock ticker symbol, e.g. AAPL for Apple Inc.\"\n   *         }\n   *       },\n   *       \"required\": [\"ticker\"]\n   *     }\n   *   }\n   * ]\n   * ```\n   *\n   * And then asked the model \"What's the S&P 500 at today?\", the model might produce\n   * `tool_use` content blocks in the response like this:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_use\",\n   *     \"id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"name\": \"get_stock_price\",\n   *     \"input\": { \"ticker\": \"^GSPC\" }\n   *   }\n   * ]\n   * ```\n   *\n   * You might then run your `get_stock_price` tool with `{\"ticker\": \"^GSPC\"}` as an\n   * input, and return the following back to the model in a subsequent `user`\n   * message:\n   *\n   * ```json\n   * [\n   *   {\n   *     \"type\": \"tool_result\",\n   *     \"tool_use_id\": \"toolu_01D7FLrfh4GYq7yT1ULFeyMV\",\n   *     \"content\": \"259.75 USD\"\n   *   }\n   * ]\n   * ```\n   *\n   * Tools can be used for workflows that include running client-side tools and\n   * functions, or more generally whenever you want the model to produce a particular\n   * JSON structure of output.\n   *\n   * See our [guide](https://docs.anthropic.com/en/docs/tool-use) for more details.\n   */\n  tools?: Array<MessageCountTokensTool>;\n}\n\nMessages.Batches = Batches;\n\nexport declare namespace Messages {\n  export {\n    type Base64ImageSource as Base64ImageSource,\n    type Base64PDFSource as Base64PDFSource,\n    type CacheControlEphemeral as CacheControlEphemeral,\n    type CitationCharLocation as CitationCharLocation,\n    type CitationCharLocationParam as CitationCharLocationParam,\n    type CitationContentBlockLocation as CitationContentBlockLocation,\n    type CitationContentBlockLocationParam as CitationContentBlockLocationParam,\n    type CitationPageLocation as CitationPageLocation,\n    type CitationPageLocationParam as CitationPageLocationParam,\n    type CitationWebSearchResultLocationParam as CitationWebSearchResultLocationParam,\n    type CitationsConfigParam as CitationsConfigParam,\n    type CitationsDelta as CitationsDelta,\n    type CitationsWebSearchResultLocation as CitationsWebSearchResultLocation,\n    type ContentBlock as ContentBlock,\n    type ContentBlockParam as ContentBlockParam,\n    type ContentBlockStartEvent as ContentBlockStartEvent,\n    type ContentBlockStopEvent as ContentBlockStopEvent,\n    type ContentBlockSource as ContentBlockSource,\n    type ContentBlockSourceContent as ContentBlockSourceContent,\n    type DocumentBlockParam as DocumentBlockParam,\n    type ImageBlockParam as ImageBlockParam,\n    type InputJSONDelta as InputJSONDelta,\n    type Message as Message,\n    type MessageCountTokensTool as MessageCountTokensTool,\n    type MessageDeltaEvent as MessageDeltaEvent,\n    type MessageDeltaUsage as MessageDeltaUsage,\n    type MessageParam as MessageParam,\n    type MessageTokensCount as MessageTokensCount,\n    type Metadata as Metadata,\n    type Model as Model,\n    type PlainTextSource as PlainTextSource,\n    type RawContentBlockDelta as RawContentBlockDelta,\n    type RawContentBlockDeltaEvent as RawContentBlockDeltaEvent,\n    type RawContentBlockStartEvent as RawContentBlockStartEvent,\n    type RawContentBlockStopEvent as RawContentBlockStopEvent,\n    type RawMessageDeltaEvent as RawMessageDeltaEvent,\n    type RawMessageStartEvent as RawMessageStartEvent,\n    type RawMessageStopEvent as RawMessageStopEvent,\n    type RawMessageStreamEvent as RawMessageStreamEvent,\n    type RedactedThinkingBlock as RedactedThinkingBlock,\n    type RedactedThinkingBlockParam as RedactedThinkingBlockParam,\n    type ServerToolUsage as ServerToolUsage,\n    type ServerToolUseBlock as ServerToolUseBlock,\n    type ServerToolUseBlockParam as ServerToolUseBlockParam,\n    type SignatureDelta as SignatureDelta,\n    type StopReason as StopReason,\n    type TextBlock as TextBlock,\n    type TextBlockParam as TextBlockParam,\n    type TextCitation as TextCitation,\n    type TextCitationParam as TextCitationParam,\n    type TextDelta as TextDelta,\n    type ThinkingBlock as ThinkingBlock,\n    type ThinkingBlockParam as ThinkingBlockParam,\n    type ThinkingConfigDisabled as ThinkingConfigDisabled,\n    type ThinkingConfigEnabled as ThinkingConfigEnabled,\n    type ThinkingConfigParam as ThinkingConfigParam,\n    type ThinkingDelta as ThinkingDelta,\n    type Tool as Tool,\n    type ToolBash20250124 as ToolBash20250124,\n    type ToolChoice as ToolChoice,\n    type ToolChoiceAny as ToolChoiceAny,\n    type ToolChoiceAuto as ToolChoiceAuto,\n    type ToolChoiceNone as ToolChoiceNone,\n    type ToolChoiceTool as ToolChoiceTool,\n    type ToolResultBlockParam as ToolResultBlockParam,\n    type ToolTextEditor20250124 as ToolTextEditor20250124,\n    type ToolUnion as ToolUnion,\n    type ToolUseBlock as ToolUseBlock,\n    type ToolUseBlockParam as ToolUseBlockParam,\n    type URLImageSource as URLImageSource,\n    type URLPDFSource as URLPDFSource,\n    type Usage as Usage,\n    type WebSearchResultBlock as WebSearchResultBlock,\n    type WebSearchResultBlockParam as WebSearchResultBlockParam,\n    type WebSearchTool20250305 as WebSearchTool20250305,\n    type WebSearchToolRequestError as WebSearchToolRequestError,\n    type WebSearchToolResultBlock as WebSearchToolResultBlock,\n    type WebSearchToolResultBlockContent as WebSearchToolResultBlockContent,\n    type WebSearchToolResultBlockParam as WebSearchToolResultBlockParam,\n    type WebSearchToolResultBlockParamContent as WebSearchToolResultBlockParamContent,\n    type WebSearchToolResultError as WebSearchToolResultError,\n    type MessageStreamEvent as MessageStreamEvent,\n    type MessageStartEvent as MessageStartEvent,\n    type MessageStopEvent as MessageStopEvent,\n    type ContentBlockDeltaEvent as ContentBlockDeltaEvent,\n    type MessageCreateParams as MessageCreateParams,\n    type MessageCreateParamsNonStreaming as MessageCreateParamsNonStreaming,\n    type MessageCreateParamsStreaming as MessageCreateParamsStreaming,\n    type MessageStreamParams as MessageStreamParams,\n    type MessageCountTokensParams as MessageCountTokensParams,\n  };\n\n  export {\n    Batches as Batches,\n    type DeletedMessageBatch as DeletedMessageBatch,\n    type MessageBatch as MessageBatch,\n    type MessageBatchCanceledResult as MessageBatchCanceledResult,\n    type MessageBatchErroredResult as MessageBatchErroredResult,\n    type MessageBatchExpiredResult as MessageBatchExpiredResult,\n    type MessageBatchIndividualResponse as MessageBatchIndividualResponse,\n    type MessageBatchRequestCounts as MessageBatchRequestCounts,\n    type MessageBatchResult as MessageBatchResult,\n    type MessageBatchSucceededResult as MessageBatchSucceededResult,\n    type MessageBatchesPage as MessageBatchesPage,\n    type BatchCreateParams as BatchCreateParams,\n    type BatchListParams as BatchListParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport { APIResource } from '../core/resource';\nimport * as BetaAPI from './beta/beta';\nimport { APIPromise } from '../core/api-promise';\nimport { Page, type PageParams, PagePromise } from '../core/pagination';\nimport { buildHeaders } from '../internal/headers';\nimport { RequestOptions } from '../internal/request-options';\nimport { path } from '../internal/utils/path';\n\nexport class Models extends APIResource {\n  /**\n   * Get a specific model.\n   *\n   * The Models API response can be used to determine information about a specific\n   * model or resolve a model alias to a model ID.\n   */\n  retrieve(\n    modelID: string,\n    params: ModelRetrieveParams | null | undefined = {},\n    options?: RequestOptions,\n  ): APIPromise<ModelInfo> {\n    const { betas } = params ?? {};\n    return this._client.get(path`/v1/models/${modelID}`, {\n      ...options,\n      headers: buildHeaders([\n        { ...(betas?.toString() != null ? { 'anthropic-beta': betas?.toString() } : undefined) },\n        options?.headers,\n      ]),\n    });\n  }\n\n  /**\n   * List available models.\n   *\n   * The Models API response can be used to determine which models are available for\n   * use in the API. More recently released models are listed first.\n   */\n  list(\n    params: ModelListParams | null | undefined = {},\n    options?: RequestOptions,\n  ): PagePromise<ModelInfosPage, ModelInfo> {\n    const { betas, ...query } = params ?? {};\n    return this._client.getAPIList('/v1/models', Page<ModelInfo>, {\n      query,\n      ...options,\n      headers: buildHeaders([\n        { ...(betas?.toString() != null ? { 'anthropic-beta': betas?.toString() } : undefined) },\n        options?.headers,\n      ]),\n    });\n  }\n}\n\nexport type ModelInfosPage = Page<ModelInfo>;\n\nexport interface ModelInfo {\n  /**\n   * Unique model identifier.\n   */\n  id: string;\n\n  /**\n   * RFC 3339 datetime string representing the time at which the model was released.\n   * May be set to an epoch value if the release date is unknown.\n   */\n  created_at: string;\n\n  /**\n   * A human-readable name for the model.\n   */\n  display_name: string;\n\n  /**\n   * Object type.\n   *\n   * For Models, this is always `\"model\"`.\n   */\n  type: 'model';\n}\n\nexport interface ModelRetrieveParams {\n  /**\n   * Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport interface ModelListParams extends PageParams {\n  /**\n   * Header param: Optional header to specify the beta version(s) you want to use.\n   */\n  betas?: Array<BetaAPI.AnthropicBeta>;\n}\n\nexport declare namespace Models {\n  export {\n    type ModelInfo as ModelInfo,\n    type ModelInfosPage as ModelInfosPage,\n    type ModelRetrieveParams as ModelRetrieveParams,\n    type ModelListParams as ModelListParams,\n  };\n}\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\n/**\n * Read an environment variable.\n *\n * Trims beginning and trailing whitespace.\n *\n * Will return undefined if the environment variable doesn't exist or cannot be accessed.\n */\nexport const readEnv = (env: string): string | undefined => {\n  if (typeof (globalThis as any).process !== 'undefined') {\n    return (globalThis as any).process.env?.[env]?.trim() ?? undefined;\n  }\n  if (typeof (globalThis as any).Deno !== 'undefined') {\n    return (globalThis as any).Deno.env?.get?.(env)?.trim();\n  }\n  return undefined;\n};\n", "// File generated from our OpenAPI spec by Stainless. See CONTRIBUTING.md for details.\n\nimport type { RequestInit, RequestInfo, BodyInit } from './internal/builtin-types';\nimport type { HTTPMethod, PromiseOrValue, MergedRequestInit, FinalizedRequestInit } from './internal/types';\nimport { uuid4 } from './internal/utils/uuid';\nimport { validatePositiveInteger, isAbsoluteURL, safeJSON } from './internal/utils/values';\nimport { sleep } from './internal/utils/sleep';\nimport { type Logger, type LogLevel, parseLogLevel } from './internal/utils/log';\nexport type { Logger, LogLevel } from './internal/utils/log';\nimport { castToError, isAbortError } from './internal/errors';\nimport type { APIResponseProps } from './internal/parse';\nimport { getPlatformHeaders } from './internal/detect-platform';\nimport * as Shims from './internal/shims';\nimport * as Opts from './internal/request-options';\nimport { VERSION } from './version';\nimport * as Errors from './core/error';\nimport * as Pagination from './core/pagination';\nimport { type PageParams, PageResponse } from './core/pagination';\nimport * as Uploads from './core/uploads';\nimport * as API from './resources/index';\nimport { APIPromise } from './core/api-promise';\nimport { type Fetch } from './internal/builtin-types';\nimport { isRunningInBrowser } from './internal/detect-platform';\nimport { HeadersLike, NullableHeaders, buildHeaders } from './internal/headers';\nimport { FinalRequestOptions, RequestOptions } from './internal/request-options';\nimport {\n  Completion,\n  CompletionCreateParams,\n  CompletionCreateParamsNonStreaming,\n  CompletionCreateParamsStreaming,\n  Completions,\n} from './resources/completions';\nimport { ModelInfo, ModelInfosPage, ModelListParams, ModelRetrieveParams, Models } from './resources/models';\nimport { readEnv } from './internal/utils/env';\nimport { formatRequestDetails, loggerFor } from './internal/utils/log';\nimport { isEmptyObj } from './internal/utils/values';\nimport {\n  AnthropicBeta,\n  Beta,\n  BetaAPIError,\n  BetaAuthenticationError,\n  BetaBillingError,\n  BetaError,\n  BetaErrorResponse,\n  BetaGatewayTimeoutError,\n  BetaInvalidRequestError,\n  BetaNotFoundError,\n  BetaOverloadedError,\n  BetaPermissionError,\n  BetaRateLimitError,\n} from './resources/beta/beta';\nimport {\n  Base64ImageSource,\n  Base64PDFSource,\n  CacheControlEphemeral,\n  CitationCharLocation,\n  CitationCharLocationParam,\n  CitationContentBlockLocation,\n  CitationContentBlockLocationParam,\n  CitationPageLocation,\n  CitationPageLocationParam,\n  CitationWebSearchResultLocationParam,\n  CitationsConfigParam,\n  CitationsDelta,\n  CitationsWebSearchResultLocation,\n  ContentBlock,\n  ContentBlockDeltaEvent,\n  ContentBlockParam,\n  ContentBlockStartEvent,\n  ContentBlockStopEvent,\n  ContentBlockSource,\n  ContentBlockSourceContent,\n  DocumentBlockParam,\n  ImageBlockParam,\n  InputJSONDelta,\n  Message,\n  MessageStreamParams,\n  MessageCountTokensParams,\n  MessageCountTokensTool,\n  MessageCreateParams,\n  MessageCreateParamsNonStreaming,\n  MessageCreateParamsStreaming,\n  MessageDeltaEvent,\n  MessageDeltaUsage,\n  MessageParam,\n  MessageStartEvent,\n  MessageStopEvent,\n  MessageStreamEvent,\n  MessageTokensCount,\n  Messages,\n  Metadata,\n  Model,\n  PlainTextSource,\n  RawContentBlockDelta,\n  RawContentBlockDeltaEvent,\n  RawContentBlockStartEvent,\n  RawContentBlockStopEvent,\n  RawMessageDeltaEvent,\n  RawMessageStartEvent,\n  RawMessageStopEvent,\n  RawMessageStreamEvent,\n  RedactedThinkingBlock,\n  RedactedThinkingBlockParam,\n  ServerToolUsage,\n  ServerToolUseBlock,\n  ServerToolUseBlockParam,\n  SignatureDelta,\n  StopReason,\n  TextBlock,\n  TextBlockParam,\n  TextCitation,\n  TextCitationParam,\n  TextDelta,\n  ThinkingBlock,\n  ThinkingBlockParam,\n  ThinkingConfigDisabled,\n  ThinkingConfigEnabled,\n  ThinkingConfigParam,\n  ThinkingDelta,\n  Tool,\n  ToolBash20250124,\n  ToolChoice,\n  ToolChoiceAny,\n  ToolChoiceAuto,\n  ToolChoiceNone,\n  ToolChoiceTool,\n  ToolResultBlockParam,\n  ToolTextEditor20250124,\n  ToolUnion,\n  ToolUseBlock,\n  ToolUseBlockParam,\n  URLImageSource,\n  URLPDFSource,\n  Usage,\n  WebSearchResultBlock,\n  WebSearchResultBlockParam,\n  WebSearchTool20250305,\n  WebSearchToolRequestError,\n  WebSearchToolResultBlock,\n  WebSearchToolResultBlockContent,\n  WebSearchToolResultBlockParam,\n  WebSearchToolResultBlockParamContent,\n  WebSearchToolResultError,\n} from './resources/messages/messages';\n\nexport interface ClientOptions {\n  /**\n   * Defaults to process.env['ANTHROPIC_API_KEY'].\n   */\n  apiKey?: string | null | undefined;\n\n  /**\n   * Defaults to process.env['ANTHROPIC_AUTH_TOKEN'].\n   */\n  authToken?: string | null | undefined;\n\n  /**\n   * Override the default base URL for the API, e.g., \"https://api.example.com/v2/\"\n   *\n   * Defaults to process.env['ANTHROPIC_BASE_URL'].\n   */\n  baseURL?: string | null | undefined;\n\n  /**\n   * The maximum amount of time (in milliseconds) that the client should wait for a response\n   * from the server before timing out a single request.\n   *\n   * Note that request timeouts are retried by default, so in a worst-case scenario you may wait\n   * much longer than this timeout before the promise succeeds or fails.\n   */\n  timeout?: number | undefined;\n  /**\n   * Additional `RequestInit` options to be passed to `fetch` calls.\n   * Properties will be overridden by per-request `fetchOptions`.\n   */\n  fetchOptions?: MergedRequestInit | undefined;\n\n  /**\n   * Specify a custom `fetch` function implementation.\n   *\n   * If not provided, we expect that `fetch` is defined globally.\n   */\n  fetch?: Fetch | undefined;\n\n  /**\n   * The maximum number of times that the client will retry a request in case of a\n   * temporary failure, like a network error or a 5XX error from the server.\n   *\n   * @default 2\n   */\n  maxRetries?: number | undefined;\n\n  /**\n   * Default headers to include with every request to the API.\n   *\n   * These can be removed in individual requests by explicitly setting the\n   * header to `null` in request options.\n   */\n  defaultHeaders?: HeadersLike | undefined;\n\n  /**\n   * Default query parameters to include with every request to the API.\n   *\n   * These can be removed in individual requests by explicitly setting the\n   * param to `undefined` in request options.\n   */\n  defaultQuery?: Record<string, string | undefined> | undefined;\n\n  /**\n   * By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n   * Only set this option to `true` if you understand the risks and have appropriate mitigations in place.\n   */\n  dangerouslyAllowBrowser?: boolean | undefined;\n\n  /**\n   * Set the log level.\n   *\n   * Defaults to process.env['ANTHROPIC_LOG'] or 'warn' if it isn't set.\n   */\n  logLevel?: LogLevel | undefined;\n\n  /**\n   * Set the logger.\n   *\n   * Defaults to globalThis.console.\n   */\n  logger?: Logger | undefined;\n}\n\nexport class BaseAnthropic {\n  apiKey: string | null;\n  authToken: string | null;\n\n  baseURL: string;\n  maxRetries: number;\n  timeout: number;\n  logger: Logger | undefined;\n  logLevel: LogLevel | undefined;\n  fetchOptions: MergedRequestInit | undefined;\n\n  private fetch: Fetch;\n  #encoder: Opts.RequestEncoder;\n  protected idempotencyHeader?: string;\n  private _options: ClientOptions;\n\n  /**\n   * API Client for interfacing with the Anthropic API.\n   *\n   * @param {string | null | undefined} [opts.apiKey=process.env['ANTHROPIC_API_KEY'] ?? null]\n   * @param {string | null | undefined} [opts.authToken=process.env['ANTHROPIC_AUTH_TOKEN'] ?? null]\n   * @param {string} [opts.baseURL=process.env['ANTHROPIC_BASE_URL'] ?? https://api.anthropic.com] - Override the default base URL for the API.\n   * @param {number} [opts.timeout=10 minutes] - The maximum amount of time (in milliseconds) the client will wait for a response before timing out.\n   * @param {MergedRequestInit} [opts.fetchOptions] - Additional `RequestInit` options to be passed to `fetch` calls.\n   * @param {Fetch} [opts.fetch] - Specify a custom `fetch` function implementation.\n   * @param {number} [opts.maxRetries=2] - The maximum number of times the client will retry a request.\n   * @param {HeadersLike} opts.defaultHeaders - Default headers to include with every request to the API.\n   * @param {Record<string, string | undefined>} opts.defaultQuery - Default query parameters to include with every request to the API.\n   * @param {boolean} [opts.dangerouslyAllowBrowser=false] - By default, client-side use of this library is not allowed, as it risks exposing your secret API credentials to attackers.\n   */\n  constructor({\n    baseURL = readEnv('ANTHROPIC_BASE_URL'),\n    apiKey = readEnv('ANTHROPIC_API_KEY') ?? null,\n    authToken = readEnv('ANTHROPIC_AUTH_TOKEN') ?? null,\n    ...opts\n  }: ClientOptions = {}) {\n    const options: ClientOptions = {\n      apiKey,\n      authToken,\n      ...opts,\n      baseURL: baseURL || `https://api.anthropic.com`,\n    };\n\n    if (!options.dangerouslyAllowBrowser && isRunningInBrowser()) {\n      throw new Errors.AnthropicError(\n        \"It looks like you're running in a browser-like environment.\\n\\nThis is disabled by default, as it risks exposing your secret API credentials to attackers.\\nIf you understand the risks and have appropriate mitigations in place,\\nyou can set the `dangerouslyAllowBrowser` option to `true`, e.g.,\\n\\nnew Anthropic({ apiKey, dangerouslyAllowBrowser: true });\\n\",\n      );\n    }\n\n    this.baseURL = options.baseURL!;\n    this.timeout = options.timeout ?? Anthropic.DEFAULT_TIMEOUT /* 10 minutes */;\n    this.logger = options.logger ?? console;\n    const defaultLogLevel = 'warn';\n    // Set default logLevel early so that we can log a warning in parseLogLevel.\n    this.logLevel = defaultLogLevel;\n    this.logLevel =\n      parseLogLevel(options.logLevel, 'ClientOptions.logLevel', this) ??\n      parseLogLevel(readEnv('ANTHROPIC_LOG'), \"process.env['ANTHROPIC_LOG']\", this) ??\n      defaultLogLevel;\n    this.fetchOptions = options.fetchOptions;\n    this.maxRetries = options.maxRetries ?? 2;\n    this.fetch = options.fetch ?? Shims.getDefaultFetch();\n    this.#encoder = Opts.FallbackEncoder;\n\n    this._options = options;\n\n    this.apiKey = apiKey;\n    this.authToken = authToken;\n  }\n\n  /**\n   * Create a new client instance re-using the same options given to the current client with optional overriding.\n   */\n  withOptions(options: Partial<ClientOptions>): this {\n    return new (this.constructor as any as new (props: ClientOptions) => typeof this)({\n      ...this._options,\n      baseURL: this.baseURL,\n      maxRetries: this.maxRetries,\n      timeout: this.timeout,\n      logger: this.logger,\n      logLevel: this.logLevel,\n      fetchOptions: this.fetchOptions,\n      apiKey: this.apiKey,\n      authToken: this.authToken,\n      ...options,\n    });\n  }\n\n  protected defaultQuery(): Record<string, string | undefined> | undefined {\n    return this._options.defaultQuery;\n  }\n\n  protected validateHeaders({ values, nulls }: NullableHeaders) {\n    if (this.apiKey && values.get('x-api-key')) {\n      return;\n    }\n    if (nulls.has('x-api-key')) {\n      return;\n    }\n\n    if (this.authToken && values.get('authorization')) {\n      return;\n    }\n    if (nulls.has('authorization')) {\n      return;\n    }\n\n    throw new Error(\n      'Could not resolve authentication method. Expected either apiKey or authToken to be set. Or for one of the \"X-Api-Key\" or \"Authorization\" headers to be explicitly omitted',\n    );\n  }\n\n  protected authHeaders(opts: FinalRequestOptions): NullableHeaders | undefined {\n    return buildHeaders([this.apiKeyAuth(opts), this.bearerAuth(opts)]);\n  }\n\n  protected apiKeyAuth(opts: FinalRequestOptions): NullableHeaders | undefined {\n    if (this.apiKey == null) {\n      return undefined;\n    }\n    return buildHeaders([{ 'X-Api-Key': this.apiKey }]);\n  }\n\n  protected bearerAuth(opts: FinalRequestOptions): NullableHeaders | undefined {\n    if (this.authToken == null) {\n      return undefined;\n    }\n    return buildHeaders([{ Authorization: `Bearer ${this.authToken}` }]);\n  }\n\n  /**\n   * Basic re-implementation of `qs.stringify` for primitive types.\n   */\n  protected stringifyQuery(query: Record<string, unknown>): string {\n    return Object.entries(query)\n      .filter(([_, value]) => typeof value !== 'undefined')\n      .map(([key, value]) => {\n        if (typeof value === 'string' || typeof value === 'number' || typeof value === 'boolean') {\n          return `${encodeURIComponent(key)}=${encodeURIComponent(value)}`;\n        }\n        if (value === null) {\n          return `${encodeURIComponent(key)}=`;\n        }\n        throw new Errors.AnthropicError(\n          `Cannot stringify type ${typeof value}; Expected string, number, boolean, or null. If you need to pass nested query parameters, you can manually encode them, e.g. { query: { 'foo[key1]': value1, 'foo[key2]': value2 } }, and please open a GitHub issue requesting better support for your use case.`,\n        );\n      })\n      .join('&');\n  }\n\n  private getUserAgent(): string {\n    return `${this.constructor.name}/JS ${VERSION}`;\n  }\n\n  protected defaultIdempotencyKey(): string {\n    return `stainless-node-retry-${uuid4()}`;\n  }\n\n  protected makeStatusError(\n    status: number,\n    error: Object,\n    message: string | undefined,\n    headers: Headers,\n  ): Errors.APIError {\n    return Errors.APIError.generate(status, error, message, headers);\n  }\n\n  buildURL(path: string, query: Record<string, unknown> | null | undefined): string {\n    const url =\n      isAbsoluteURL(path) ?\n        new URL(path)\n      : new URL(this.baseURL + (this.baseURL.endsWith('/') && path.startsWith('/') ? path.slice(1) : path));\n\n    const defaultQuery = this.defaultQuery();\n    if (!isEmptyObj(defaultQuery)) {\n      query = { ...defaultQuery, ...query };\n    }\n\n    if (typeof query === 'object' && query && !Array.isArray(query)) {\n      url.search = this.stringifyQuery(query as Record<string, unknown>);\n    }\n\n    return url.toString();\n  }\n\n  _calculateNonstreamingTimeout(maxTokens: number): number {\n    const defaultTimeout = 10 * 60;\n    const expectedTimeout = (60 * 60 * maxTokens) / 128_000;\n    if (expectedTimeout > defaultTimeout) {\n      throw new Errors.AnthropicError(\n        'Streaming is strongly recommended for operations that may take longer than 10 minutes. ' +\n          'See https://github.com/anthropics/anthropic-sdk-python#streaming-responses for more details',\n      );\n    }\n    return defaultTimeout * 1000;\n  }\n\n  /**\n   * Used as a callback for mutating the given `FinalRequestOptions` object.\n   */\n  protected async prepareOptions(options: FinalRequestOptions): Promise<void> {}\n\n  /**\n   * Used as a callback for mutating the given `RequestInit` object.\n   *\n   * This is useful for cases where you want to add certain headers based off of\n   * the request properties, e.g. `method` or `url`.\n   */\n  protected async prepareRequest(\n    request: RequestInit,\n    { url, options }: { url: string; options: FinalRequestOptions },\n  ): Promise<void> {}\n\n  get<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('get', path, opts);\n  }\n\n  post<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('post', path, opts);\n  }\n\n  patch<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('patch', path, opts);\n  }\n\n  put<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('put', path, opts);\n  }\n\n  delete<Rsp>(path: string, opts?: PromiseOrValue<RequestOptions>): APIPromise<Rsp> {\n    return this.methodRequest('delete', path, opts);\n  }\n\n  private methodRequest<Rsp>(\n    method: HTTPMethod,\n    path: string,\n    opts?: PromiseOrValue<RequestOptions>,\n  ): APIPromise<Rsp> {\n    return this.request(\n      Promise.resolve(opts).then((opts) => {\n        return { method, path, ...opts };\n      }),\n    );\n  }\n\n  request<Rsp>(\n    options: PromiseOrValue<FinalRequestOptions>,\n    remainingRetries: number | null = null,\n  ): APIPromise<Rsp> {\n    return new APIPromise(this, this.makeRequest(options, remainingRetries, undefined));\n  }\n\n  private async makeRequest(\n    optionsInput: PromiseOrValue<FinalRequestOptions>,\n    retriesRemaining: number | null,\n    retryOfRequestLogID: string | undefined,\n  ): Promise<APIResponseProps> {\n    const options = await optionsInput;\n    const maxRetries = options.maxRetries ?? this.maxRetries;\n    if (retriesRemaining == null) {\n      retriesRemaining = maxRetries;\n    }\n\n    await this.prepareOptions(options);\n\n    const { req, url, timeout } = this.buildRequest(options, { retryCount: maxRetries - retriesRemaining });\n\n    await this.prepareRequest(req, { url, options });\n\n    /** Not an API request ID, just for correlating local log entries. */\n    const requestLogID = 'log_' + ((Math.random() * (1 << 24)) | 0).toString(16).padStart(6, '0');\n    const retryLogStr = retryOfRequestLogID === undefined ? '' : `, retryOf: ${retryOfRequestLogID}`;\n    const startTime = Date.now();\n\n    loggerFor(this).debug(\n      `[${requestLogID}] sending request`,\n      formatRequestDetails({\n        retryOfRequestLogID,\n        method: options.method,\n        url,\n        options,\n        headers: req.headers,\n      }),\n    );\n\n    if (options.signal?.aborted) {\n      throw new Errors.APIUserAbortError();\n    }\n\n    const controller = new AbortController();\n    const response = await this.fetchWithTimeout(url, req, timeout, controller).catch(castToError);\n    const headersTime = Date.now();\n\n    if (response instanceof Error) {\n      const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n      if (options.signal?.aborted) {\n        throw new Errors.APIUserAbortError();\n      }\n      // detect native connection timeout errors\n      // deno throws \"TypeError: error sending request for url (https://example/): client error (Connect): tcp connect error: Operation timed out (os error 60): Operation timed out (os error 60)\"\n      // undici throws \"TypeError: fetch failed\" with cause \"ConnectTimeoutError: Connect Timeout Error (attempted address: example:443, timeout: 1ms)\"\n      // others do not provide enough information to distinguish timeouts from other connection errors\n      const isTimeout =\n        isAbortError(response) ||\n        /timed? ?out/i.test(String(response) + ('cause' in response ? String(response.cause) : ''));\n      if (retriesRemaining) {\n        loggerFor(this).info(\n          `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} - ${retryMessage}`,\n        );\n        loggerFor(this).debug(\n          `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} (${retryMessage})`,\n          formatRequestDetails({\n            retryOfRequestLogID,\n            url,\n            durationMs: headersTime - startTime,\n            message: response.message,\n          }),\n        );\n        return this.retryRequest(options, retriesRemaining, retryOfRequestLogID ?? requestLogID);\n      }\n      loggerFor(this).info(\n        `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} - error; no more retries left`,\n      );\n      loggerFor(this).debug(\n        `[${requestLogID}] connection ${isTimeout ? 'timed out' : 'failed'} (error; no more retries left)`,\n        formatRequestDetails({\n          retryOfRequestLogID,\n          url,\n          durationMs: headersTime - startTime,\n          message: response.message,\n        }),\n      );\n      if (isTimeout) {\n        throw new Errors.APIConnectionTimeoutError();\n      }\n      throw new Errors.APIConnectionError({ cause: response });\n    }\n\n    const specialHeaders = [...response.headers.entries()]\n      .filter(([name]) => name === 'request-id')\n      .map(([name, value]) => ', ' + name + ': ' + JSON.stringify(value))\n      .join('');\n    const responseInfo = `[${requestLogID}${retryLogStr}${specialHeaders}] ${req.method} ${url} ${\n      response.ok ? 'succeeded' : 'failed'\n    } with status ${response.status} in ${headersTime - startTime}ms`;\n\n    if (!response.ok) {\n      const shouldRetry = this.shouldRetry(response);\n      if (retriesRemaining && shouldRetry) {\n        const retryMessage = `retrying, ${retriesRemaining} attempts remaining`;\n\n        // We don't need the body of this response.\n        await Shims.CancelReadableStream(response.body);\n        loggerFor(this).info(`${responseInfo} - ${retryMessage}`);\n        loggerFor(this).debug(\n          `[${requestLogID}] response error (${retryMessage})`,\n          formatRequestDetails({\n            retryOfRequestLogID,\n            url: response.url,\n            status: response.status,\n            headers: response.headers,\n            durationMs: headersTime - startTime,\n          }),\n        );\n        return this.retryRequest(\n          options,\n          retriesRemaining,\n          retryOfRequestLogID ?? requestLogID,\n          response.headers,\n        );\n      }\n\n      const retryMessage = shouldRetry ? `error; no more retries left` : `error; not retryable`;\n\n      loggerFor(this).info(`${responseInfo} - ${retryMessage}`);\n\n      const errText = await response.text().catch((err: any) => castToError(err).message);\n      const errJSON = safeJSON(errText);\n      const errMessage = errJSON ? undefined : errText;\n\n      loggerFor(this).debug(\n        `[${requestLogID}] response error (${retryMessage})`,\n        formatRequestDetails({\n          retryOfRequestLogID,\n          url: response.url,\n          status: response.status,\n          headers: response.headers,\n          message: errMessage,\n          durationMs: Date.now() - startTime,\n        }),\n      );\n\n      const err = this.makeStatusError(response.status, errJSON, errMessage, response.headers);\n      throw err;\n    }\n\n    loggerFor(this).info(responseInfo);\n    loggerFor(this).debug(\n      `[${requestLogID}] response start`,\n      formatRequestDetails({\n        retryOfRequestLogID,\n        url: response.url,\n        status: response.status,\n        headers: response.headers,\n        durationMs: headersTime - startTime,\n      }),\n    );\n\n    return { response, options, controller, requestLogID, retryOfRequestLogID, startTime };\n  }\n\n  getAPIList<Item, PageClass extends Pagination.AbstractPage<Item> = Pagination.AbstractPage<Item>>(\n    path: string,\n    Page: new (...args: any[]) => PageClass,\n    opts?: RequestOptions,\n  ): Pagination.PagePromise<PageClass, Item> {\n    return this.requestAPIList(Page, { method: 'get', path, ...opts });\n  }\n\n  requestAPIList<\n    Item = unknown,\n    PageClass extends Pagination.AbstractPage<Item> = Pagination.AbstractPage<Item>,\n  >(\n    Page: new (...args: ConstructorParameters<typeof Pagination.AbstractPage>) => PageClass,\n    options: FinalRequestOptions,\n  ): Pagination.PagePromise<PageClass, Item> {\n    const request = this.makeRequest(options, null, undefined);\n    return new Pagination.PagePromise<PageClass, Item>(this as any as Anthropic, request, Page);\n  }\n\n  async fetchWithTimeout(\n    url: RequestInfo,\n    init: RequestInit | undefined,\n    ms: number,\n    controller: AbortController,\n  ): Promise<Response> {\n    const { signal, method, ...options } = init || {};\n    if (signal) signal.addEventListener('abort', () => controller.abort());\n\n    const timeout = setTimeout(() => controller.abort(), ms);\n\n    const isReadableBody =\n      ((globalThis as any).ReadableStream && options.body instanceof (globalThis as any).ReadableStream) ||\n      (typeof options.body === 'object' && options.body !== null && Symbol.asyncIterator in options.body);\n\n    const fetchOptions: RequestInit = {\n      signal: controller.signal as any,\n      ...(isReadableBody ? { duplex: 'half' } : {}),\n      method: 'GET',\n      ...options,\n    };\n    if (method) {\n      // Custom methods like 'patch' need to be uppercased\n      // See https://github.com/nodejs/undici/issues/2294\n      fetchOptions.method = method.toUpperCase();\n    }\n\n    try {\n      // use undefined this binding; fetch errors if bound to something else in browser/cloudflare\n      return await this.fetch.call(undefined, url, fetchOptions);\n    } finally {\n      clearTimeout(timeout);\n    }\n  }\n\n  private shouldRetry(response: Response): boolean {\n    // Note this is not a standard header.\n    const shouldRetryHeader = response.headers.get('x-should-retry');\n\n    // If the server explicitly says whether or not to retry, obey.\n    if (shouldRetryHeader === 'true') return true;\n    if (shouldRetryHeader === 'false') return false;\n\n    // Retry on request timeouts.\n    if (response.status === 408) return true;\n\n    // Retry on lock timeouts.\n    if (response.status === 409) return true;\n\n    // Retry on rate limits.\n    if (response.status === 429) return true;\n\n    // Retry internal errors.\n    if (response.status >= 500) return true;\n\n    return false;\n  }\n\n  private async retryRequest(\n    options: FinalRequestOptions,\n    retriesRemaining: number,\n    requestLogID: string,\n    responseHeaders?: Headers | undefined,\n  ): Promise<APIResponseProps> {\n    let timeoutMillis: number | undefined;\n\n    // Note the `retry-after-ms` header may not be standard, but is a good idea and we'd like proactive support for it.\n    const retryAfterMillisHeader = responseHeaders?.get('retry-after-ms');\n    if (retryAfterMillisHeader) {\n      const timeoutMs = parseFloat(retryAfterMillisHeader);\n      if (!Number.isNaN(timeoutMs)) {\n        timeoutMillis = timeoutMs;\n      }\n    }\n\n    // About the Retry-After header: https://developer.mozilla.org/en-US/docs/Web/HTTP/Headers/Retry-After\n    const retryAfterHeader = responseHeaders?.get('retry-after');\n    if (retryAfterHeader && !timeoutMillis) {\n      const timeoutSeconds = parseFloat(retryAfterHeader);\n      if (!Number.isNaN(timeoutSeconds)) {\n        timeoutMillis = timeoutSeconds * 1000;\n      } else {\n        timeoutMillis = Date.parse(retryAfterHeader) - Date.now();\n      }\n    }\n\n    // If the API asks us to wait a certain amount of time (and it's a reasonable amount),\n    // just do what it says, but otherwise calculate a default\n    if (!(timeoutMillis && 0 <= timeoutMillis && timeoutMillis < 60 * 1000)) {\n      const maxRetries = options.maxRetries ?? this.maxRetries;\n      timeoutMillis = this.calculateDefaultRetryTimeoutMillis(retriesRemaining, maxRetries);\n    }\n    await sleep(timeoutMillis);\n\n    return this.makeRequest(options, retriesRemaining - 1, requestLogID);\n  }\n\n  private calculateDefaultRetryTimeoutMillis(retriesRemaining: number, maxRetries: number): number {\n    const initialRetryDelay = 0.5;\n    const maxRetryDelay = 8.0;\n\n    const numRetries = maxRetries - retriesRemaining;\n\n    // Apply exponential backoff, but not more than the max.\n    const sleepSeconds = Math.min(initialRetryDelay * Math.pow(2, numRetries), maxRetryDelay);\n\n    // Apply some jitter, take up to at most 25 percent of the retry time.\n    const jitter = 1 - Math.random() * 0.25;\n\n    return sleepSeconds * jitter * 1000;\n  }\n\n  public calculateNonstreamingTimeout(maxTokens: number, maxNonstreamingTokens?: number): number {\n    const maxTime = 60 * 60 * 1000; // 10 minutes\n    const defaultTime = 60 * 10 * 1000; // 10 minutes\n\n    const expectedTime = (maxTime * maxTokens) / 128000;\n    if (expectedTime > defaultTime || (maxNonstreamingTokens != null && maxTokens > maxNonstreamingTokens)) {\n      throw new Errors.AnthropicError(\n        'Streaming is strongly recommended for operations that may token longer than 10 minutes. See https://github.com/anthropics/anthropic-sdk-typescript#long-requests for more details',\n      );\n    }\n\n    return defaultTime;\n  }\n\n  buildRequest(\n    inputOptions: FinalRequestOptions,\n    { retryCount = 0 }: { retryCount?: number } = {},\n  ): { req: FinalizedRequestInit; url: string; timeout: number } {\n    const options = { ...inputOptions };\n    const { method, path, query } = options;\n\n    const url = this.buildURL(path!, query as Record<string, unknown>);\n    if ('timeout' in options) validatePositiveInteger('timeout', options.timeout);\n    options.timeout = options.timeout ?? this.timeout;\n    const { bodyHeaders, body } = this.buildBody({ options });\n    const reqHeaders = this.buildHeaders({ options: inputOptions, method, bodyHeaders, retryCount });\n\n    const req: FinalizedRequestInit = {\n      method,\n      headers: reqHeaders,\n      ...(options.signal && { signal: options.signal }),\n      ...((globalThis as any).ReadableStream &&\n        body instanceof (globalThis as any).ReadableStream && { duplex: 'half' }),\n      ...(body && { body }),\n      ...((this.fetchOptions as any) ?? {}),\n      ...((options.fetchOptions as any) ?? {}),\n    };\n\n    return { req, url, timeout: options.timeout };\n  }\n\n  private buildHeaders({\n    options,\n    method,\n    bodyHeaders,\n    retryCount,\n  }: {\n    options: FinalRequestOptions;\n    method: HTTPMethod;\n    bodyHeaders: HeadersLike;\n    retryCount: number;\n  }): Headers {\n    let idempotencyHeaders: HeadersLike = {};\n    if (this.idempotencyHeader && method !== 'get') {\n      if (!options.idempotencyKey) options.idempotencyKey = this.defaultIdempotencyKey();\n      idempotencyHeaders[this.idempotencyHeader] = options.idempotencyKey;\n    }\n\n    const headers = buildHeaders([\n      idempotencyHeaders,\n      {\n        Accept: 'application/json',\n        'User-Agent': this.getUserAgent(),\n        'X-Stainless-Retry-Count': String(retryCount),\n        ...(options.timeout ? { 'X-Stainless-Timeout': String(Math.trunc(options.timeout / 1000)) } : {}),\n        ...getPlatformHeaders(),\n        ...(this._options.dangerouslyAllowBrowser ?\n          { 'anthropic-dangerous-direct-browser-access': 'true' }\n        : undefined),\n        'anthropic-version': '2023-06-01',\n      },\n      this.authHeaders(options),\n      this._options.defaultHeaders,\n      bodyHeaders,\n      options.headers,\n    ]);\n\n    this.validateHeaders(headers);\n\n    return headers.values;\n  }\n\n  private buildBody({ options: { body, headers: rawHeaders } }: { options: FinalRequestOptions }): {\n    bodyHeaders: HeadersLike;\n    body: BodyInit | undefined;\n  } {\n    if (!body) {\n      return { bodyHeaders: undefined, body: undefined };\n    }\n    const headers = buildHeaders([rawHeaders]);\n    if (\n      // Pass raw type verbatim\n      ArrayBuffer.isView(body) ||\n      body instanceof ArrayBuffer ||\n      body instanceof DataView ||\n      (typeof body === 'string' &&\n        // Preserve legacy string encoding behavior for now\n        headers.values.has('content-type')) ||\n      // `Blob` is superset of `File`\n      body instanceof Blob ||\n      // `FormData` -> `multipart/form-data`\n      body instanceof FormData ||\n      // `URLSearchParams` -> `application/x-www-form-urlencoded`\n      body instanceof URLSearchParams ||\n      // Send chunked stream (each chunk has own `length`)\n      ((globalThis as any).ReadableStream && body instanceof (globalThis as any).ReadableStream)\n    ) {\n      return { bodyHeaders: undefined, body: body as BodyInit };\n    } else if (\n      typeof body === 'object' &&\n      (Symbol.asyncIterator in body ||\n        (Symbol.iterator in body && 'next' in body && typeof body.next === 'function'))\n    ) {\n      return { bodyHeaders: undefined, body: Shims.ReadableStreamFrom(body as AsyncIterable<Uint8Array>) };\n    } else {\n      return this.#encoder({ body, headers });\n    }\n  }\n\n  static Anthropic = this;\n  static HUMAN_PROMPT = '\\n\\nHuman:';\n  static AI_PROMPT = '\\n\\nAssistant:';\n  static DEFAULT_TIMEOUT = 600000; // 10 minutes\n\n  static AnthropicError = Errors.AnthropicError;\n  static APIError = Errors.APIError;\n  static APIConnectionError = Errors.APIConnectionError;\n  static APIConnectionTimeoutError = Errors.APIConnectionTimeoutError;\n  static APIUserAbortError = Errors.APIUserAbortError;\n  static NotFoundError = Errors.NotFoundError;\n  static ConflictError = Errors.ConflictError;\n  static RateLimitError = Errors.RateLimitError;\n  static BadRequestError = Errors.BadRequestError;\n  static AuthenticationError = Errors.AuthenticationError;\n  static InternalServerError = Errors.InternalServerError;\n  static PermissionDeniedError = Errors.PermissionDeniedError;\n  static UnprocessableEntityError = Errors.UnprocessableEntityError;\n\n  static toFile = Uploads.toFile;\n}\n\n/**\n * API Client for interfacing with the Anthropic API.\n */\nexport class Anthropic extends BaseAnthropic {\n  completions: API.Completions = new API.Completions(this);\n  messages: API.Messages = new API.Messages(this);\n  models: API.Models = new API.Models(this);\n  beta: API.Beta = new API.Beta(this);\n}\nAnthropic.Completions = Completions;\nAnthropic.Messages = Messages;\nAnthropic.Models = Models;\nAnthropic.Beta = Beta;\nexport declare namespace Anthropic {\n  export type RequestOptions = Opts.RequestOptions;\n\n  export import Page = Pagination.Page;\n  export { type PageParams as PageParams, type PageResponse as PageResponse };\n\n  export {\n    Completions as Completions,\n    type Completion as Completion,\n    type CompletionCreateParams as CompletionCreateParams,\n    type CompletionCreateParamsNonStreaming as CompletionCreateParamsNonStreaming,\n    type CompletionCreateParamsStreaming as CompletionCreateParamsStreaming,\n  };\n\n  export {\n    Messages as Messages,\n    type Base64ImageSource as Base64ImageSource,\n    type Base64PDFSource as Base64PDFSource,\n    type CacheControlEphemeral as CacheControlEphemeral,\n    type CitationCharLocation as CitationCharLocation,\n    type CitationCharLocationParam as CitationCharLocationParam,\n    type CitationContentBlockLocation as CitationContentBlockLocation,\n    type CitationContentBlockLocationParam as CitationContentBlockLocationParam,\n    type CitationPageLocation as CitationPageLocation,\n    type CitationPageLocationParam as CitationPageLocationParam,\n    type CitationWebSearchResultLocationParam as CitationWebSearchResultLocationParam,\n    type CitationsConfigParam as CitationsConfigParam,\n    type CitationsDelta as CitationsDelta,\n    type CitationsWebSearchResultLocation as CitationsWebSearchResultLocation,\n    type ContentBlock as ContentBlock,\n    type ContentBlockDeltaEvent as ContentBlockDeltaEvent,\n    type ContentBlockParam as ContentBlockParam,\n    type ContentBlockStartEvent as ContentBlockStartEvent,\n    type ContentBlockStopEvent as ContentBlockStopEvent,\n    type ContentBlockSource as ContentBlockSource,\n    type ContentBlockSourceContent as ContentBlockSourceContent,\n    type DocumentBlockParam as DocumentBlockParam,\n    type ImageBlockParam as ImageBlockParam,\n    type InputJSONDelta as InputJSONDelta,\n    type Message as Message,\n    type MessageCountTokensTool as MessageCountTokensTool,\n    type MessageDeltaEvent as MessageDeltaEvent,\n    type MessageDeltaUsage as MessageDeltaUsage,\n    type MessageParam as MessageParam,\n    type MessageStartEvent as MessageStartEvent,\n    type MessageStopEvent as MessageStopEvent,\n    type MessageStreamEvent as MessageStreamEvent,\n    type MessageTokensCount as MessageTokensCount,\n    type Metadata as Metadata,\n    type Model as Model,\n    type PlainTextSource as PlainTextSource,\n    type RawContentBlockDelta as RawContentBlockDelta,\n    type RawContentBlockDeltaEvent as RawContentBlockDeltaEvent,\n    type RawContentBlockStartEvent as RawContentBlockStartEvent,\n    type RawContentBlockStopEvent as RawContentBlockStopEvent,\n    type RawMessageDeltaEvent as RawMessageDeltaEvent,\n    type RawMessageStartEvent as RawMessageStartEvent,\n    type RawMessageStopEvent as RawMessageStopEvent,\n    type RawMessageStreamEvent as RawMessageStreamEvent,\n    type RedactedThinkingBlock as RedactedThinkingBlock,\n    type RedactedThinkingBlockParam as RedactedThinkingBlockParam,\n    type ServerToolUsage as ServerToolUsage,\n    type ServerToolUseBlock as ServerToolUseBlock,\n    type ServerToolUseBlockParam as ServerToolUseBlockParam,\n    type SignatureDelta as SignatureDelta,\n    type StopReason as StopReason,\n    type TextBlock as TextBlock,\n    type TextBlockParam as TextBlockParam,\n    type TextCitation as TextCitation,\n    type TextCitationParam as TextCitationParam,\n    type TextDelta as TextDelta,\n    type ThinkingBlock as ThinkingBlock,\n    type ThinkingBlockParam as ThinkingBlockParam,\n    type ThinkingConfigDisabled as ThinkingConfigDisabled,\n    type ThinkingConfigEnabled as ThinkingConfigEnabled,\n    type ThinkingConfigParam as ThinkingConfigParam,\n    type ThinkingDelta as ThinkingDelta,\n    type Tool as Tool,\n    type ToolBash20250124 as ToolBash20250124,\n    type ToolChoice as ToolChoice,\n    type ToolChoiceAny as ToolChoiceAny,\n    type ToolChoiceAuto as ToolChoiceAuto,\n    type ToolChoiceNone as ToolChoiceNone,\n    type ToolChoiceTool as ToolChoiceTool,\n    type ToolResultBlockParam as ToolResultBlockParam,\n    type ToolTextEditor20250124 as ToolTextEditor20250124,\n    type ToolUnion as ToolUnion,\n    type ToolUseBlock as ToolUseBlock,\n    type ToolUseBlockParam as ToolUseBlockParam,\n    type URLImageSource as URLImageSource,\n    type URLPDFSource as URLPDFSource,\n    type Usage as Usage,\n    type WebSearchResultBlock as WebSearchResultBlock,\n    type WebSearchResultBlockParam as WebSearchResultBlockParam,\n    type WebSearchTool20250305 as WebSearchTool20250305,\n    type WebSearchToolRequestError as WebSearchToolRequestError,\n    type WebSearchToolResultBlock as WebSearchToolResultBlock,\n    type WebSearchToolResultBlockContent as WebSearchToolResultBlockContent,\n    type WebSearchToolResultBlockParam as WebSearchToolResultBlockParam,\n    type WebSearchToolResultBlockParamContent as WebSearchToolResultBlockParamContent,\n    type WebSearchToolResultError as WebSearchToolResultError,\n    type MessageCreateParams as MessageCreateParams,\n    type MessageCreateParamsNonStreaming as MessageCreateParamsNonStreaming,\n    type MessageCreateParamsStreaming as MessageCreateParamsStreaming,\n    type MessageStreamParams as MessageStreamParams,\n    type MessageCountTokensParams as MessageCountTokensParams,\n  };\n\n  export {\n    Models as Models,\n    type ModelInfo as ModelInfo,\n    type ModelInfosPage as ModelInfosPage,\n    type ModelRetrieveParams as ModelRetrieveParams,\n    type ModelListParams as ModelListParams,\n  };\n\n  export {\n    Beta as Beta,\n    type AnthropicBeta as AnthropicBeta,\n    type BetaAPIError as BetaAPIError,\n    type BetaAuthenticationError as BetaAuthenticationError,\n    type BetaBillingError as BetaBillingError,\n    type BetaError as BetaError,\n    type BetaErrorResponse as BetaErrorResponse,\n    type BetaGatewayTimeoutError as BetaGatewayTimeoutError,\n    type BetaInvalidRequestError as BetaInvalidRequestError,\n    type BetaNotFoundError as BetaNotFoundError,\n    type BetaOverloadedError as BetaOverloadedError,\n    type BetaPermissionError as BetaPermissionError,\n    type BetaRateLimitError as BetaRateLimitError,\n  };\n\n  export type APIErrorObject = API.APIErrorObject;\n  export type AuthenticationError = API.AuthenticationError;\n  export type BillingError = API.BillingError;\n  export type ErrorObject = API.ErrorObject;\n  export type ErrorResponse = API.ErrorResponse;\n  export type GatewayTimeoutError = API.GatewayTimeoutError;\n  export type InvalidRequestError = API.InvalidRequestError;\n  export type NotFoundError = API.NotFoundError;\n  export type OverloadedError = API.OverloadedError;\n  export type PermissionError = API.PermissionError;\n  export type RateLimitError = API.RateLimitError;\n}\nexport const { HUMAN_PROMPT, AI_PROMPT } = Anthropic;\n"], "mappings": ";;;AAAA,SAAS,uBAAuB,UAAU,OAAO,OAAO,MAAM,GAAG;AAC7D,MAAI,SAAS;AACT,UAAM,IAAI,UAAU,gCAAgC;AACxD,MAAI,SAAS,OAAO,CAAC;AACjB,UAAM,IAAI,UAAU,+CAA+C;AACvE,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ;AAC5E,UAAM,IAAI,UAAU,yEAAyE;AACjG,SAAO,SAAS,MAAM,EAAE,KAAK,UAAU,KAAK,IAAI,IAAK,EAAE,QAAQ,QAAS,MAAM,IAAI,UAAU,KAAK,GAAG;AACxG;AACA,SAAS,uBAAuB,UAAU,OAAO,MAAM,GAAG;AACtD,MAAI,SAAS,OAAO,CAAC;AACjB,UAAM,IAAI,UAAU,+CAA+C;AACvE,MAAI,OAAO,UAAU,aAAa,aAAa,SAAS,CAAC,IAAI,CAAC,MAAM,IAAI,QAAQ;AAC5E,UAAM,IAAI,UAAU,0EAA0E;AAClG,SAAO,SAAS,MAAM,IAAI,SAAS,MAAM,EAAE,KAAK,QAAQ,IAAI,IAAI,EAAE,QAAQ,MAAM,IAAI,QAAQ;AAChG;;;ACVO,IAAI,QAAQ,WAAA;AACjB,QAAM,EAAE,OAAM,IAAK;AACnB,MAAI,iCAAQ,YAAY;AACtB,YAAQ,OAAO,WAAW,KAAK,MAAM;AACrC,WAAO,OAAO,WAAU;EAC1B;AACA,QAAM,KAAK,IAAI,WAAW,CAAC;AAC3B,QAAM,aAAa,SAAS,MAAM,OAAO,gBAAgB,EAAE,EAAE,CAAC,IAAK,MAAO,KAAK,OAAM,IAAK,MAAQ;AAClG,SAAO,uCAAuC,QAAQ,UAAU,CAAC,OAC9D,CAAC,IAAK,WAAU,IAAM,MAAO,CAAC,IAAI,GAAM,SAAS,EAAE,CAAC;AAEzD;;;ACdM,SAAU,aAAa,KAAY;AACvC,SACE,OAAO,QAAQ,YACf,QAAQ;GAEN,UAAU,OAAQ,IAAY,SAAS;EAEtC,aAAa,OAAO,OAAQ,IAAY,OAAO,EAAE,SAAS,+BAA+B;AAEhG;AAEO,IAAM,cAAc,CAAC,QAAmB;AAC7C,MAAI,eAAe;AAAO,WAAO;AACjC,MAAI,OAAO,QAAQ,YAAY,QAAQ,MAAM;AAC3C,QAAI;AACF,UAAI,OAAO,UAAU,SAAS,KAAK,GAAG,MAAM,kBAAkB;AAE5D,cAAM,QAAQ,IAAI,MAAM,IAAI,SAAS,IAAI,QAAQ,EAAE,OAAO,IAAI,MAAK,IAAK,CAAA,CAAE;AAC1E,YAAI,IAAI;AAAO,gBAAM,QAAQ,IAAI;AAEjC,YAAI,IAAI,SAAS,CAAC,MAAM;AAAO,gBAAM,QAAQ,IAAI;AACjD,YAAI,IAAI;AAAM,gBAAM,OAAO,IAAI;AAC/B,eAAO;MACT;IACF,QAAQ;IAAC;AACT,QAAI;AACF,aAAO,IAAI,MAAM,KAAK,UAAU,GAAG,CAAC;IACtC,QAAQ;IAAC;EACX;AACA,SAAO,IAAI,MAAM,GAAG;AACtB;;;AC5BM,IAAO,iBAAP,cAA8B,MAAK;;AAEnC,IAAO,WAAP,MAAO,kBAIH,eAAc;EAUtB,YAAY,QAAiB,OAAe,SAA6B,SAAiB;AACxF,UAAM,GAAG,UAAS,YAAY,QAAQ,OAAO,OAAO,CAAC,EAAE;AACvD,SAAK,SAAS;AACd,SAAK,UAAU;AACf,SAAK,YAAY,mCAAS,IAAI;AAC9B,SAAK,QAAQ;EACf;EAEQ,OAAO,YAAY,QAA4B,OAAY,SAA2B;AAC5F,UAAM,OACJ,+BAAO,WACL,OAAO,MAAM,YAAY,WACvB,MAAM,UACN,KAAK,UAAU,MAAM,OAAO,IAC9B,QAAQ,KAAK,UAAU,KAAK,IAC5B;AAEJ,QAAI,UAAU,KAAK;AACjB,aAAO,GAAG,MAAM,IAAI,GAAG;IACzB;AACA,QAAI,QAAQ;AACV,aAAO,GAAG,MAAM;IAClB;AACA,QAAI,KAAK;AACP,aAAO;IACT;AACA,WAAO;EACT;EAEA,OAAO,SACL,QACA,eACA,SACA,SAA4B;AAE5B,QAAI,CAAC,UAAU,CAAC,SAAS;AACvB,aAAO,IAAI,mBAAmB,EAAE,SAAS,OAAO,YAAY,aAAa,EAAC,CAAE;IAC9E;AAEA,UAAM,QAAQ;AAEd,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,gBAAgB,QAAQ,OAAO,SAAS,OAAO;IAC5D;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,oBAAoB,QAAQ,OAAO,SAAS,OAAO;IAChE;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,sBAAsB,QAAQ,OAAO,SAAS,OAAO;IAClE;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,cAAc,QAAQ,OAAO,SAAS,OAAO;IAC1D;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,cAAc,QAAQ,OAAO,SAAS,OAAO;IAC1D;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,yBAAyB,QAAQ,OAAO,SAAS,OAAO;IACrE;AAEA,QAAI,WAAW,KAAK;AAClB,aAAO,IAAI,eAAe,QAAQ,OAAO,SAAS,OAAO;IAC3D;AAEA,QAAI,UAAU,KAAK;AACjB,aAAO,IAAI,oBAAoB,QAAQ,OAAO,SAAS,OAAO;IAChE;AAEA,WAAO,IAAI,UAAS,QAAQ,OAAO,SAAS,OAAO;EACrD;;AAGI,IAAO,oBAAP,cAAiC,SAAyC;EAC9E,YAAY,EAAE,QAAO,IAA2B,CAAA,GAAE;AAChD,UAAM,QAAW,QAAW,WAAW,wBAAwB,MAAS;EAC1E;;AAGI,IAAO,qBAAP,cAAkC,SAAyC;EAC/E,YAAY,EAAE,SAAS,MAAK,GAA+D;AACzF,UAAM,QAAW,QAAW,WAAW,qBAAqB,MAAS;AAGrE,QAAI;AAAO,WAAK,QAAQ;EAC1B;;AAGI,IAAO,4BAAP,cAAyC,mBAAkB;EAC/D,YAAY,EAAE,QAAO,IAA2B,CAAA,GAAE;AAChD,UAAM,EAAE,SAAS,WAAW,qBAAoB,CAAE;EACpD;;AAGI,IAAO,kBAAP,cAA+B,SAAsB;;AAErD,IAAO,sBAAP,cAAmC,SAAsB;;AAEzD,IAAO,wBAAP,cAAqC,SAAsB;;AAE3D,IAAO,gBAAP,cAA6B,SAAsB;;AAEnD,IAAO,gBAAP,cAA6B,SAAsB;;AAEnD,IAAO,2BAAP,cAAwC,SAAsB;;AAE9D,IAAO,iBAAP,cAA8B,SAAsB;;AAEpD,IAAO,sBAAP,cAAmC,SAAyB;;;;AC/HlE,IAAM,yBAAyB;AAExB,IAAM,gBAAgB,CAAC,QAAwB;AACpD,SAAO,uBAAuB,KAAK,GAAG;AACxC;AAGM,SAAU,SAAS,GAAU;AACjC,MAAI,OAAO,MAAM,UAAU;AACzB,WAAO,CAAA;EACT;AAEA,SAAO,KAAK,CAAA;AACd;AAGM,SAAU,WAAW,KAA8B;AACvD,MAAI,CAAC;AAAK,WAAO;AACjB,aAAW,MAAM;AAAK,WAAO;AAC7B,SAAO;AACT;AAGM,SAAU,OAAkC,KAAQ,KAAgB;AACxE,SAAO,OAAO,UAAU,eAAe,KAAK,KAAK,GAAG;AACtD;AAcO,IAAM,0BAA0B,CAAC,MAAc,MAAsB;AAC1E,MAAI,OAAO,MAAM,YAAY,CAAC,OAAO,UAAU,CAAC,GAAG;AACjD,UAAM,IAAI,eAAe,GAAG,IAAI,qBAAqB;EACvD;AACA,MAAI,IAAI,GAAG;AACT,UAAM,IAAI,eAAe,GAAG,IAAI,6BAA6B;EAC/D;AACA,SAAO;AACT;AA2CO,IAAM,WAAW,CAAC,SAAgB;AACvC,MAAI;AACF,WAAO,KAAK,MAAM,IAAI;EACxB,SAAS,KAAK;AACZ,WAAO;EACT;AACF;;;ACnGO,IAAM,QAAQ,CAAC,OAAe,IAAI,QAAc,CAAC,YAAY,WAAW,SAAS,EAAE,CAAC;;;ACa3F,IAAM,eAAe;EACnB,KAAK;EACL,OAAO;EACP,MAAM;EACN,MAAM;EACN,OAAO;;AAGF,IAAM,gBAAgB,CAC3B,YACA,YACA,WACwB;AACxB,MAAI,CAAC,YAAY;AACf,WAAO;EACT;AACA,MAAI,OAAO,cAAc,UAAU,GAAG;AACpC,WAAO;EACT;AACA,YAAU,MAAM,EAAE,KAChB,GAAG,UAAU,eAAe,KAAK,UAAU,UAAU,CAAC,qBAAqB,KAAK,UAC9E,OAAO,KAAK,YAAY,CAAC,CAC1B,EAAE;AAEL,SAAO;AACT;AAEA,SAAS,OAAI;AAAI;AAEjB,SAAS,UAAU,SAAuB,QAA4B,UAAkB;AACtF,MAAI,CAAC,UAAU,aAAa,OAAO,IAAI,aAAa,QAAQ,GAAG;AAC7D,WAAO;EACT,OAAO;AAEL,WAAO,OAAO,OAAO,EAAE,KAAK,MAAM;EACpC;AACF;AAEA,IAAM,aAAa;EACjB,OAAO;EACP,MAAM;EACN,MAAM;EACN,OAAO;;AAGT,IAAI,gBAAgB,oBAAI,QAAO;AAEzB,SAAU,UAAU,QAAqB;AAC7C,QAAM,SAAS,OAAO;AACtB,QAAM,WAAW,OAAO,YAAY;AACpC,MAAI,CAAC,QAAQ;AACX,WAAO;EACT;AAEA,QAAM,eAAe,cAAc,IAAI,MAAM;AAC7C,MAAI,gBAAgB,aAAa,CAAC,MAAM,UAAU;AAChD,WAAO,aAAa,CAAC;EACvB;AAEA,QAAM,cAAc;IAClB,OAAO,UAAU,SAAS,QAAQ,QAAQ;IAC1C,MAAM,UAAU,QAAQ,QAAQ,QAAQ;IACxC,MAAM,UAAU,QAAQ,QAAQ,QAAQ;IACxC,OAAO,UAAU,SAAS,QAAQ,QAAQ;;AAG5C,gBAAc,IAAI,QAAQ,CAAC,UAAU,WAAW,CAAC;AAEjD,SAAO;AACT;AAEO,IAAM,uBAAuB,CAAC,YAWhC;AACH,MAAI,QAAQ,SAAS;AACnB,YAAQ,UAAU,EAAE,GAAG,QAAQ,QAAO;AACtC,WAAO,QAAQ,QAAQ,SAAS;EAClC;AACA,MAAI,QAAQ,SAAS;AACnB,YAAQ,UAAU,OAAO,aACtB,QAAQ,mBAAmB,UAAU,CAAC,GAAG,QAAQ,OAAO,IAAI,OAAO,QAAQ,QAAQ,OAAO,GAAG,IAC5F,CAAC,CAAC,MAAM,KAAK,MAAM;MACjB;MAEE,KAAK,YAAW,MAAO,eACvB,KAAK,YAAW,MAAO,mBACvB,KAAK,YAAW,MAAO,YACvB,KAAK,YAAW,MAAO,eAEvB,QACA;KACH,CACF;EAEL;AACA,MAAI,yBAAyB,SAAS;AACpC,QAAI,QAAQ,qBAAqB;AAC/B,cAAQ,UAAU,QAAQ;IAC5B;AACA,WAAO,QAAQ;EACjB;AACA,SAAO;AACT;;;AC9HO,IAAM,UAAU;;;ACIhB,IAAM,qBAAqB,MAAK;AACrC;;IAEE,OAAO,WAAW;IAElB,OAAO,OAAO,aAAa;IAE3B,OAAO,cAAc;;AAEzB;AAOA,SAAS,sBAAmB;AAC1B,MAAI,OAAO,SAAS,eAAe,KAAK,SAAS,MAAM;AACrD,WAAO;EACT;AACA,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO;EACT;AACA,MACE,OAAO,UAAU,SAAS,KACxB,OAAQ,WAAmB,YAAY,cAAe,WAAmB,UAAU,CAAC,MAChF,oBACN;AACA,WAAO;EACT;AACA,SAAO;AACT;AAwBA,IAAM,wBAAwB,MAAyB;AA3DvD,MAAAA;AA4DE,QAAM,mBAAmB,oBAAmB;AAC5C,MAAI,qBAAqB,QAAQ;AAC/B,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB,kBAAkB,KAAK,MAAM,EAAE;MACjD,oBAAoB,cAAc,KAAK,MAAM,IAAI;MACjD,uBAAuB;MACvB,+BACE,OAAO,KAAK,YAAY,WAAW,KAAK,YAAUA,MAAA,KAAK,YAAL,gBAAAA,IAAc,SAAQ;;EAE9E;AACA,MAAI,OAAO,gBAAgB,aAAa;AACtC,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB;MAClB,oBAAoB,SAAS,WAAW;MACxC,uBAAuB;MACvB,+BAAgC,WAAmB,QAAQ;;EAE/D;AAEA,MAAI,qBAAqB,QAAQ;AAC/B,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB,kBAAmB,WAAmB,QAAQ,QAAQ;MACxE,oBAAoB,cAAe,WAAmB,QAAQ,IAAI;MAClE,uBAAuB;MACvB,+BAAgC,WAAmB,QAAQ;;EAE/D;AAEA,QAAM,cAAc,eAAc;AAClC,MAAI,aAAa;AACf,WAAO;MACL,oBAAoB;MACpB,+BAA+B;MAC/B,kBAAkB;MAClB,oBAAoB;MACpB,uBAAuB,WAAW,YAAY,OAAO;MACrD,+BAA+B,YAAY;;EAE/C;AAGA,SAAO;IACL,oBAAoB;IACpB,+BAA+B;IAC/B,kBAAkB;IAClB,oBAAoB;IACpB,uBAAuB;IACvB,+BAA+B;;AAEnC;AAUA,SAAS,iBAAc;AACrB,MAAI,OAAO,cAAc,eAAe,CAAC,WAAW;AAClD,WAAO;EACT;AAGA,QAAM,kBAAkB;IACtB,EAAE,KAAK,QAAiB,SAAS,uCAAsC;IACvE,EAAE,KAAK,MAAe,SAAS,uCAAsC;IACrE,EAAE,KAAK,MAAe,SAAS,6CAA4C;IAC3E,EAAE,KAAK,UAAmB,SAAS,yCAAwC;IAC3E,EAAE,KAAK,WAAoB,SAAS,0CAAyC;IAC7E,EAAE,KAAK,UAAmB,SAAS,oEAAmE;;AAIxG,aAAW,EAAE,KAAK,QAAO,KAAM,iBAAiB;AAC9C,UAAM,QAAQ,QAAQ,KAAK,UAAU,SAAS;AAC9C,QAAI,OAAO;AACT,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,QAAQ,MAAM,CAAC,KAAK;AAC1B,YAAM,QAAQ,MAAM,CAAC,KAAK;AAE1B,aAAO,EAAE,SAAS,KAAK,SAAS,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAE;IAC9D;EACF;AAEA,SAAO;AACT;AAEA,IAAM,gBAAgB,CAAC,SAAsB;AAK3C,MAAI,SAAS;AAAO,WAAO;AAC3B,MAAI,SAAS,YAAY,SAAS;AAAO,WAAO;AAChD,MAAI,SAAS;AAAO,WAAO;AAC3B,MAAI,SAAS,aAAa,SAAS;AAAS,WAAO;AACnD,MAAI;AAAM,WAAO,SAAS,IAAI;AAC9B,SAAO;AACT;AAEA,IAAM,oBAAoB,CAAC,aAAkC;AAO3D,aAAW,SAAS,YAAW;AAM/B,MAAI,SAAS,SAAS,KAAK;AAAG,WAAO;AACrC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAU,WAAO;AAClC,MAAI,aAAa;AAAS,WAAO;AACjC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAW,WAAO;AACnC,MAAI,aAAa;AAAS,WAAO;AACjC,MAAI;AAAU,WAAO,SAAS,QAAQ;AACtC,SAAO;AACT;AAEA,IAAI;AACG,IAAM,qBAAqB,MAAK;AACrC,SAAQ,qBAAA,mBAAqB,sBAAqB;AACpD;;;ACvLM,SAAU,kBAAe;AAC7B,MAAI,OAAO,UAAU,aAAa;AAChC,WAAO;EACT;AAEA,QAAM,IAAI,MACR,sJAAsJ;AAE1J;AAIM,SAAU,sBAAsB,MAAwB;AAC5D,QAAM,iBAAkB,WAAmB;AAC3C,MAAI,OAAO,mBAAmB,aAAa;AAGzC,UAAM,IAAI,MACR,yHAAyH;EAE7H;AAEA,SAAO,IAAI,eAAe,GAAG,IAAI;AACnC;AAEM,SAAU,mBAAsB,UAAwC;AAC5E,MAAI,OACF,OAAO,iBAAiB,WAAW,SAAS,OAAO,aAAa,EAAC,IAAK,SAAS,OAAO,QAAQ,EAAC;AAEjG,SAAO,mBAAmB;IACxB,QAAK;IAAI;IACT,MAAM,KAAK,YAAe;AACxB,YAAM,EAAE,MAAM,MAAK,IAAK,MAAM,KAAK,KAAI;AACvC,UAAI,MAAM;AACR,mBAAW,MAAK;MAClB,OAAO;AACL,mBAAW,QAAQ,KAAK;MAC1B;IACF;IACA,MAAM,SAAM;AAnDhB,UAAAC;AAoDM,cAAMA,MAAA,KAAK,WAAL,gBAAAA,IAAA;IACR;GACD;AACH;AAQM,SAAU,8BAAiC,QAAW;AAC1D,MAAI,OAAO,OAAO,aAAa;AAAG,WAAO;AAEzC,QAAM,SAAS,OAAO,UAAS;AAC/B,SAAO;IACL,MAAM,OAAI;AACR,UAAI;AACF,cAAM,SAAS,MAAM,OAAO,KAAI;AAChC,YAAI,iCAAQ;AAAM,iBAAO,YAAW;AACpC,eAAO;MACT,SAAS,GAAG;AACV,eAAO,YAAW;AAClB,cAAM;MACR;IACF;IACA,MAAM,SAAM;AACV,YAAM,gBAAgB,OAAO,OAAM;AACnC,aAAO,YAAW;AAClB,YAAM;AACN,aAAO,EAAE,MAAM,MAAM,OAAO,OAAS;IACvC;IACA,CAAC,OAAO,aAAa,IAAC;AACpB,aAAO;IACT;;AAEJ;AAMA,eAAsB,qBAAqB,QAAW;AA9FtD,MAAAA,KAAA;AA+FE,MAAI,WAAW,QAAQ,OAAO,WAAW;AAAU;AAEnD,MAAI,OAAO,OAAO,aAAa,GAAG;AAChC,YAAM,MAAAA,MAAA,OAAO,OAAO,aAAa,EAAC,GAAG,WAA/B,wBAAAA;AACN;EACF;AAEA,QAAM,SAAS,OAAO,UAAS;AAC/B,QAAM,gBAAgB,OAAO,OAAM;AACnC,SAAO,YAAW;AAClB,QAAM;AACR;;;AC3EO,IAAM,kBAAkC,CAAC,EAAE,SAAS,KAAI,MAAM;AACnE,SAAO;IACL,aAAa;MACX,gBAAgB;;IAElB,MAAM,KAAK,UAAU,IAAI;;AAE7B;;;ACtCM,SAAU,YAAY,SAAqB;AAC/C,MAAI,SAAS;AACb,aAAW,UAAU,SAAS;AAC5B,cAAU,OAAO;EACnB;AACA,QAAM,SAAS,IAAI,WAAW,MAAM;AACpC,MAAI,QAAQ;AACZ,aAAW,UAAU,SAAS;AAC5B,WAAO,IAAI,QAAQ,KAAK;AACxB,aAAS,OAAO;EAClB;AAEA,SAAO;AACT;AAEA,IAAI;AACE,SAAU,WAAW,KAAW;AACpC,MAAI;AACJ,UACE,gBACE,UAAU,IAAK,WAAmB,YAAW,GAAM,cAAc,QAAQ,OAAO,KAAK,OAAO,IAC9F,GAAG;AACP;AAEA,IAAI;AACE,SAAU,WAAW,OAAiB;AAC1C,MAAI;AACJ,UACE,gBACE,UAAU,IAAK,WAAmB,YAAW,GAAM,cAAc,QAAQ,OAAO,KAAK,OAAO,IAC9F,KAAK;AACT;;;;;ACrBM,IAAO,cAAP,MAAkB;EAQtB,cAAA;AAHA,wBAAA,IAAA,MAAA,MAAA;AACA,qCAAA,IAAA,MAAA,MAAA;AAGE,2BAAA,MAAI,qBAAW,IAAI,WAAU,GAAE,GAAA;AAC/B,2BAAA,MAAI,kCAAwB,MAAI,GAAA;EAClC;EAEA,OAAO,OAAY;AACjB,QAAI,SAAS,MAAM;AACjB,aAAO,CAAA;IACT;AAEA,UAAM,cACJ,iBAAiB,cAAc,IAAI,WAAW,KAAK,IACjD,OAAO,UAAU,WAAW,WAAW,KAAK,IAC5C;AAEJ,2BAAA,MAAI,qBAAW,YAAY,CAAC,uBAAA,MAAI,qBAAA,GAAA,GAAU,WAAW,CAAC,GAAC,GAAA;AAEvD,UAAM,QAAkB,CAAA;AACxB,QAAI;AACJ,YAAQ,eAAe,iBAAiB,uBAAA,MAAI,qBAAA,GAAA,GAAU,uBAAA,MAAI,kCAAA,GAAA,CAAqB,MAAM,MAAM;AACzF,UAAI,aAAa,YAAY,uBAAA,MAAI,kCAAA,GAAA,KAAyB,MAAM;AAE9D,+BAAA,MAAI,kCAAwB,aAAa,OAAK,GAAA;AAC9C;MACF;AAGA,UACE,uBAAA,MAAI,kCAAA,GAAA,KAAyB,SAC5B,aAAa,UAAU,uBAAA,MAAI,kCAAA,GAAA,IAAwB,KAAK,aAAa,WACtE;AACA,cAAM,KAAK,WAAW,uBAAA,MAAI,qBAAA,GAAA,EAAS,SAAS,GAAG,uBAAA,MAAI,kCAAA,GAAA,IAAwB,CAAC,CAAC,CAAC;AAC9E,+BAAA,MAAI,qBAAW,uBAAA,MAAI,qBAAA,GAAA,EAAS,SAAS,uBAAA,MAAI,kCAAA,GAAA,CAAqB,GAAC,GAAA;AAC/D,+BAAA,MAAI,kCAAwB,MAAI,GAAA;AAChC;MACF;AAEA,YAAM,WACJ,uBAAA,MAAI,kCAAA,GAAA,MAA0B,OAAO,aAAa,YAAY,IAAI,aAAa;AAEjF,YAAM,OAAO,WAAW,uBAAA,MAAI,qBAAA,GAAA,EAAS,SAAS,GAAG,QAAQ,CAAC;AAC1D,YAAM,KAAK,IAAI;AAEf,6BAAA,MAAI,qBAAW,uBAAA,MAAI,qBAAA,GAAA,EAAS,SAAS,aAAa,KAAK,GAAC,GAAA;AACxD,6BAAA,MAAI,kCAAwB,MAAI,GAAA;IAClC;AAEA,WAAO;EACT;EAEA,QAAK;AACH,QAAI,CAAC,uBAAA,MAAI,qBAAA,GAAA,EAAS,QAAQ;AACxB,aAAO,CAAA;IACT;AACA,WAAO,KAAK,OAAO,IAAI;EACzB;;;AA7DO,YAAA,gBAAgB,oBAAI,IAAI,CAAC,MAAM,IAAI,CAAC;AACpC,YAAA,iBAAiB;AAwE1B,SAAS,iBACP,QACA,YAAyB;AAEzB,QAAM,UAAU;AAChB,QAAM,WAAW;AAEjB,WAAS,IAAI,cAAc,GAAG,IAAI,OAAO,QAAQ,KAAK;AACpD,QAAI,OAAO,CAAC,MAAM,SAAS;AACzB,aAAO,EAAE,WAAW,GAAG,OAAO,IAAI,GAAG,UAAU,MAAK;IACtD;AAEA,QAAI,OAAO,CAAC,MAAM,UAAU;AAC1B,aAAO,EAAE,WAAW,GAAG,OAAO,IAAI,GAAG,UAAU,KAAI;IACrD;EACF;AAEA,SAAO;AACT;AAEM,SAAU,uBAAuB,QAAkB;AAIvD,QAAM,UAAU;AAChB,QAAM,WAAW;AAEjB,WAAS,IAAI,GAAG,IAAI,OAAO,SAAS,GAAG,KAAK;AAC1C,QAAI,OAAO,CAAC,MAAM,WAAW,OAAO,IAAI,CAAC,MAAM,SAAS;AAEtD,aAAO,IAAI;IACb;AACA,QAAI,OAAO,CAAC,MAAM,YAAY,OAAO,IAAI,CAAC,MAAM,UAAU;AAExD,aAAO,IAAI;IACb;AACA,QACE,OAAO,CAAC,MAAM,YACd,OAAO,IAAI,CAAC,MAAM,WAClB,IAAI,IAAI,OAAO,UACf,OAAO,IAAI,CAAC,MAAM,YAClB,OAAO,IAAI,CAAC,MAAM,SAClB;AAEA,aAAO,IAAI;IACb;EACF;AAEA,SAAO;AACT;;;ACnHM,IAAO,SAAP,MAAO,QAAM;EAGjB,YACU,UACR,YAA2B;AADnB,SAAA,WAAA;AAGR,SAAK,aAAa;EACpB;EAEA,OAAO,gBAAsB,UAAoB,YAA2B;AAC1E,QAAI,WAAW;AAEf,oBAAgB,WAAQ;AACtB,UAAI,UAAU;AACZ,cAAM,IAAI,eAAe,0EAA0E;MACrG;AACA,iBAAW;AACX,UAAI,OAAO;AACX,UAAI;AACF,yBAAiB,OAAO,iBAAiB,UAAU,UAAU,GAAG;AAC9D,cAAI,IAAI,UAAU,cAAc;AAC9B,gBAAI;AACF,oBAAM,KAAK,MAAM,IAAI,IAAI;YAC3B,SAAS,GAAG;AACV,sBAAQ,MAAM,sCAAsC,IAAI,IAAI;AAC5D,sBAAQ,MAAM,eAAe,IAAI,GAAG;AACpC,oBAAM;YACR;UACF;AAEA,cACE,IAAI,UAAU,mBACd,IAAI,UAAU,mBACd,IAAI,UAAU,kBACd,IAAI,UAAU,yBACd,IAAI,UAAU,yBACd,IAAI,UAAU,sBACd;AACA,gBAAI;AACF,oBAAM,KAAK,MAAM,IAAI,IAAI;YAC3B,SAAS,GAAG;AACV,sBAAQ,MAAM,sCAAsC,IAAI,IAAI;AAC5D,sBAAQ,MAAM,eAAe,IAAI,GAAG;AACpC,oBAAM;YACR;UACF;AAEA,cAAI,IAAI,UAAU,QAAQ;AACxB;UACF;AAEA,cAAI,IAAI,UAAU,SAAS;AACzB,kBAAM,IAAI,SAAS,QAAW,SAAS,IAAI,IAAI,KAAK,IAAI,MAAM,QAAW,SAAS,OAAO;UAC3F;QACF;AACA,eAAO;MACT,SAAS,GAAG;AAEV,YAAI,aAAa,CAAC;AAAG;AACrB,cAAM;MACR;AAEE,YAAI,CAAC;AAAM,qBAAW,MAAK;MAC7B;IACF;AAEA,WAAO,IAAI,QAAO,UAAU,UAAU;EACxC;;;;;EAMA,OAAO,mBAAyB,gBAAgC,YAA2B;AACzF,QAAI,WAAW;AAEf,oBAAgB,YAAS;AACvB,YAAM,cAAc,IAAI,YAAW;AAEnC,YAAM,OAAO,8BAAqC,cAAc;AAChE,uBAAiB,SAAS,MAAM;AAC9B,mBAAW,QAAQ,YAAY,OAAO,KAAK,GAAG;AAC5C,gBAAM;QACR;MACF;AAEA,iBAAW,QAAQ,YAAY,MAAK,GAAI;AACtC,cAAM;MACR;IACF;AAEA,oBAAgB,WAAQ;AACtB,UAAI,UAAU;AACZ,cAAM,IAAI,eAAe,0EAA0E;MACrG;AACA,iBAAW;AACX,UAAI,OAAO;AACX,UAAI;AACF,yBAAiB,QAAQ,UAAS,GAAI;AACpC,cAAI;AAAM;AACV,cAAI;AAAM,kBAAM,KAAK,MAAM,IAAI;QACjC;AACA,eAAO;MACT,SAAS,GAAG;AAEV,YAAI,aAAa,CAAC;AAAG;AACrB,cAAM;MACR;AAEE,YAAI,CAAC;AAAM,qBAAW,MAAK;MAC7B;IACF;AAEA,WAAO,IAAI,QAAO,UAAU,UAAU;EACxC;EAEA,CAAC,OAAO,aAAa,IAAC;AACpB,WAAO,KAAK,SAAQ;EACtB;;;;;EAMA,MAAG;AACD,UAAM,OAA6C,CAAA;AACnD,UAAM,QAA8C,CAAA;AACpD,UAAM,WAAW,KAAK,SAAQ;AAE9B,UAAM,cAAc,CAAC,UAAoE;AACvF,aAAO;QACL,MAAM,MAAK;AACT,cAAI,MAAM,WAAW,GAAG;AACtB,kBAAM,SAAS,SAAS,KAAI;AAC5B,iBAAK,KAAK,MAAM;AAChB,kBAAM,KAAK,MAAM;UACnB;AACA,iBAAO,MAAM,MAAK;QACpB;;IAEJ;AAEA,WAAO;MACL,IAAI,QAAO,MAAM,YAAY,IAAI,GAAG,KAAK,UAAU;MACnD,IAAI,QAAO,MAAM,YAAY,KAAK,GAAG,KAAK,UAAU;;EAExD;;;;;;EAOA,mBAAgB;AACd,UAAM,OAAO;AACb,QAAI;AAEJ,WAAO,mBAAmB;MACxB,MAAM,QAAK;AACT,eAAO,KAAK,OAAO,aAAa,EAAC;MACnC;MACA,MAAM,KAAK,MAAS;AAClB,YAAI;AACF,gBAAM,EAAE,OAAO,KAAI,IAAK,MAAM,KAAK,KAAI;AACvC,cAAI;AAAM,mBAAO,KAAK,MAAK;AAE3B,gBAAM,QAAQ,WAAW,KAAK,UAAU,KAAK,IAAI,IAAI;AAErD,eAAK,QAAQ,KAAK;QACpB,SAAS,KAAK;AACZ,eAAK,MAAM,GAAG;QAChB;MACF;MACA,MAAM,SAAM;;AACV,gBAAMC,MAAA,KAAK,WAAL,gBAAAA,IAAA;MACR;KACD;EACH;;AAGF,gBAAuB,iBACrB,UACA,YAA2B;AAE3B,MAAI,CAAC,SAAS,MAAM;AAClB,eAAW,MAAK;AAChB,QACE,OAAQ,WAAmB,cAAc,eACxC,WAAmB,UAAU,YAAY,eAC1C;AACA,YAAM,IAAI,eACR,gKAAgK;IAEpK;AACA,UAAM,IAAI,eAAe,mDAAmD;EAC9E;AAEA,QAAM,aAAa,IAAI,WAAU;AACjC,QAAM,cAAc,IAAI,YAAW;AAEnC,QAAM,OAAO,8BAAqC,SAAS,IAAI;AAC/D,mBAAiB,YAAY,cAAc,IAAI,GAAG;AAChD,eAAW,QAAQ,YAAY,OAAO,QAAQ,GAAG;AAC/C,YAAM,MAAM,WAAW,OAAO,IAAI;AAClC,UAAI;AAAK,cAAM;IACjB;EACF;AAEA,aAAW,QAAQ,YAAY,MAAK,GAAI;AACtC,UAAM,MAAM,WAAW,OAAO,IAAI;AAClC,QAAI;AAAK,YAAM;EACjB;AACF;AAMA,gBAAgB,cAAc,UAAsC;AAClE,MAAI,OAAO,IAAI,WAAU;AAEzB,mBAAiB,SAAS,UAAU;AAClC,QAAI,SAAS,MAAM;AACjB;IACF;AAEA,UAAM,cACJ,iBAAiB,cAAc,IAAI,WAAW,KAAK,IACjD,OAAO,UAAU,WAAW,WAAW,KAAK,IAC5C;AAEJ,QAAI,UAAU,IAAI,WAAW,KAAK,SAAS,YAAY,MAAM;AAC7D,YAAQ,IAAI,IAAI;AAChB,YAAQ,IAAI,aAAa,KAAK,MAAM;AACpC,WAAO;AAEP,QAAI;AACJ,YAAQ,eAAe,uBAAuB,IAAI,OAAO,IAAI;AAC3D,YAAM,KAAK,MAAM,GAAG,YAAY;AAChC,aAAO,KAAK,MAAM,YAAY;IAChC;EACF;AAEA,MAAI,KAAK,SAAS,GAAG;AACnB,UAAM;EACR;AACF;AAEA,IAAM,aAAN,MAAgB;EAKd,cAAA;AACE,SAAK,QAAQ;AACb,SAAK,OAAO,CAAA;AACZ,SAAK,SAAS,CAAA;EAChB;EAEA,OAAO,MAAY;AACjB,QAAI,KAAK,SAAS,IAAI,GAAG;AACvB,aAAO,KAAK,UAAU,GAAG,KAAK,SAAS,CAAC;IAC1C;AAEA,QAAI,CAAC,MAAM;AAET,UAAI,CAAC,KAAK,SAAS,CAAC,KAAK,KAAK;AAAQ,eAAO;AAE7C,YAAM,MAAuB;QAC3B,OAAO,KAAK;QACZ,MAAM,KAAK,KAAK,KAAK,IAAI;QACzB,KAAK,KAAK;;AAGZ,WAAK,QAAQ;AACb,WAAK,OAAO,CAAA;AACZ,WAAK,SAAS,CAAA;AAEd,aAAO;IACT;AAEA,SAAK,OAAO,KAAK,IAAI;AAErB,QAAI,KAAK,WAAW,GAAG,GAAG;AACxB,aAAO;IACT;AAEA,QAAI,CAAC,WAAW,GAAG,KAAK,IAAI,UAAU,MAAM,GAAG;AAE/C,QAAI,MAAM,WAAW,GAAG,GAAG;AACzB,cAAQ,MAAM,UAAU,CAAC;IAC3B;AAEA,QAAI,cAAc,SAAS;AACzB,WAAK,QAAQ;IACf,WAAW,cAAc,QAAQ;AAC/B,WAAK,KAAK,KAAK,KAAK;IACtB;AAEA,WAAO;EACT;;AAGF,SAAS,UAAU,KAAa,WAAiB;AAC/C,QAAM,QAAQ,IAAI,QAAQ,SAAS;AACnC,MAAI,UAAU,IAAI;AAChB,WAAO,CAAC,IAAI,UAAU,GAAG,KAAK,GAAG,WAAW,IAAI,UAAU,QAAQ,UAAU,MAAM,CAAC;EACrF;AAEA,SAAO,CAAC,KAAK,IAAI,EAAE;AACrB;;;ACzTA,eAAsB,qBACpB,QACA,OAAuB;AAEvB,QAAM,EAAE,UAAU,cAAc,qBAAqB,UAAS,IAAK;AACnE,QAAM,OAAO,OAAO,YAAW;AAtBjC,QAAAC;AAuBI,QAAI,MAAM,QAAQ,QAAQ;AACxB,gBAAU,MAAM,EAAE,MAAM,YAAY,SAAS,QAAQ,SAAS,KAAK,SAAS,SAAS,SAAS,IAAI;AAKlG,UAAI,MAAM,QAAQ,eAAe;AAC/B,eAAO,MAAM,QAAQ,cAAc,gBAAgB,UAAU,MAAM,UAAU;MAC/E;AAEA,aAAO,OAAO,gBAAgB,UAAU,MAAM,UAAU;IAC1D;AAGA,QAAI,SAAS,WAAW,KAAK;AAC3B,aAAO;IACT;AAEA,QAAI,MAAM,QAAQ,kBAAkB;AAClC,aAAO;IACT;AAEA,UAAM,cAAc,SAAS,QAAQ,IAAI,cAAc;AACvD,UAAM,aAAYA,MAAA,2CAAa,MAAM,KAAK,OAAxB,gBAAAA,IAA4B;AAC9C,UAAM,UAAS,uCAAW,SAAS,yBAAuB,uCAAW,SAAS;AAC9E,QAAI,QAAQ;AACV,YAAM,OAAO,MAAM,SAAS,KAAI;AAChC,aAAO,aAAa,MAAW,QAAQ;IACzC;AAEA,UAAM,OAAO,MAAM,SAAS,KAAI;AAChC,WAAO;EACT,GAAE;AACF,YAAU,MAAM,EAAE,MAChB,IAAI,YAAY,qBAChB,qBAAqB;IACnB;IACA,KAAK,SAAS;IACd,QAAQ,SAAS;IACjB;IACA,YAAY,KAAK,IAAG,IAAK;GAC1B,CAAC;AAEJ,SAAO;AACT;AAOM,SAAU,aAAgB,OAAU,UAAkB;AAC1D,MAAI,CAAC,SAAS,OAAO,UAAU,YAAY,MAAM,QAAQ,KAAK,GAAG;AAC/D,WAAO;EACT;AAEA,SAAO,OAAO,eAAe,OAAO,eAAe;IACjD,OAAO,SAAS,QAAQ,IAAI,YAAY;IACxC,YAAY;GACb;AACH;;;;ACnEM,IAAO,aAAP,MAAO,oBAAsB,QAAyB;EAI1D,YACE,QACQ,iBACA,gBAGgC,sBAAoB;AAE5D,UAAM,CAAC,YAAW;AAIhB,cAAQ,IAAW;IACrB,CAAC;AAXO,SAAA,kBAAA;AACA,SAAA,gBAAA;AALV,uBAAA,IAAA,MAAA,MAAA;AAgBE,2BAAA,MAAI,oBAAW,QAAM,GAAA;EACvB;EAEA,YAAe,WAAkD;AAC/D,WAAO,IAAI,YAAW,uBAAA,MAAI,oBAAA,GAAA,GAAU,KAAK,iBAAiB,OAAO,QAAQ,UACvE,aAAa,UAAU,MAAM,KAAK,cAAc,QAAQ,KAAK,GAAG,KAAK,GAAG,MAAM,QAAQ,CAAC;EAE3F;;;;;;;;;;;;EAaA,aAAU;AACR,WAAO,KAAK,gBAAgB,KAAK,CAAC,MAAM,EAAE,QAAQ;EACpD;;;;;;;;;;;;;EAcA,MAAM,eAAY;AAChB,UAAM,CAAC,MAAM,QAAQ,IAAI,MAAM,QAAQ,IAAI,CAAC,KAAK,MAAK,GAAI,KAAK,WAAU,CAAE,CAAC;AAC5E,WAAO,EAAE,MAAM,UAAU,YAAY,SAAS,QAAQ,IAAI,YAAY,EAAC;EACzE;EAEQ,QAAK;AACX,QAAI,CAAC,KAAK,eAAe;AACvB,WAAK,gBAAgB,KAAK,gBAAgB,KACxC,CAAC,SAAS,KAAK,cAAc,uBAAA,MAAI,oBAAA,GAAA,GAAU,IAAI,CAAqC;IAExF;AACA,WAAO,KAAK;EACd;EAES,KACP,aACA,YAAmF;AAEnF,WAAO,KAAK,MAAK,EAAG,KAAK,aAAa,UAAU;EAClD;EAES,MACP,YAAiF;AAEjF,WAAO,KAAK,MAAK,EAAG,MAAM,UAAU;EACtC;EAES,QAAQ,WAA2C;AAC1D,WAAO,KAAK,MAAK,EAAG,QAAQ,SAAS;EACvC;;;;;;ACvFI,IAAgB,eAAhB,MAA4B;EAOhC,YAAY,QAAuB,UAAoB,MAAe,SAA4B;AANlG,yBAAA,IAAA,MAAA,MAAA;AAOE,2BAAA,MAAI,sBAAW,QAAM,GAAA;AACrB,SAAK,UAAU;AACf,SAAK,WAAW;AAChB,SAAK,OAAO;EACd;EAMA,cAAW;AACT,UAAM,QAAQ,KAAK,kBAAiB;AACpC,QAAI,CAAC,MAAM;AAAQ,aAAO;AAC1B,WAAO,KAAK,uBAAsB,KAAM;EAC1C;EAEA,MAAM,cAAW;AACf,UAAM,cAAc,KAAK,uBAAsB;AAC/C,QAAI,CAAC,aAAa;AAChB,YAAM,IAAI,eACR,uFAAuF;IAE3F;AAEA,WAAO,MAAM,uBAAA,MAAI,sBAAA,GAAA,EAAS,eAAe,KAAK,aAAoB,WAAW;EAC/E;EAEA,OAAO,YAAS;AACd,QAAI,OAAa;AACjB,UAAM;AACN,WAAO,KAAK,YAAW,GAAI;AACzB,aAAO,MAAM,KAAK,YAAW;AAC7B,YAAM;IACR;EACF;EAEA,SAAO,uBAAA,oBAAA,QAAA,GAAC,OAAO,cAAa,IAAC;AAC3B,qBAAiB,QAAQ,KAAK,UAAS,GAAI;AACzC,iBAAW,QAAQ,KAAK,kBAAiB,GAAI;AAC3C,cAAM;MACR;IACF;EACF;;AAYI,IAAO,cAAP,cAII,WAAqB;EAG7B,YACE,QACA,SACAC,OAA4E;AAE5E,UACE,QACA,SACA,OAAOC,SAAQ,UACb,IAAID,MACFC,SACA,MAAM,UACN,MAAM,qBAAqBA,SAAQ,KAAK,GACxC,MAAM,OAAO,CACc;EAEnC;;;;;;;;EASA,QAAQ,OAAO,aAAa,IAAC;AAC3B,UAAM,OAAO,MAAM;AACnB,qBAAiB,QAAQ,MAAM;AAC7B,YAAM;IACR;EACF;;AAwBI,IAAO,OAAP,cAA0B,aAAkB;EAShD,YACE,QACA,UACA,MACA,SAA4B;AAE5B,UAAM,QAAQ,UAAU,MAAM,OAAO;AAErC,SAAK,OAAO,KAAK,QAAQ,CAAA;AACzB,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,WAAW,KAAK,YAAY;AACjC,SAAK,UAAU,KAAK,WAAW;EACjC;EAEA,oBAAiB;AACf,WAAO,KAAK,QAAQ,CAAA;EACtB;EAES,cAAW;AAClB,QAAI,KAAK,aAAa,OAAO;AAC3B,aAAO;IACT;AAEA,WAAO,MAAM,YAAW;EAC1B;EAEA,yBAAsB;AA1KxB,QAAAC;AA2KI,SAAKA,MAAA,KAAK,QAAQ,UAAb,gBAAAA,IAAiD,cAAc;AAElE,YAAM,WAAW,KAAK;AACtB,UAAI,CAAC,UAAU;AACb,eAAO;MACT;AAEA,aAAO;QACL,GAAG,KAAK;QACR,OAAO;UACL,GAAG,SAAS,KAAK,QAAQ,KAAK;UAC9B,WAAW;;;IAGjB;AAEA,UAAM,SAAS,KAAK;AACpB,QAAI,CAAC,QAAQ;AACX,aAAO;IACT;AAEA,WAAO;MACL,GAAG,KAAK;MACR,OAAO;QACL,GAAG,SAAS,KAAK,QAAQ,KAAK;QAC9B,UAAU;;;EAGhB;;;;AC1LK,IAAM,mBAAmB,MAAK;;AACnC,MAAI,OAAO,SAAS,aAAa;AAC/B,UAAM,EAAE,QAAO,IAAK;AACpB,UAAM,YACJ,SAAOC,MAAA,mCAAS,aAAT,gBAAAA,IAAmB,UAAS,YAAY,SAAS,QAAQ,SAAS,KAAK,MAAM,GAAG,CAAC,IAAI;AAC9F,UAAM,IAAI,MACR,4EACG,YACC,+FACA,GAAG;EAEX;AACF;AAiBM,SAAU,SACd,UACA,UACA,SAAyB;AAEzB,mBAAgB;AAChB,SAAO,IAAI,KAAK,UAAiB,YAAY,gBAAgB,OAAO;AACtE;AAEM,SAAU,QAAQ,OAAU;AAChC,UAEK,OAAO,UAAU,YAChB,UAAU,SACR,UAAU,SAAS,MAAM,QAAQ,OAAO,MAAM,IAAI,KACjD,SAAS,SAAS,MAAM,OAAO,OAAO,MAAM,GAAG,KAC/C,cAAc,SAAS,MAAM,YAAY,OAAO,MAAM,QAAQ,KAC9D,UAAU,SAAS,MAAM,QAAQ,OAAO,MAAM,IAAI,MACvD,IAEC,MAAM,OAAO,EACb,IAAG,KAAM;AAEhB;AAEO,IAAM,kBAAkB,CAAC,UAC9B,SAAS,QAAQ,OAAO,UAAU,YAAY,OAAO,MAAM,OAAO,aAAa,MAAM;AAiBhF,IAAM,8BAA8B,OACzC,MACAC,WAC2B;AAC3B,SAAO,EAAE,GAAG,MAAM,MAAM,MAAM,WAAW,KAAK,MAAMA,MAAK,EAAC;AAC5D;AAEA,IAAM,sBAAsB,oBAAI,QAAO;AAQvC,SAAS,iBAAiB,aAAkC;AAC1D,QAAMA,SAAe,OAAO,gBAAgB,aAAa,cAAe,YAAoB;AAC5F,QAAM,SAAS,oBAAoB,IAAIA,MAAK;AAC5C,MAAI;AAAQ,WAAO;AACnB,QAAM,WAAW,YAAW;AAC1B,QAAI;AACF,YAAM,gBACJ,cAAcA,SACZA,OAAM,YACL,MAAMA,OAAM,QAAQ,GAAG;AAC5B,YAAM,OAAO,IAAI,SAAQ;AACzB,UAAI,KAAK,SAAQ,MAAQ,MAAM,IAAI,cAAc,IAAI,EAAE,KAAI,GAAK;AAC9D,eAAO;MACT;AACA,aAAO;IACT,QAAQ;AAEN,aAAO;IACT;EACF,GAAE;AACF,sBAAoB,IAAIA,QAAO,OAAO;AACtC,SAAO;AACT;AAEO,IAAM,aAAa,OACxB,MACAA,WACqB;AACrB,MAAI,CAAE,MAAM,iBAAiBA,MAAK,GAAI;AACpC,UAAM,IAAI,UACR,mGAAmG;EAEvG;AACA,QAAM,OAAO,IAAI,SAAQ;AACzB,QAAM,QAAQ,IAAI,OAAO,QAAQ,QAAQ,CAAA,CAAE,EAAE,IAAI,CAAC,CAAC,KAAK,KAAK,MAAM,aAAa,MAAM,KAAK,KAAK,CAAC,CAAC;AAClG,SAAO;AACT;AAIA,IAAM,cAAc,CAAC,UAAiC,iBAAiB,QAAQ,UAAU;AAkBzF,IAAM,eAAe,OAAO,MAAgB,KAAa,UAAiC;AACxF,MAAI,UAAU;AAAW;AACzB,MAAI,SAAS,MAAM;AACjB,UAAM,IAAI,UACR,sBAAsB,GAAG,6DAA6D;EAE1F;AAGA,MAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AACxF,SAAK,OAAO,KAAK,OAAO,KAAK,CAAC;EAChC,WAAW,iBAAiB,UAAU;AACpC,QAAI,UAAU,CAAA;AACd,UAAM,cAAc,MAAM,QAAQ,IAAI,cAAc;AACpD,QAAI,aAAa;AACf,gBAAU,EAAE,MAAM,YAAW;IAC/B;AAEA,SAAK,OAAO,KAAK,SAAS,CAAC,MAAM,MAAM,KAAI,CAAE,GAAG,QAAQ,KAAK,GAAG,OAAO,CAAC;EAC1E,WAAW,gBAAgB,KAAK,GAAG;AACjC,SAAK,OAAO,KAAK,SAAS,CAAC,MAAM,IAAI,SAAS,mBAAmB,KAAK,CAAC,EAAE,KAAI,CAAE,GAAG,QAAQ,KAAK,CAAC,CAAC;EACnG,WAAW,YAAY,KAAK,GAAG;AAC7B,SAAK,OAAO,KAAK,SAAS,CAAC,KAAK,GAAG,QAAQ,KAAK,GAAG,EAAE,MAAM,MAAM,KAAI,CAAE,CAAC;EAC1E,WAAW,MAAM,QAAQ,KAAK,GAAG;AAC/B,UAAM,QAAQ,IAAI,MAAM,IAAI,CAAC,UAAU,aAAa,MAAM,MAAM,MAAM,KAAK,CAAC,CAAC;EAC/E,WAAW,OAAO,UAAU,UAAU;AACpC,UAAM,QAAQ,IACZ,OAAO,QAAQ,KAAK,EAAE,IAAI,CAAC,CAAC,MAAM,IAAI,MAAM,aAAa,MAAM,GAAG,GAAG,IAAI,IAAI,KAAK,IAAI,CAAC,CAAC;EAE5F,OAAO;AACL,UAAM,IAAI,UACR,wGAAwG,KAAK,UAAU;EAE3H;AACF;;;ACxKA,IAAM,aAAa,CAAC,UAClB,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,SAAS,cACtB,OAAO,MAAM,UAAU,cACvB,OAAO,MAAM,gBAAgB;AAe/B,IAAM,aAAa,CAAC,UAClB,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,SAAS,YACtB,OAAO,MAAM,iBAAiB,YAC9B,WAAW,KAAK;AAUlB,IAAM,iBAAiB,CAAC,UACtB,SAAS,QACT,OAAO,UAAU,YACjB,OAAO,MAAM,QAAQ,YACrB,OAAO,MAAM,SAAS;AAiBxB,eAAsB,OACpB,OACA,MACA,SAAqC;AAErC,mBAAgB;AAGhB,UAAQ,MAAM;AAEd,WAAA,OAAS,QAAQ,KAAK;AAItB,MAAI,WAAW,KAAK,GAAG;AACrB,QAAI,iBAAiB,QAAQ,QAAQ,QAAQ,WAAW,MAAM;AAC5D,aAAO;IACT;AACA,WAAO,SAAS,CAAC,MAAM,MAAM,YAAW,CAAE,GAAG,QAAQ,MAAM,MAAM;MAC/D,MAAM,MAAM;MACZ,cAAc,MAAM;MACpB,GAAG;KACJ;EACH;AAEA,MAAI,eAAe,KAAK,GAAG;AACzB,UAAM,OAAO,MAAM,MAAM,KAAI;AAC7B,aAAA,OAAS,IAAI,IAAI,MAAM,GAAG,EAAE,SAAS,MAAM,OAAO,EAAE,IAAG;AAEvD,WAAO,SAAS,MAAM,SAAS,IAAI,GAAG,MAAM,OAAO;EACrD;AAEA,QAAM,QAAQ,MAAM,SAAS,KAAK;AAElC,MAAI,EAAC,mCAAS,OAAM;AAClB,UAAM,OAAO,MAAM,KAAK,CAAC,SAAS,OAAO,SAAS,YAAY,UAAU,QAAQ,KAAK,IAAI;AACzF,QAAI,OAAO,SAAS,UAAU;AAC5B,gBAAU,EAAE,GAAG,SAAS,KAAI;IAC9B;EACF;AAEA,SAAO,SAAS,OAAO,MAAM,OAAO;AACtC;AAEA,eAAe,SAAS,OAAiD;;AACvE,MAAI,QAAyB,CAAA;AAC7B,MACE,OAAO,UAAU,YACjB,YAAY,OAAO,KAAK;EACxB,iBAAiB,aACjB;AACA,UAAM,KAAK,KAAK;EAClB,WAAW,WAAW,KAAK,GAAG;AAC5B,UAAM,KAAK,iBAAiB,OAAO,QAAQ,MAAM,MAAM,YAAW,CAAE;EACtE,WACE,gBAAgB,KAAK,GACrB;AACA,qBAAiB,SAAS,OAAO;AAC/B,YAAM,KAAK,GAAI,MAAM,SAAS,KAAqB,CAAE;IACvD;EACF,OAAO;AACL,UAAM,eAAcC,MAAA,+BAAO,gBAAP,gBAAAA,IAAoB;AACxC,UAAM,IAAI,MACR,yBAAyB,OAAO,KAAK,GACnC,cAAc,kBAAkB,WAAW,KAAK,EAClD,GAAG,cAAc,KAAK,CAAC,EAAE;EAE7B;AAEA,SAAO;AACT;AAEA,SAAS,cAAc,OAAc;AACnC,MAAI,OAAO,UAAU,YAAY,UAAU;AAAM,WAAO;AACxD,QAAM,QAAQ,OAAO,oBAAoB,KAAK;AAC9C,SAAO,aAAa,MAAM,IAAI,CAAC,MAAM,IAAI,CAAC,GAAG,EAAE,KAAK,IAAI,CAAC;AAC3D;;;AC1JM,IAAO,cAAP,MAAkB;EAGtB,YAAY,QAAqB;AAC/B,SAAK,UAAU;EACjB;;;;ACEF,IAAM,+BAA+B,OAAO,IAAI,8BAA8B;AAkB9E,IAAM,UAAU,MAAM;AAEtB,UAAU,eAAe,SAAoB;AAC3C,MAAI,CAAC;AAAS;AAEd,MAAI,gCAAgC,SAAS;AAC3C,UAAM,EAAE,QAAQ,MAAK,IAAK;AAC1B,WAAO,OAAO,QAAO;AACrB,eAAW,QAAQ,OAAO;AACxB,YAAM,CAAC,MAAM,IAAI;IACnB;AACA;EACF;AAEA,MAAI,cAAc;AAClB,MAAI;AACJ,MAAI,mBAAmB,SAAS;AAC9B,WAAO,QAAQ,QAAO;EACxB,WAAW,QAAQ,OAAO,GAAG;AAC3B,WAAO;EACT,OAAO;AACL,kBAAc;AACd,WAAO,OAAO,QAAQ,WAAW,CAAA,CAAE;EACrC;AACA,WAAS,OAAO,MAAM;AACpB,UAAM,OAAO,IAAI,CAAC;AAClB,QAAI,OAAO,SAAS;AAAU,YAAM,IAAI,UAAU,qCAAqC;AACvF,UAAM,SAAS,QAAQ,IAAI,CAAC,CAAC,IAAI,IAAI,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AACjD,QAAI,WAAW;AACf,eAAW,SAAS,QAAQ;AAC1B,UAAI,UAAU;AAAW;AAIzB,UAAI,eAAe,CAAC,UAAU;AAC5B,mBAAW;AACX,cAAM,CAAC,MAAM,IAAI;MACnB;AACA,YAAM,CAAC,MAAM,KAAK;IACpB;EACF;AACF;AAEO,IAAM,eAAe,CAAC,eAA8C;AACzE,QAAM,gBAAgB,IAAI,QAAO;AACjC,QAAM,cAAc,oBAAI,IAAG;AAC3B,aAAW,WAAW,YAAY;AAChC,UAAM,cAAc,oBAAI,IAAG;AAC3B,eAAW,CAAC,MAAM,KAAK,KAAK,eAAe,OAAO,GAAG;AACnD,YAAM,YAAY,KAAK,YAAW;AAClC,UAAI,CAAC,YAAY,IAAI,SAAS,GAAG;AAC/B,sBAAc,OAAO,IAAI;AACzB,oBAAY,IAAI,SAAS;MAC3B;AACA,UAAI,UAAU,MAAM;AAClB,sBAAc,OAAO,IAAI;AACzB,oBAAY,IAAI,SAAS;MAC3B,OAAO;AACL,sBAAc,OAAO,MAAM,KAAK;AAChC,oBAAY,OAAO,SAAS;MAC9B;IACF;EACF;AACA,SAAO,EAAE,CAAC,4BAA4B,GAAG,MAAM,QAAQ,eAAe,OAAO,YAAW;AAC1F;;;ACnFM,SAAU,cAAc,KAAW;AACvC,SAAO,IAAI,QAAQ,oCAAoC,kBAAkB;AAC3E;AAEO,IAAM,wBAAwB,CAAC,cAAc,kBAClD,SAASC,MAAK,YAA+B,QAA0B;AAErE,MAAI,QAAQ,WAAW;AAAG,WAAO,QAAQ,CAAC;AAE1C,MAAI,WAAW;AACf,QAAMA,QAAO,QAAQ,OAAO,CAAC,eAAe,cAAc,UAAS;AACjE,QAAI,OAAO,KAAK,YAAY,GAAG;AAC7B,iBAAW;IACb;AACA,WACE,gBACA,gBACC,UAAU,OAAO,SAAS,MAAM,WAAW,qBAAqB,aAAa,OAAO,OAAO,KAAK,CAAC,CAAC;EAEvG,GAAG,EAAE;AAEL,QAAM,WAAWA,MAAK,MAAM,QAAQ,CAAC,EAAE,CAAC;AACxC,QAAM,kBAAkB,CAAA;AACxB,QAAM,wBAAwB,WAAA,uCAAA,IAAoC;AAClE,MAAI;AAGJ,UAAQ,QAAQ,sBAAsB,KAAK,QAAQ,OAAO,MAAM;AAC9D,oBAAgB,KAAK;MACnB,OAAO,MAAM;MACb,QAAQ,MAAM,CAAC,EAAE;KAClB;EACH;AAEA,MAAI,gBAAgB,SAAS,GAAG;AAC9B,QAAI,UAAU;AACd,UAAM,YAAY,gBAAgB,OAAO,CAAC,KAAK,YAAW;AACxD,YAAM,SAAS,IAAI,OAAO,QAAQ,QAAQ,OAAO;AACjD,YAAM,SAAS,IAAI,OAAO,QAAQ,MAAM;AACxC,gBAAU,QAAQ,QAAQ,QAAQ;AAClC,aAAO,MAAM,SAAS;IACxB,GAAG,EAAE;AAEL,UAAM,IAAI,eACR;EAA0DA,KAAI;EAAK,SAAS,EAAE;EAElF;AAEA,SAAOA;AACT;AAKK,IAAM,OAAO,sBAAsB,aAAa;;;ACpDjD,IAAO,QAAP,cAAqB,YAAW;;;;;;;;;;;;EAYpC,KACE,SAA4C,CAAA,GAC5C,SAAwB;AAExB,UAAM,EAAE,OAAO,GAAG,MAAK,IAAK,UAAU,CAAA;AACtC,WAAO,KAAK,QAAQ,WAAW,aAAa,MAAoB;MAC9D;MACA,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,sBAAsB,EAAE,SAAQ,EAAE;QACzE,mCAAS;OACV;KACF;EACH;;;;;;;;;;;EAYA,OACE,QACA,SAA8C,CAAA,GAC9C,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,OAAO,iBAAiB,MAAM,IAAI;MACpD,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,sBAAsB,EAAE,SAAQ,EAAE;QACzE,mCAAS;OACV;KACF;EACH;;;;;;;;;;;;;;EAeA,SACE,QACA,SAAgD,CAAA,GAChD,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,IAAI,iBAAiB,MAAM,YAAY;MACzD,GAAG;MACH,SAAS,aAAa;QACpB;UACE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,sBAAsB,EAAE,SAAQ;UACrE,QAAQ;;QAEV,mCAAS;OACV;MACD,kBAAkB;KACnB;EACH;;;;;;;;;;EAWA,iBACE,QACA,SAAwD,CAAA,GACxD,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,IAAI,iBAAiB,MAAM,IAAI;MACjD,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,sBAAsB,EAAE,SAAQ,EAAE;QACzE,mCAAS;OACV;KACF;EACH;;;;;;;;;;;EAYA,OAAO,QAA0B,SAAwB;AACvD,UAAM,EAAE,OAAO,GAAG,KAAI,IAAK;AAC3B,WAAO,KAAK,QAAQ,KAClB,aACA,4BACE;MACE;MACA,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,sBAAsB,EAAE,SAAQ,EAAE;QACzE,mCAAS;OACV;OAEH,KAAK,OAAO,CACb;EAEL;;;;ACxII,IAAO,SAAP,cAAsB,YAAW;;;;;;;;;;;;;;EAcrC,SACE,SACA,SAAiD,CAAA,GACjD,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,IAAI,kBAAkB,OAAO,cAAc;MAC7D,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,IAAI,+BAAO,eAAc,OAAO,EAAE,kBAAkB,+BAAO,WAAU,IAAK,OAAU;QACtF,mCAAS;OACV;KACF;EACH;;;;;;;;;;;;;;;EAgBA,KACE,SAA6C,CAAA,GAC7C,SAAwB;AAExB,UAAM,EAAE,OAAO,GAAG,MAAK,IAAK,UAAU,CAAA;AACtC,WAAO,KAAK,QAAQ,WAAW,wBAAwB,MAAqB;MAC1E;MACA,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,IAAI,+BAAO,eAAc,OAAO,EAAE,kBAAkB,+BAAO,WAAU,IAAK,OAAU;QACtF,mCAAS;OACV;KACF;EACH;;;;AC9DI,IAAO,eAAP,MAAO,cAAY;EAGvB,YACU,UACR,YAA2B;AADnB,SAAA,WAAA;AAGR,SAAK,aAAa;EACpB;EAEQ,OAAO,UAAO;AACpB,UAAM,cAAc,IAAI,YAAW;AACnC,qBAAiB,SAAS,KAAK,UAAU;AACvC,iBAAW,QAAQ,YAAY,OAAO,KAAK,GAAG;AAC5C,cAAM,KAAK,MAAM,IAAI;MACvB;IACF;AAEA,eAAW,QAAQ,YAAY,MAAK,GAAI;AACtC,YAAM,KAAK,MAAM,IAAI;IACvB;EACF;EAEA,CAAC,OAAO,aAAa,IAAC;AACpB,WAAO,KAAK,QAAO;EACrB;EAEA,OAAO,aAAgB,UAAoB,YAA2B;AACpE,QAAI,CAAC,SAAS,MAAM;AAClB,iBAAW,MAAK;AAChB,UACE,OAAQ,WAAmB,cAAc,eACxC,WAAmB,UAAU,YAAY,eAC1C;AACA,cAAM,IAAI,eACR,gKAAgK;MAEpK;AACA,YAAM,IAAI,eAAe,mDAAmD;IAC9E;AAEA,WAAO,IAAI,cAAa,8BAAqC,SAAS,IAAI,GAAG,UAAU;EACzF;;;;ACjCI,IAAO,UAAP,cAAuB,YAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA8BtC,OAAO,QAA2B,SAAwB;AACxD,UAAM,EAAE,OAAO,GAAG,KAAI,IAAK;AAC3B,WAAO,KAAK,QAAQ,KAAK,kCAAkC;MACzD;MACA,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,4BAA4B,EAAE,SAAQ,EAAE;QAC/E,mCAAS;OACV;KACF;EACH;;;;;;;;;;;;;;;;;EAkBA,SACE,gBACA,SAAiD,CAAA,GACjD,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,IAAI,4BAA4B,cAAc,cAAc;MAC9E,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,4BAA4B,EAAE,SAAQ,EAAE;QAC/E,mCAAS;OACV;KACF;EACH;;;;;;;;;;;;;;;;EAiBA,KACE,SAA6C,CAAA,GAC7C,SAAwB;AAExB,UAAM,EAAE,OAAO,GAAG,MAAK,IAAK,UAAU,CAAA;AACtC,WAAO,KAAK,QAAQ,WAAW,kCAAkC,MAAwB;MACvF;MACA,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,4BAA4B,EAAE,SAAQ,EAAE;QAC/E,mCAAS;OACV;KACF;EACH;;;;;;;;;;;;;;;;;;EAmBA,OACE,gBACA,SAA+C,CAAA,GAC/C,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,OAAO,4BAA4B,cAAc,cAAc;MACjF,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,4BAA4B,EAAE,SAAQ,EAAE;QAC/E,mCAAS;OACV;KACF;EACH;;;;;;;;;;;;;;;;;;;;;;;EAwBA,OACE,gBACA,SAA+C,CAAA,GAC/C,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,KAAK,4BAA4B,cAAc,qBAAqB;MACtF,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,4BAA4B,EAAE,SAAQ,EAAE;QAC/E,mCAAS;OACV;KACF;EACH;;;;;;;;;;;;;;;;;;;EAoBA,MAAM,QACJ,gBACA,SAAyC,CAAA,GACzC,SAAwB;AAExB,UAAM,QAAQ,MAAM,KAAK,SAAS,cAAc;AAChD,QAAI,CAAC,MAAM,aAAa;AACtB,YAAM,IAAI,eACR,yDAAyD,MAAM,iBAAiB,MAAM,MAAM,EAAE,EAAE;IAEpG;AAEA,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QACT,IAAI,MAAM,aAAa;MACtB,GAAG;MACH,SAAS,aAAa;QACpB;UACE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,4BAA4B,EAAE,SAAQ;UAC3E,QAAQ;;QAEV,mCAAS;OACV;MACD,QAAQ;MACR,kBAAkB;KACnB,EACA,YAAY,CAAC,GAAG,UAAU,aAAa,aAAa,MAAM,UAAU,MAAM,UAAU,CAAC;EAG1F;;;;ACnOF,IAAM,WAAW,CAAC,UAA0B;AACxC,MAAI,UAAU;AACd,MAAI,SAAkB,CAAA;AAEtB,SAAO,UAAU,MAAM,QAAQ;AAC7B,QAAI,OAAO,MAAM,OAAO;AAExB,QAAI,SAAS,MAAM;AACjB;AACA;IACF;AAEA,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK;QACV,MAAM;QACN,OAAO;OACR;AAED;AACA;IACF;AAEA,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK;QACV,MAAM;QACN,OAAO;OACR;AAED;AACA;IACF;AAEA,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK;QACV,MAAM;QACN,OAAO;OACR;AAED;AACA;IACF;AAEA,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK;QACV,MAAM;QACN,OAAO;OACR;AAED;AACA;IACF;AAEA,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK;QACV,MAAM;QACN,OAAO;OACR;AAED;AACA;IACF;AAEA,QAAI,SAAS,KAAK;AAChB,aAAO,KAAK;QACV,MAAM;QACN,OAAO;OACR;AAED;AACA;IACF;AAEA,QAAI,SAAS,KAAK;AAChB,UAAI,QAAQ;AACZ,UAAI,gBAAgB;AAEpB,aAAO,MAAM,EAAE,OAAO;AAEtB,aAAO,SAAS,KAAK;AACnB,YAAI,YAAY,MAAM,QAAQ;AAC5B,0BAAgB;AAChB;QACF;AAEA,YAAI,SAAS,MAAM;AACjB;AACA,cAAI,YAAY,MAAM,QAAQ;AAC5B,4BAAgB;AAChB;UACF;AACA,mBAAS,OAAO,MAAM,OAAO;AAC7B,iBAAO,MAAM,EAAE,OAAO;QACxB,OAAO;AACL,mBAAS;AACT,iBAAO,MAAM,EAAE,OAAO;QACxB;MACF;AAEA,aAAO,MAAM,EAAE,OAAO;AAEtB,UAAI,CAAC,eAAe;AAClB,eAAO,KAAK;UACV,MAAM;UACN;SACD;MACH;AACA;IACF;AAEA,QAAI,aAAa;AACjB,QAAI,QAAQ,WAAW,KAAK,IAAI,GAAG;AACjC;AACA;IACF;AAEA,QAAI,UAAU;AACd,QAAK,QAAQ,QAAQ,KAAK,IAAI,KAAM,SAAS,OAAO,SAAS,KAAK;AAChE,UAAI,QAAQ;AAEZ,UAAI,SAAS,KAAK;AAChB,iBAAS;AACT,eAAO,MAAM,EAAE,OAAO;MACxB;AAEA,aAAQ,QAAQ,QAAQ,KAAK,IAAI,KAAM,SAAS,KAAK;AACnD,iBAAS;AACT,eAAO,MAAM,EAAE,OAAO;MACxB;AAEA,aAAO,KAAK;QACV,MAAM;QACN;OACD;AACD;IACF;AAEA,QAAI,UAAU;AACd,QAAI,QAAQ,QAAQ,KAAK,IAAI,GAAG;AAC9B,UAAI,QAAQ;AAEZ,aAAO,QAAQ,QAAQ,KAAK,IAAI,GAAG;AACjC,YAAI,YAAY,MAAM,QAAQ;AAC5B;QACF;AACA,iBAAS;AACT,eAAO,MAAM,EAAE,OAAO;MACxB;AAEA,UAAI,SAAS,UAAU,SAAS,WAAW,UAAU,QAAQ;AAC3D,eAAO,KAAK;UACV,MAAM;UACN;SACD;MACH,OAAO;AAEL;AACA;MACF;AACA;IACF;AAEA;EACF;AAEA,SAAO;AACT;AArKF,IAsKE,QAAQ,CAAC,WAA4B;AACnC,MAAI,OAAO,WAAW,GAAG;AACvB,WAAO;EACT;AAEA,MAAI,YAAY,OAAO,OAAO,SAAS,CAAC;AAExC,UAAQ,UAAU,MAAM;IACtB,KAAK;AACH,eAAS,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC;AAC1C,aAAO,MAAM,MAAM;AACnB;IACF,KAAK;AACH,UAAI,2BAA2B,UAAU,MAAM,UAAU,MAAM,SAAS,CAAC;AACzE,UAAI,6BAA6B,OAAO,6BAA6B,KAAK;AACxE,iBAAS,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC;AAC1C,eAAO,MAAM,MAAM;MACrB;IACF,KAAK;AACH,UAAI,0BAA0B,OAAO,OAAO,SAAS,CAAC;AACtD,WAAI,mEAAyB,UAAS,aAAa;AACjD,iBAAS,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC;AAC1C,eAAO,MAAM,MAAM;MACrB,YAAW,mEAAyB,UAAS,WAAW,wBAAwB,UAAU,KAAK;AAC7F,iBAAS,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC;AAC1C,eAAO,MAAM,MAAM;MACrB;AACA;IACF,KAAK;AACH,eAAS,OAAO,MAAM,GAAG,OAAO,SAAS,CAAC;AAC1C,aAAO,MAAM,MAAM;AACnB;EACJ;AAEA,SAAO;AACT;AAzMF,IA0ME,UAAU,CAAC,WAA4B;AACrC,MAAI,OAAiB,CAAA;AAErB,SAAO,IAAI,CAAC,UAAS;AACnB,QAAI,MAAM,SAAS,SAAS;AAC1B,UAAI,MAAM,UAAU,KAAK;AACvB,aAAK,KAAK,GAAG;MACf,OAAO;AACL,aAAK,OAAO,KAAK,YAAY,GAAG,GAAG,CAAC;MACtC;IACF;AACA,QAAI,MAAM,SAAS,SAAS;AAC1B,UAAI,MAAM,UAAU,KAAK;AACvB,aAAK,KAAK,GAAG;MACf,OAAO;AACL,aAAK,OAAO,KAAK,YAAY,GAAG,GAAG,CAAC;MACtC;IACF;EACF,CAAC;AAED,MAAI,KAAK,SAAS,GAAG;AACnB,SAAK,QAAO,EAAG,IAAI,CAAC,SAAQ;AAC1B,UAAI,SAAS,KAAK;AAChB,eAAO,KAAK;UACV,MAAM;UACN,OAAO;SACR;MACH,WAAW,SAAS,KAAK;AACvB,eAAO,KAAK;UACV,MAAM;UACN,OAAO;SACR;MACH;IACF,CAAC;EACH;AAEA,SAAO;AACT;AA/OF,IAgPE,WAAW,CAAC,WAA2B;AACrC,MAAI,SAAS;AAEb,SAAO,IAAI,CAAC,UAAS;AACnB,YAAQ,MAAM,MAAM;MAClB,KAAK;AACH,kBAAU,MAAM,MAAM,QAAQ;AAC9B;MACF;AACE,kBAAU,MAAM;AAChB;IACJ;EACF,CAAC;AAED,SAAO;AACT;AA/PF,IAgQE,eAAe,CAAC,UAA2B,KAAK,MAAM,SAAS,QAAQ,MAAM,SAAS,KAAK,CAAC,CAAC,CAAC,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;AC9NjG,IAAM,oBAAoB;AAEpB,IAAO,oBAAP,MAAO,mBAAiB;EAwB5B,cAAA;;AAvBA,SAAA,WAA+B,CAAA;AAC/B,SAAA,mBAAkC,CAAA;AAClC,8CAAA,IAAA,MAAA,MAAA;AAEA,SAAA,aAA8B,IAAI,gBAAe;AAEjD,wCAAA,IAAA,MAAA,MAAA;AACA,+CAAA,IAAA,MAAgE,MAAK;IAAE,CAAC;AACxE,8CAAA,IAAA,MAA2D,MAAK;IAAE,CAAC;AAEnE,kCAAA,IAAA,MAAA,MAAA;AACA,yCAAA,IAAA,MAAiC,MAAK;IAAE,CAAC;AACzC,wCAAA,IAAA,MAAqD,MAAK;IAAE,CAAC;AAE7D,iCAAA,IAAA,MAA4F,CAAA,CAAE;AAE9F,6BAAA,IAAA,MAAS,KAAK;AACd,+BAAA,IAAA,MAAW,KAAK;AAChB,+BAAA,IAAA,MAAW,KAAK;AAChB,8CAAA,IAAA,MAA0B,KAAK;AAC/B,gCAAA,IAAA,MAAA,MAAA;AACA,kCAAA,IAAA,MAAA,MAAA;AA6QA,mCAAA,IAAA,MAAe,CAAC,UAAkB;AAChC,6BAAA,MAAI,4BAAY,MAAI,GAAA;AACpB,UAAI,aAAa,KAAK,GAAG;AACvB,gBAAQ,IAAI,kBAAiB;MAC/B;AACA,UAAI,iBAAiB,mBAAmB;AACtC,+BAAA,MAAI,4BAAY,MAAI,GAAA;AACpB,eAAO,KAAK,MAAM,SAAS,KAAK;MAClC;AACA,UAAI,iBAAiB,gBAAgB;AACnC,eAAO,KAAK,MAAM,SAAS,KAAK;MAClC;AACA,UAAI,iBAAiB,OAAO;AAC1B,cAAM,iBAAiC,IAAI,eAAe,MAAM,OAAO;AAEvE,uBAAe,QAAQ;AACvB,eAAO,KAAK,MAAM,SAAS,cAAc;MAC3C;AACA,aAAO,KAAK,MAAM,SAAS,IAAI,eAAe,OAAO,KAAK,CAAC,CAAC;IAC9D,CAAC;AA7RC,2BAAA,MAAI,qCAAqB,IAAI,QAAyB,CAAC,SAAS,WAAU;AACxE,6BAAA,MAAI,4CAA4B,SAAO,GAAA;AACvC,6BAAA,MAAI,2CAA2B,QAAM,GAAA;IACvC,CAAC,GAAC,GAAA;AAEF,2BAAA,MAAI,+BAAe,IAAI,QAAc,CAAC,SAAS,WAAU;AACvD,6BAAA,MAAI,sCAAsB,SAAO,GAAA;AACjC,6BAAA,MAAI,qCAAqB,QAAM,GAAA;IACjC,CAAC,GAAC,GAAA;AAMF,2BAAA,MAAI,qCAAA,GAAA,EAAmB,MAAM,MAAK;IAAE,CAAC;AACrC,2BAAA,MAAI,+BAAA,GAAA,EAAa,MAAM,MAAK;IAAE,CAAC;EACjC;EAEA,IAAI,WAAQ;AACV,WAAO,uBAAA,MAAI,6BAAA,GAAA;EACb;EAEA,IAAI,aAAU;AACZ,WAAO,uBAAA,MAAI,+BAAA,GAAA;EACb;;;;;;;;;;;EAYA,MAAM,eAAY;AAKhB,UAAM,WAAW,MAAM,uBAAA,MAAI,qCAAA,GAAA;AAC3B,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,uCAAuC;IACzD;AAEA,WAAO;MACL,MAAM;MACN;MACA,YAAY,SAAS,QAAQ,IAAI,YAAY;;EAEjD;;;;;;;;EASA,OAAO,mBAAmB,QAAsB;AAC9C,UAAM,SAAS,IAAI,mBAAiB;AACpC,WAAO,KAAK,MAAM,OAAO,oBAAoB,MAAM,CAAC;AACpD,WAAO;EACT;EAEA,OAAO,cACL,UACA,QACA,SAAwB;AAExB,UAAM,SAAS,IAAI,mBAAiB;AACpC,eAAW,WAAW,OAAO,UAAU;AACrC,aAAO,iBAAiB,OAAO;IACjC;AACA,WAAO,KAAK,MACV,OAAO,eACL,UACA,EAAE,GAAG,QAAQ,QAAQ,KAAI,GACzB,EAAE,GAAG,SAAS,SAAS,EAAE,GAAG,mCAAS,SAAS,6BAA6B,SAAQ,EAAE,CAAE,CACxF;AAEH,WAAO;EACT;EAEU,KAAK,UAA4B;AACzC,aAAQ,EAAG,KAAK,MAAK;AACnB,WAAK,WAAU;AACf,WAAK,MAAM,KAAK;IAClB,GAAG,uBAAA,MAAI,gCAAA,GAAA,CAAa;EACtB;EAEU,iBAAiB,SAAyB;AAClD,SAAK,SAAS,KAAK,OAAO;EAC5B;EAEU,YAAY,SAAsB,OAAO,MAAI;AACrD,SAAK,iBAAiB,KAAK,OAAO;AAClC,QAAI,MAAM;AACR,WAAK,MAAM,WAAW,OAAO;IAC/B;EACF;EAEU,MAAM,eACd,UACA,QACA,SAAwB;;AAExB,UAAM,SAAS,mCAAS;AACxB,QAAI,QAAQ;AACV,UAAI,OAAO;AAAS,aAAK,WAAW,MAAK;AACzC,aAAO,iBAAiB,SAAS,MAAM,KAAK,WAAW,MAAK,CAAE;IAChE;AACA,2BAAA,MAAI,8BAAA,KAAA,+BAAA,EAAc,KAAlB,IAAI;AACJ,UAAM,EAAE,UAAU,MAAM,OAAM,IAAK,MAAM,SACtC,OAAO,EAAE,GAAG,QAAQ,QAAQ,KAAI,GAAI,EAAE,GAAG,SAAS,QAAQ,KAAK,WAAW,OAAM,CAAE,EAClF,aAAY;AACf,SAAK,WAAW,QAAQ;AACxB,qBAAiB,SAAS,QAAQ;AAChC,6BAAA,MAAI,8BAAA,KAAA,iCAAA,EAAgB,KAApB,MAAqB,KAAK;IAC5B;AACA,SAAIC,MAAA,OAAO,WAAW,WAAlB,gBAAAA,IAA0B,SAAS;AACrC,YAAM,IAAI,kBAAiB;IAC7B;AACA,2BAAA,MAAI,8BAAA,KAAA,6BAAA,EAAY,KAAhB,IAAI;EACN;EAEU,WAAW,UAAyB;AAC5C,QAAI,KAAK;AAAO;AAChB,2BAAA,MAAI,6BAAa,UAAQ,GAAA;AACzB,2BAAA,MAAI,+BAAe,qCAAU,QAAQ,IAAI,eAAa,GAAA;AACtD,2BAAA,MAAI,4CAAA,GAAA,EAAyB,KAA7B,MAA8B,QAAQ;AACtC,SAAK,MAAM,SAAS;EACtB;EAEA,IAAI,QAAK;AACP,WAAO,uBAAA,MAAI,0BAAA,GAAA;EACb;EAEA,IAAI,UAAO;AACT,WAAO,uBAAA,MAAI,4BAAA,GAAA;EACb;EAEA,IAAI,UAAO;AACT,WAAO,uBAAA,MAAI,4BAAA,GAAA;EACb;EAEA,QAAK;AACH,SAAK,WAAW,MAAK;EACvB;;;;;;;;EASA,GAA4C,OAAc,UAAoC;AAC5F,UAAM,YACJ,uBAAA,MAAI,8BAAA,GAAA,EAAY,KAAK,MAAM,uBAAA,MAAI,8BAAA,GAAA,EAAY,KAAK,IAAI,CAAA;AACtD,cAAU,KAAK,EAAE,SAAQ,CAAE;AAC3B,WAAO;EACT;;;;;;;;EASA,IAA6C,OAAc,UAAoC;AAC7F,UAAM,YAAY,uBAAA,MAAI,8BAAA,GAAA,EAAY,KAAK;AACvC,QAAI,CAAC;AAAW,aAAO;AACvB,UAAM,QAAQ,UAAU,UAAU,CAAC,MAAM,EAAE,aAAa,QAAQ;AAChE,QAAI,SAAS;AAAG,gBAAU,OAAO,OAAO,CAAC;AACzC,WAAO;EACT;;;;;;EAOA,KAA8C,OAAc,UAAoC;AAC9F,UAAM,YACJ,uBAAA,MAAI,8BAAA,GAAA,EAAY,KAAK,MAAM,uBAAA,MAAI,8BAAA,GAAA,EAAY,KAAK,IAAI,CAAA;AACtD,cAAU,KAAK,EAAE,UAAU,MAAM,KAAI,CAAE;AACvC,WAAO;EACT;;;;;;;;;;;;EAaA,QACE,OAAY;AAMZ,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,6BAAA,MAAI,2CAA2B,MAAI,GAAA;AACnC,UAAI,UAAU;AAAS,aAAK,KAAK,SAAS,MAAM;AAChD,WAAK,KAAK,OAAO,OAAc;IACjC,CAAC;EACH;EAEA,MAAM,OAAI;AACR,2BAAA,MAAI,2CAA2B,MAAI,GAAA;AACnC,UAAM,uBAAA,MAAI,+BAAA,GAAA;EACZ;EAEA,IAAI,iBAAc;AAChB,WAAO,uBAAA,MAAI,2CAAA,GAAA;EACb;;;;;EAaA,MAAM,eAAY;AAChB,UAAM,KAAK,KAAI;AACf,WAAO,uBAAA,MAAI,8BAAA,KAAA,kCAAA,EAAiB,KAArB,IAAI;EACb;;;;;;EAqBA,MAAM,YAAS;AACb,UAAM,KAAK,KAAI;AACf,WAAO,uBAAA,MAAI,8BAAA,KAAA,+BAAA,EAAc,KAAlB,IAAI;EACb;EAuBU,MACR,UACG,MAA4C;AAG/C,QAAI,uBAAA,MAAI,0BAAA,GAAA;AAAS;AAEjB,QAAI,UAAU,OAAO;AACnB,6BAAA,MAAI,0BAAU,MAAI,GAAA;AAClB,6BAAA,MAAI,sCAAA,GAAA,EAAmB,KAAvB,IAAI;IACN;AAEA,UAAM,YAA4D,uBAAA,MAAI,8BAAA,GAAA,EAAY,KAAK;AACvF,QAAI,WAAW;AACb,6BAAA,MAAI,8BAAA,GAAA,EAAY,KAAK,IAAI,UAAU,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI;AACxD,gBAAU,QAAQ,CAAC,EAAE,SAAQ,MAAY,SAAS,GAAG,IAAI,CAAC;IAC5D;AAEA,QAAI,UAAU,SAAS;AACrB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,CAAC,uBAAA,MAAI,2CAAA,GAAA,KAA4B,EAAC,uCAAW,SAAQ;AACvD,gBAAQ,OAAO,KAAK;MACtB;AACA,6BAAA,MAAI,2CAAA,GAAA,EAAwB,KAA5B,MAA6B,KAAK;AAClC,6BAAA,MAAI,qCAAA,GAAA,EAAkB,KAAtB,MAAuB,KAAK;AAC5B,WAAK,MAAM,KAAK;AAChB;IACF;AAEA,QAAI,UAAU,SAAS;AAGrB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,CAAC,uBAAA,MAAI,2CAAA,GAAA,KAA4B,EAAC,uCAAW,SAAQ;AAOvD,gBAAQ,OAAO,KAAK;MACtB;AACA,6BAAA,MAAI,2CAAA,GAAA,EAAwB,KAA5B,MAA6B,KAAK;AAClC,6BAAA,MAAI,qCAAA,GAAA,EAAkB,KAAtB,MAAuB,KAAK;AAC5B,WAAK,MAAM,KAAK;IAClB;EACF;EAEU,aAAU;AAClB,UAAM,eAAe,KAAK,iBAAiB,GAAG,EAAE;AAChD,QAAI,cAAc;AAChB,WAAK,MAAM,gBAAgB,uBAAA,MAAI,8BAAA,KAAA,kCAAA,EAAiB,KAArB,IAAI,CAAmB;IACpD;EACF;EAgFU,MAAM,oBACd,gBACA,SAAwB;;AAExB,UAAM,SAAS,mCAAS;AACxB,QAAI,QAAQ;AACV,UAAI,OAAO;AAAS,aAAK,WAAW,MAAK;AACzC,aAAO,iBAAiB,SAAS,MAAM,KAAK,WAAW,MAAK,CAAE;IAChE;AACA,2BAAA,MAAI,8BAAA,KAAA,+BAAA,EAAc,KAAlB,IAAI;AACJ,SAAK,WAAW,IAAI;AACpB,UAAM,SAAS,OAAO,mBAA2C,gBAAgB,KAAK,UAAU;AAChG,qBAAiB,SAAS,QAAQ;AAChC,6BAAA,MAAI,8BAAA,KAAA,iCAAA,EAAgB,KAApB,MAAqB,KAAK;IAC5B;AACA,SAAIA,MAAA,OAAO,WAAW,WAAlB,gBAAAA,IAA0B,SAAS;AACrC,YAAM,IAAI,kBAAiB;IAC7B;AACA,2BAAA,MAAI,8BAAA,KAAA,6BAAA,EAAY,KAAhB,IAAI;EACN;EA6GA,EAAA,4CAAA,oBAAA,QAAA,GAAA,sCAAA,oBAAA,QAAA,GAAA,6CAAA,oBAAA,QAAA,GAAA,4CAAA,oBAAA,QAAA,GAAA,gCAAA,oBAAA,QAAA,GAAA,uCAAA,oBAAA,QAAA,GAAA,sCAAA,oBAAA,QAAA,GAAA,+BAAA,oBAAA,QAAA,GAAA,2BAAA,oBAAA,QAAA,GAAA,6BAAA,oBAAA,QAAA,GAAA,6BAAA,oBAAA,QAAA,GAAA,4CAAA,oBAAA,QAAA,GAAA,8BAAA,oBAAA,QAAA,GAAA,gCAAA,oBAAA,QAAA,GAAA,iCAAA,oBAAA,QAAA,GAAA,+BAAA,oBAAA,QAAA,GAAA,qCAAA,SAAAC,sCAAA;AAjUE,QAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,YAAM,IAAI,eAAe,8DAA8D;IACzF;AACA,WAAO,KAAK,iBAAiB,GAAG,EAAE;EACpC,GAAC,kCAAA,SAAAC,mCAAA;AAYC,QAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,YAAM,IAAI,eAAe,8DAA8D;IACzF;AACA,UAAM,aAAa,KAAK,iBACrB,GAAG,EAAE,EACL,QAAQ,OAAO,CAAC,UAAkC,MAAM,SAAS,MAAM,EACvE,IAAI,CAAC,UAAU,MAAM,IAAI;AAC5B,QAAI,WAAW,WAAW,GAAG;AAC3B,YAAM,IAAI,eAAe,+DAA+D;IAC1F;AACA,WAAO,WAAW,KAAK,GAAG;EAC5B,GAAC,kCAAA,SAAAC,mCAAA;AAyFC,QAAI,KAAK;AAAO;AAChB,2BAAA,MAAI,2CAA2B,QAAS,GAAA;EAC1C,GAAC,oCAAA,SAAAC,mCACe,OAA6B;AAC3C,QAAI,KAAK;AAAO;AAChB,UAAM,kBAAkB,uBAAA,MAAI,8BAAA,KAAA,oCAAA,EAAmB,KAAvB,MAAwB,KAAK;AACrD,SAAK,MAAM,eAAe,OAAO,eAAe;AAEhD,YAAQ,MAAM,MAAM;MAClB,KAAK,uBAAuB;AAC1B,cAAM,UAAU,gBAAgB,QAAQ,GAAG,EAAE;AAC7C,gBAAQ,MAAM,MAAM,MAAM;UACxB,KAAK,cAAc;AACjB,gBAAI,QAAQ,SAAS,QAAQ;AAC3B,mBAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,QAAQ,QAAQ,EAAE;YACzD;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,gBAAI,QAAQ,SAAS,QAAQ;AAC3B,mBAAK,MAAM,YAAY,MAAM,MAAM,UAAU,QAAQ,aAAa,CAAA,CAAE;YACtE;AACA;UACF;UACA,KAAK,oBAAoB;AACvB,iBAAK,QAAQ,SAAS,cAAc,QAAQ,SAAS,mBAAmB,QAAQ,OAAO;AACrF,mBAAK,MAAM,aAAa,MAAM,MAAM,cAAc,QAAQ,KAAK;YACjE;AACA;UACF;UACA,KAAK,kBAAkB;AACrB,gBAAI,QAAQ,SAAS,YAAY;AAC/B,mBAAK,MAAM,YAAY,MAAM,MAAM,UAAU,QAAQ,QAAQ;YAC/D;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,gBAAI,QAAQ,SAAS,YAAY;AAC/B,mBAAK,MAAM,aAAa,QAAQ,SAAS;YAC3C;AACA;UACF;UACA;AACE,uBAAW,MAAM,KAAK;QAC1B;AACA;MACF;MACA,KAAK,gBAAgB;AACnB,aAAK,iBAAiB,eAAe;AACrC,aAAK,YAAY,iBAAiB,IAAI;AACtC;MACF;MACA,KAAK,sBAAsB;AACzB,aAAK,MAAM,gBAAgB,gBAAgB,QAAQ,GAAG,EAAE,CAAE;AAC1D;MACF;MACA,KAAK,iBAAiB;AACpB,+BAAA,MAAI,2CAA2B,iBAAe,GAAA;AAC9C;MACF;MACA,KAAK;MACL,KAAK;AACH;IACJ;EACF,GAAC,gCAAA,SAAAC,iCAAA;AAEC,QAAI,KAAK,OAAO;AACd,YAAM,IAAI,eAAe,yCAAyC;IACpE;AACA,UAAM,WAAW,uBAAA,MAAI,2CAAA,GAAA;AACrB,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eAAe,0CAA0C;IACrE;AACA,2BAAA,MAAI,2CAA2B,QAAS,GAAA;AACxC,WAAO;EACT,GAAC,uCAAA,SAAAC,sCA4BkB,OAA6B;AAC9C,QAAI,WAAW,uBAAA,MAAI,2CAAA,GAAA;AAEnB,QAAI,MAAM,SAAS,iBAAiB;AAClC,UAAI,UAAU;AACZ,cAAM,IAAI,eAAe,+BAA+B,MAAM,IAAI,kCAAkC;MACtG;AACA,aAAO,MAAM;IACf;AAEA,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eAAe,+BAA+B,MAAM,IAAI,yBAAyB;IAC7F;AAEA,YAAQ,MAAM,MAAM;MAClB,KAAK;AACH,eAAO;MACT,KAAK;AACH,iBAAS,YAAY,MAAM,MAAM;AACjC,iBAAS,cAAc,MAAM,MAAM;AACnC,iBAAS,gBAAgB,MAAM,MAAM;AACrC,iBAAS,MAAM,gBAAgB,MAAM,MAAM;AAE3C,YAAI,MAAM,MAAM,gBAAgB,MAAM;AACpC,mBAAS,MAAM,eAAe,MAAM,MAAM;QAC5C;AAEA,YAAI,MAAM,MAAM,+BAA+B,MAAM;AACnD,mBAAS,MAAM,8BAA8B,MAAM,MAAM;QAC3D;AAEA,YAAI,MAAM,MAAM,2BAA2B,MAAM;AAC/C,mBAAS,MAAM,0BAA0B,MAAM,MAAM;QACvD;AAEA,YAAI,MAAM,MAAM,mBAAmB,MAAM;AACvC,mBAAS,MAAM,kBAAkB,MAAM,MAAM;QAC/C;AAEA,eAAO;MACT,KAAK;AACH,iBAAS,QAAQ,KAAK,MAAM,aAAa;AACzC,eAAO;MACT,KAAK,uBAAuB;AAC1B,cAAM,kBAAkB,SAAS,QAAQ,GAAG,MAAM,KAAK;AAEvD,gBAAQ,MAAM,MAAM,MAAM;UACxB,KAAK,cAAc;AACjB,iBAAI,mDAAiB,UAAS,QAAQ;AACpC,8BAAgB,QAAQ,MAAM,MAAM;YACtC;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,iBAAI,mDAAiB,UAAS,QAAQ;AACpC,8BAAgB,cAAhB,gBAAgB,YAAc,CAAA;AAC9B,8BAAgB,UAAU,KAAK,MAAM,MAAM,QAAQ;YACrD;AACA;UACF;UACA,KAAK,oBAAoB;AACvB,iBAAI,mDAAiB,UAAS,eAAc,mDAAiB,UAAS,gBAAgB;AAIpF,kBAAI,UAAW,gBAAwB,iBAAiB,KAAK;AAC7D,yBAAW,MAAM,MAAM;AAEvB,qBAAO,eAAe,iBAAiB,mBAAmB;gBACxD,OAAO;gBACP,YAAY;gBACZ,UAAU;eACX;AAED,kBAAI,SAAS;AACX,gCAAgB,QAAQ,aAAa,OAAO;cAC9C;YACF;AACA;UACF;UACA,KAAK,kBAAkB;AACrB,iBAAI,mDAAiB,UAAS,YAAY;AACxC,8BAAgB,YAAY,MAAM,MAAM;YAC1C;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,iBAAI,mDAAiB,UAAS,YAAY;AACxC,8BAAgB,YAAY,MAAM,MAAM;YAC1C;AACA;UACF;UACA;AACE,uBAAW,MAAM,KAAK;QAC1B;AACA,eAAO;MACT;MACA,KAAK;AACH,eAAO;IACX;EACF,GAEC,OAAO,cAAa,IAAC;AACpB,UAAM,YAAsC,CAAA;AAC5C,UAAM,YAGA,CAAA;AACN,QAAI,OAAO;AAEX,SAAK,GAAG,eAAe,CAAC,UAAS;AAC/B,YAAM,SAAS,UAAU,MAAK;AAC9B,UAAI,QAAQ;AACV,eAAO,QAAQ,KAAK;MACtB,OAAO;AACL,kBAAU,KAAK,KAAK;MACtB;IACF,CAAC;AAED,SAAK,GAAG,OAAO,MAAK;AAClB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,QAAQ,MAAS;MAC1B;AACA,gBAAU,SAAS;IACrB,CAAC;AAED,SAAK,GAAG,SAAS,CAAC,QAAO;AACvB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,OAAO,GAAG;MACnB;AACA,gBAAU,SAAS;IACrB,CAAC;AAED,SAAK,GAAG,SAAS,CAAC,QAAO;AACvB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,OAAO,GAAG;MACnB;AACA,gBAAU,SAAS;IACrB,CAAC;AAED,WAAO;MACL,MAAM,YAA4D;AAChE,YAAI,CAAC,UAAU,QAAQ;AACrB,cAAI,MAAM;AACR,mBAAO,EAAE,OAAO,QAAW,MAAM,KAAI;UACvC;AACA,iBAAO,IAAI,QAA4C,CAAC,SAAS,WAC/D,UAAU,KAAK,EAAE,SAAS,OAAM,CAAE,CAAC,EACnC,KAAK,CAACC,WAAWA,SAAQ,EAAE,OAAOA,QAAO,MAAM,MAAK,IAAK,EAAE,OAAO,QAAW,MAAM,KAAI,CAAG;QAC9F;AACA,cAAM,QAAQ,UAAU,MAAK;AAC7B,eAAO,EAAE,OAAO,OAAO,MAAM,MAAK;MACpC;MACA,QAAQ,YAAW;AACjB,aAAK,MAAK;AACV,eAAO,EAAE,OAAO,QAAW,MAAM,KAAI;MACvC;;EAEJ;EAEA,mBAAgB;AACd,UAAM,SAAS,IAAI,OAAO,KAAK,OAAO,aAAa,EAAE,KAAK,IAAI,GAAG,KAAK,UAAU;AAChF,WAAO,OAAO,iBAAgB;EAChC;;AAIF,SAAS,WAAW,GAAQ;AAAG;;;ACrqBxB,IAAM,4BAAoD;EAC/D,0BAA0B;EAC1B,mBAAmB;EACnB,0BAA0B;EAC1B,yCAAyC;EACzC,0BAA0B;;;;ACuB5B,IAAM,oBAEF;EACF,cAAc;EACd,mBAAmB;EACnB,sBAAsB;EACtB,2BAA2B;EAC3B,sBAAsB;EACtB,4BAA4B;EAC5B,cAAc;EACd,cAAc;;AAIV,IAAO,WAAP,cAAwB,YAAW;EAAzC,cAAA;;AACE,SAAA,UAA8B,IAAe,QAAQ,KAAK,OAAO;EAmGnE;EAtEE,OACE,QACA,SAAwB;AAExB,UAAM,EAAE,OAAO,GAAG,KAAI,IAAK;AAE3B,QAAI,KAAK,SAAS,mBAAmB;AACnC,cAAQ,KACN,cAAc,KAAK,KAAK,iDACtB,kBAAkB,KAAK,KAAK,CAC9B;6HAAgI;IAEpI;AAEA,QAAI,UAAW,KAAK,QAAgB,SAAS;AAC7C,QAAI,CAAC,KAAK,UAAU,WAAW,MAAM;AACnC,YAAM,wBAAwB,0BAA0B,KAAK,KAAK,KAAK;AACvE,gBAAU,KAAK,QAAQ,6BAA6B,KAAK,YAAY,qBAAqB;IAC5F;AACA,WAAO,KAAK,QAAQ,KAAK,0BAA0B;MACjD;MACA,SAAS,WAAW;MACpB,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,IAAI,+BAAO,eAAc,OAAO,EAAE,kBAAkB,+BAAO,WAAU,IAAK,OAAU;QACtF,mCAAS;OACV;MACD,QAAQ,OAAO,UAAU;KAC1B;EACH;;;;EAKA,OAAO,MAA+B,SAAwB;AAC5D,WAAO,kBAAkB,cAAc,MAAM,MAAM,OAAO;EAC5D;;;;;;;;;;;;;;;;;;;EAoBA,YACE,QACA,SAAwB;AAExB,UAAM,EAAE,OAAO,GAAG,KAAI,IAAK;AAC3B,WAAO,KAAK,QAAQ,KAAK,uCAAuC;MAC9D;MACA,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,kBAAkB,CAAC,GAAI,SAAS,CAAA,GAAK,2BAA2B,EAAE,SAAQ,EAAE;QAC9E,mCAAS;OACV;KACF;EACH;;AA+6DF,SAAS,UAAU;;;AC77Db,IAAO,OAAP,cAAoB,YAAW;EAArC,cAAA;;AACE,SAAA,SAA2B,IAAc,OAAO,KAAK,OAAO;AAC5D,SAAA,WAAiC,IAAgB,SAAS,KAAK,OAAO;AACtE,SAAA,QAAwB,IAAa,MAAM,KAAK,OAAO;EACzD;;AA0FA,KAAK,SAAS;AACd,KAAK,WAAW;AAChB,KAAK,QAAQ;;;ACzNP,IAAO,cAAP,cAA2B,YAAW;EA0B1C,OACE,QACA,SAAwB;AAExB,UAAM,EAAE,OAAO,GAAG,KAAI,IAAK;AAC3B,WAAO,KAAK,QAAQ,KAAK,gBAAgB;MACvC;MACA,SAAU,KAAK,QAAgB,SAAS,WAAW;MACnD,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,IAAI,+BAAO,eAAc,OAAO,EAAE,kBAAkB,+BAAO,WAAU,IAAK,OAAU;QACtF,mCAAS;OACV;MACD,QAAQ,OAAO,UAAU;KAC1B;EACH;;;;;;;;;;;;;;;;;;;;;;;;;;ACbF,IAAMC,qBAAoB;AAEpB,IAAO,gBAAP,MAAO,eAAa;EAwBxB,cAAA;;AAvBA,SAAA,WAA2B,CAAA;AAC3B,SAAA,mBAA8B,CAAA;AAC9B,0CAAA,IAAA,MAAA,MAAA;AAEA,SAAA,aAA8B,IAAI,gBAAe;AAEjD,oCAAA,IAAA,MAAA,MAAA;AACA,2CAAA,IAAA,MAAgE,MAAK;IAAE,CAAC;AACxE,0CAAA,IAAA,MAA2D,MAAK;IAAE,CAAC;AAEnE,8BAAA,IAAA,MAAA,MAAA;AACA,qCAAA,IAAA,MAAiC,MAAK;IAAE,CAAC;AACzC,oCAAA,IAAA,MAAqD,MAAK;IAAE,CAAC;AAE7D,6BAAA,IAAA,MAA4F,CAAA,CAAE;AAE9F,yBAAA,IAAA,MAAS,KAAK;AACd,2BAAA,IAAA,MAAW,KAAK;AAChB,2BAAA,IAAA,MAAW,KAAK;AAChB,0CAAA,IAAA,MAA0B,KAAK;AAC/B,4BAAA,IAAA,MAAA,MAAA;AACA,8BAAA,IAAA,MAAA,MAAA;AA6QA,+BAAA,IAAA,MAAe,CAAC,UAAkB;AAChC,6BAAA,MAAI,wBAAY,MAAI,GAAA;AACpB,UAAI,aAAa,KAAK,GAAG;AACvB,gBAAQ,IAAI,kBAAiB;MAC/B;AACA,UAAI,iBAAiB,mBAAmB;AACtC,+BAAA,MAAI,wBAAY,MAAI,GAAA;AACpB,eAAO,KAAK,MAAM,SAAS,KAAK;MAClC;AACA,UAAI,iBAAiB,gBAAgB;AACnC,eAAO,KAAK,MAAM,SAAS,KAAK;MAClC;AACA,UAAI,iBAAiB,OAAO;AAC1B,cAAM,iBAAiC,IAAI,eAAe,MAAM,OAAO;AAEvE,uBAAe,QAAQ;AACvB,eAAO,KAAK,MAAM,SAAS,cAAc;MAC3C;AACA,aAAO,KAAK,MAAM,SAAS,IAAI,eAAe,OAAO,KAAK,CAAC,CAAC;IAC9D,CAAC;AA7RC,2BAAA,MAAI,iCAAqB,IAAI,QAAyB,CAAC,SAAS,WAAU;AACxE,6BAAA,MAAI,wCAA4B,SAAO,GAAA;AACvC,6BAAA,MAAI,uCAA2B,QAAM,GAAA;IACvC,CAAC,GAAC,GAAA;AAEF,2BAAA,MAAI,2BAAe,IAAI,QAAc,CAAC,SAAS,WAAU;AACvD,6BAAA,MAAI,kCAAsB,SAAO,GAAA;AACjC,6BAAA,MAAI,iCAAqB,QAAM,GAAA;IACjC,CAAC,GAAC,GAAA;AAMF,2BAAA,MAAI,iCAAA,GAAA,EAAmB,MAAM,MAAK;IAAE,CAAC;AACrC,2BAAA,MAAI,2BAAA,GAAA,EAAa,MAAM,MAAK;IAAE,CAAC;EACjC;EAEA,IAAI,WAAQ;AACV,WAAO,uBAAA,MAAI,yBAAA,GAAA;EACb;EAEA,IAAI,aAAU;AACZ,WAAO,uBAAA,MAAI,2BAAA,GAAA;EACb;;;;;;;;;;;EAYA,MAAM,eAAY;AAKhB,UAAM,WAAW,MAAM,uBAAA,MAAI,iCAAA,GAAA;AAC3B,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,MAAM,uCAAuC;IACzD;AAEA,WAAO;MACL,MAAM;MACN;MACA,YAAY,SAAS,QAAQ,IAAI,YAAY;;EAEjD;;;;;;;;EASA,OAAO,mBAAmB,QAAsB;AAC9C,UAAM,SAAS,IAAI,eAAa;AAChC,WAAO,KAAK,MAAM,OAAO,oBAAoB,MAAM,CAAC;AACpD,WAAO;EACT;EAEA,OAAO,cACL,UACA,QACA,SAAwB;AAExB,UAAM,SAAS,IAAI,eAAa;AAChC,eAAW,WAAW,OAAO,UAAU;AACrC,aAAO,iBAAiB,OAAO;IACjC;AACA,WAAO,KAAK,MACV,OAAO,eACL,UACA,EAAE,GAAG,QAAQ,QAAQ,KAAI,GACzB,EAAE,GAAG,SAAS,SAAS,EAAE,GAAG,mCAAS,SAAS,6BAA6B,SAAQ,EAAE,CAAE,CACxF;AAEH,WAAO;EACT;EAEU,KAAK,UAA4B;AACzC,aAAQ,EAAG,KAAK,MAAK;AACnB,WAAK,WAAU;AACf,WAAK,MAAM,KAAK;IAClB,GAAG,uBAAA,MAAI,4BAAA,GAAA,CAAa;EACtB;EAEU,iBAAiB,SAAqB;AAC9C,SAAK,SAAS,KAAK,OAAO;EAC5B;EAEU,YAAY,SAAkB,OAAO,MAAI;AACjD,SAAK,iBAAiB,KAAK,OAAO;AAClC,QAAI,MAAM;AACR,WAAK,MAAM,WAAW,OAAO;IAC/B;EACF;EAEU,MAAM,eACd,UACA,QACA,SAAwB;;AAExB,UAAM,SAAS,mCAAS;AACxB,QAAI,QAAQ;AACV,UAAI,OAAO;AAAS,aAAK,WAAW,MAAK;AACzC,aAAO,iBAAiB,SAAS,MAAM,KAAK,WAAW,MAAK,CAAE;IAChE;AACA,2BAAA,MAAI,0BAAA,KAAA,2BAAA,EAAc,KAAlB,IAAI;AACJ,UAAM,EAAE,UAAU,MAAM,OAAM,IAAK,MAAM,SACtC,OAAO,EAAE,GAAG,QAAQ,QAAQ,KAAI,GAAI,EAAE,GAAG,SAAS,QAAQ,KAAK,WAAW,OAAM,CAAE,EAClF,aAAY;AACf,SAAK,WAAW,QAAQ;AACxB,qBAAiB,SAAS,QAAQ;AAChC,6BAAA,MAAI,0BAAA,KAAA,6BAAA,EAAgB,KAApB,MAAqB,KAAK;IAC5B;AACA,SAAIC,MAAA,OAAO,WAAW,WAAlB,gBAAAA,IAA0B,SAAS;AACrC,YAAM,IAAI,kBAAiB;IAC7B;AACA,2BAAA,MAAI,0BAAA,KAAA,yBAAA,EAAY,KAAhB,IAAI;EACN;EAEU,WAAW,UAAyB;AAC5C,QAAI,KAAK;AAAO;AAChB,2BAAA,MAAI,yBAAa,UAAQ,GAAA;AACzB,2BAAA,MAAI,2BAAe,qCAAU,QAAQ,IAAI,eAAa,GAAA;AACtD,2BAAA,MAAI,wCAAA,GAAA,EAAyB,KAA7B,MAA8B,QAAQ;AACtC,SAAK,MAAM,SAAS;EACtB;EAEA,IAAI,QAAK;AACP,WAAO,uBAAA,MAAI,sBAAA,GAAA;EACb;EAEA,IAAI,UAAO;AACT,WAAO,uBAAA,MAAI,wBAAA,GAAA;EACb;EAEA,IAAI,UAAO;AACT,WAAO,uBAAA,MAAI,wBAAA,GAAA;EACb;EAEA,QAAK;AACH,SAAK,WAAW,MAAK;EACvB;;;;;;;;EASA,GAA4C,OAAc,UAAoC;AAC5F,UAAM,YACJ,uBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,MAAM,uBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,IAAI,CAAA;AACtD,cAAU,KAAK,EAAE,SAAQ,CAAE;AAC3B,WAAO;EACT;;;;;;;;EASA,IAA6C,OAAc,UAAoC;AAC7F,UAAM,YAAY,uBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK;AACvC,QAAI,CAAC;AAAW,aAAO;AACvB,UAAM,QAAQ,UAAU,UAAU,CAAC,MAAM,EAAE,aAAa,QAAQ;AAChE,QAAI,SAAS;AAAG,gBAAU,OAAO,OAAO,CAAC;AACzC,WAAO;EACT;;;;;;EAOA,KAA8C,OAAc,UAAoC;AAC9F,UAAM,YACJ,uBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,MAAM,uBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,IAAI,CAAA;AACtD,cAAU,KAAK,EAAE,UAAU,MAAM,KAAI,CAAE;AACvC,WAAO;EACT;;;;;;;;;;;;EAaA,QACE,OAAY;AAMZ,WAAO,IAAI,QAAQ,CAAC,SAAS,WAAU;AACrC,6BAAA,MAAI,uCAA2B,MAAI,GAAA;AACnC,UAAI,UAAU;AAAS,aAAK,KAAK,SAAS,MAAM;AAChD,WAAK,KAAK,OAAO,OAAc;IACjC,CAAC;EACH;EAEA,MAAM,OAAI;AACR,2BAAA,MAAI,uCAA2B,MAAI,GAAA;AACnC,UAAM,uBAAA,MAAI,2BAAA,GAAA;EACZ;EAEA,IAAI,iBAAc;AAChB,WAAO,uBAAA,MAAI,uCAAA,GAAA;EACb;;;;;EAaA,MAAM,eAAY;AAChB,UAAM,KAAK,KAAI;AACf,WAAO,uBAAA,MAAI,0BAAA,KAAA,8BAAA,EAAiB,KAArB,IAAI;EACb;;;;;;EAqBA,MAAM,YAAS;AACb,UAAM,KAAK,KAAI;AACf,WAAO,uBAAA,MAAI,0BAAA,KAAA,2BAAA,EAAc,KAAlB,IAAI;EACb;EAuBU,MACR,UACG,MAA4C;AAG/C,QAAI,uBAAA,MAAI,sBAAA,GAAA;AAAS;AAEjB,QAAI,UAAU,OAAO;AACnB,6BAAA,MAAI,sBAAU,MAAI,GAAA;AAClB,6BAAA,MAAI,kCAAA,GAAA,EAAmB,KAAvB,IAAI;IACN;AAEA,UAAM,YAA4D,uBAAA,MAAI,0BAAA,GAAA,EAAY,KAAK;AACvF,QAAI,WAAW;AACb,6BAAA,MAAI,0BAAA,GAAA,EAAY,KAAK,IAAI,UAAU,OAAO,CAAC,MAAM,CAAC,EAAE,IAAI;AACxD,gBAAU,QAAQ,CAAC,EAAE,SAAQ,MAAY,SAAS,GAAG,IAAI,CAAC;IAC5D;AAEA,QAAI,UAAU,SAAS;AACrB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,CAAC,uBAAA,MAAI,uCAAA,GAAA,KAA4B,EAAC,uCAAW,SAAQ;AACvD,gBAAQ,OAAO,KAAK;MACtB;AACA,6BAAA,MAAI,uCAAA,GAAA,EAAwB,KAA5B,MAA6B,KAAK;AAClC,6BAAA,MAAI,iCAAA,GAAA,EAAkB,KAAtB,MAAuB,KAAK;AAC5B,WAAK,MAAM,KAAK;AAChB;IACF;AAEA,QAAI,UAAU,SAAS;AAGrB,YAAM,QAAQ,KAAK,CAAC;AACpB,UAAI,CAAC,uBAAA,MAAI,uCAAA,GAAA,KAA4B,EAAC,uCAAW,SAAQ;AAOvD,gBAAQ,OAAO,KAAK;MACtB;AACA,6BAAA,MAAI,uCAAA,GAAA,EAAwB,KAA5B,MAA6B,KAAK;AAClC,6BAAA,MAAI,iCAAA,GAAA,EAAkB,KAAtB,MAAuB,KAAK;AAC5B,WAAK,MAAM,KAAK;IAClB;EACF;EAEU,aAAU;AAClB,UAAM,eAAe,KAAK,iBAAiB,GAAG,EAAE;AAChD,QAAI,cAAc;AAChB,WAAK,MAAM,gBAAgB,uBAAA,MAAI,0BAAA,KAAA,8BAAA,EAAiB,KAArB,IAAI,CAAmB;IACpD;EACF;EAgFU,MAAM,oBACd,gBACA,SAAwB;;AAExB,UAAM,SAAS,mCAAS;AACxB,QAAI,QAAQ;AACV,UAAI,OAAO;AAAS,aAAK,WAAW,MAAK;AACzC,aAAO,iBAAiB,SAAS,MAAM,KAAK,WAAW,MAAK,CAAE;IAChE;AACA,2BAAA,MAAI,0BAAA,KAAA,2BAAA,EAAc,KAAlB,IAAI;AACJ,SAAK,WAAW,IAAI;AACpB,UAAM,SAAS,OAAO,mBAAuC,gBAAgB,KAAK,UAAU;AAC5F,qBAAiB,SAAS,QAAQ;AAChC,6BAAA,MAAI,0BAAA,KAAA,6BAAA,EAAgB,KAApB,MAAqB,KAAK;IAC5B;AACA,SAAIA,MAAA,OAAO,WAAW,WAAlB,gBAAAA,IAA0B,SAAS;AACrC,YAAM,IAAI,kBAAiB;IAC7B;AACA,2BAAA,MAAI,0BAAA,KAAA,yBAAA,EAAY,KAAhB,IAAI;EACN;EA8GA,EAAA,wCAAA,oBAAA,QAAA,GAAA,kCAAA,oBAAA,QAAA,GAAA,yCAAA,oBAAA,QAAA,GAAA,wCAAA,oBAAA,QAAA,GAAA,4BAAA,oBAAA,QAAA,GAAA,mCAAA,oBAAA,QAAA,GAAA,kCAAA,oBAAA,QAAA,GAAA,2BAAA,oBAAA,QAAA,GAAA,uBAAA,oBAAA,QAAA,GAAA,yBAAA,oBAAA,QAAA,GAAA,yBAAA,oBAAA,QAAA,GAAA,wCAAA,oBAAA,QAAA,GAAA,0BAAA,oBAAA,QAAA,GAAA,4BAAA,oBAAA,QAAA,GAAA,6BAAA,oBAAA,QAAA,GAAA,2BAAA,oBAAA,QAAA,GAAA,iCAAA,SAAAC,kCAAA;AAlUE,QAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,YAAM,IAAI,eAAe,8DAA8D;IACzF;AACA,WAAO,KAAK,iBAAiB,GAAG,EAAE;EACpC,GAAC,8BAAA,SAAAC,+BAAA;AAYC,QAAI,KAAK,iBAAiB,WAAW,GAAG;AACtC,YAAM,IAAI,eAAe,8DAA8D;IACzF;AACA,UAAM,aAAa,KAAK,iBACrB,GAAG,EAAE,EACL,QAAQ,OAAO,CAAC,UAA8B,MAAM,SAAS,MAAM,EACnE,IAAI,CAAC,UAAU,MAAM,IAAI;AAC5B,QAAI,WAAW,WAAW,GAAG;AAC3B,YAAM,IAAI,eAAe,+DAA+D;IAC1F;AACA,WAAO,WAAW,KAAK,GAAG;EAC5B,GAAC,8BAAA,SAAAC,+BAAA;AAyFC,QAAI,KAAK;AAAO;AAChB,2BAAA,MAAI,uCAA2B,QAAS,GAAA;EAC1C,GAAC,gCAAA,SAAAC,+BACe,OAAyB;AACvC,QAAI,KAAK;AAAO;AAChB,UAAM,kBAAkB,uBAAA,MAAI,0BAAA,KAAA,gCAAA,EAAmB,KAAvB,MAAwB,KAAK;AACrD,SAAK,MAAM,eAAe,OAAO,eAAe;AAEhD,YAAQ,MAAM,MAAM;MAClB,KAAK,uBAAuB;AAC1B,cAAM,UAAU,gBAAgB,QAAQ,GAAG,EAAE;AAC7C,gBAAQ,MAAM,MAAM,MAAM;UACxB,KAAK,cAAc;AACjB,gBAAI,QAAQ,SAAS,QAAQ;AAC3B,mBAAK,MAAM,QAAQ,MAAM,MAAM,MAAM,QAAQ,QAAQ,EAAE;YACzD;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,gBAAI,QAAQ,SAAS,QAAQ;AAC3B,mBAAK,MAAM,YAAY,MAAM,MAAM,UAAU,QAAQ,aAAa,CAAA,CAAE;YACtE;AACA;UACF;UACA,KAAK,oBAAoB;AACvB,gBAAI,QAAQ,SAAS,cAAc,QAAQ,OAAO;AAChD,mBAAK,MAAM,aAAa,MAAM,MAAM,cAAc,QAAQ,KAAK;YACjE;AACA;UACF;UACA,KAAK,kBAAkB;AACrB,gBAAI,QAAQ,SAAS,YAAY;AAC/B,mBAAK,MAAM,YAAY,MAAM,MAAM,UAAU,QAAQ,QAAQ;YAC/D;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,gBAAI,QAAQ,SAAS,YAAY;AAC/B,mBAAK,MAAM,aAAa,QAAQ,SAAS;YAC3C;AACA;UACF;UACA;AACE,YAAAC,YAAW,MAAM,KAAK;QAC1B;AACA;MACF;MACA,KAAK,gBAAgB;AACnB,aAAK,iBAAiB,eAAe;AACrC,aAAK,YAAY,iBAAiB,IAAI;AACtC;MACF;MACA,KAAK,sBAAsB;AACzB,aAAK,MAAM,gBAAgB,gBAAgB,QAAQ,GAAG,EAAE,CAAE;AAC1D;MACF;MACA,KAAK,iBAAiB;AACpB,+BAAA,MAAI,uCAA2B,iBAAe,GAAA;AAC9C;MACF;MACA,KAAK;MACL,KAAK;AACH;IACJ;EACF,GAAC,4BAAA,SAAAC,6BAAA;AAEC,QAAI,KAAK,OAAO;AACd,YAAM,IAAI,eAAe,yCAAyC;IACpE;AACA,UAAM,WAAW,uBAAA,MAAI,uCAAA,GAAA;AACrB,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eAAe,0CAA0C;IACrE;AACA,2BAAA,MAAI,uCAA2B,QAAS,GAAA;AACxC,WAAO;EACT,GAAC,mCAAA,SAAAC,kCA4BkB,OAAyB;AAC1C,QAAI,WAAW,uBAAA,MAAI,uCAAA,GAAA;AAEnB,QAAI,MAAM,SAAS,iBAAiB;AAClC,UAAI,UAAU;AACZ,cAAM,IAAI,eAAe,+BAA+B,MAAM,IAAI,kCAAkC;MACtG;AACA,aAAO,MAAM;IACf;AAEA,QAAI,CAAC,UAAU;AACb,YAAM,IAAI,eAAe,+BAA+B,MAAM,IAAI,yBAAyB;IAC7F;AAEA,YAAQ,MAAM,MAAM;MAClB,KAAK;AACH,eAAO;MACT,KAAK;AACH,iBAAS,cAAc,MAAM,MAAM;AACnC,iBAAS,gBAAgB,MAAM,MAAM;AACrC,iBAAS,MAAM,gBAAgB,MAAM,MAAM;AAG3C,YAAI,MAAM,MAAM,gBAAgB,MAAM;AACpC,mBAAS,MAAM,eAAe,MAAM,MAAM;QAC5C;AAEA,YAAI,MAAM,MAAM,+BAA+B,MAAM;AACnD,mBAAS,MAAM,8BAA8B,MAAM,MAAM;QAC3D;AAEA,YAAI,MAAM,MAAM,2BAA2B,MAAM;AAC/C,mBAAS,MAAM,0BAA0B,MAAM,MAAM;QACvD;AAEA,YAAI,MAAM,MAAM,mBAAmB,MAAM;AACvC,mBAAS,MAAM,kBAAkB,MAAM,MAAM;QAC/C;AAEA,eAAO;MACT,KAAK;AACH,iBAAS,QAAQ,KAAK,MAAM,aAAa;AACzC,eAAO;MACT,KAAK,uBAAuB;AAC1B,cAAM,kBAAkB,SAAS,QAAQ,GAAG,MAAM,KAAK;AAEvD,gBAAQ,MAAM,MAAM,MAAM;UACxB,KAAK,cAAc;AACjB,iBAAI,mDAAiB,UAAS,QAAQ;AACpC,8BAAgB,QAAQ,MAAM,MAAM;YACtC;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,iBAAI,mDAAiB,UAAS,QAAQ;AACpC,8BAAgB,cAAhB,gBAAgB,YAAc,CAAA;AAC9B,8BAAgB,UAAU,KAAK,MAAM,MAAM,QAAQ;YACrD;AACA;UACF;UACA,KAAK,oBAAoB;AACvB,iBAAI,mDAAiB,UAAS,YAAY;AAIxC,kBAAI,UAAW,gBAAwBR,kBAAiB,KAAK;AAC7D,yBAAW,MAAM,MAAM;AAEvB,qBAAO,eAAe,iBAAiBA,oBAAmB;gBACxD,OAAO;gBACP,YAAY;gBACZ,UAAU;eACX;AAED,kBAAI,SAAS;AACX,gCAAgB,QAAQ,aAAa,OAAO;cAC9C;YACF;AACA;UACF;UACA,KAAK,kBAAkB;AACrB,iBAAI,mDAAiB,UAAS,YAAY;AACxC,8BAAgB,YAAY,MAAM,MAAM;YAC1C;AACA;UACF;UACA,KAAK,mBAAmB;AACtB,iBAAI,mDAAiB,UAAS,YAAY;AACxC,8BAAgB,YAAY,MAAM,MAAM;YAC1C;AACA;UACF;UACA;AACE,YAAAM,YAAW,MAAM,KAAK;QAC1B;AAEA,eAAO;MACT;MACA,KAAK;AACH,eAAO;IACX;EACF,GAEC,OAAO,cAAa,IAAC;AACpB,UAAM,YAAkC,CAAA;AACxC,UAAM,YAGA,CAAA;AACN,QAAI,OAAO;AAEX,SAAK,GAAG,eAAe,CAAC,UAAS;AAC/B,YAAM,SAAS,UAAU,MAAK;AAC9B,UAAI,QAAQ;AACV,eAAO,QAAQ,KAAK;MACtB,OAAO;AACL,kBAAU,KAAK,KAAK;MACtB;IACF,CAAC;AAED,SAAK,GAAG,OAAO,MAAK;AAClB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,QAAQ,MAAS;MAC1B;AACA,gBAAU,SAAS;IACrB,CAAC;AAED,SAAK,GAAG,SAAS,CAAC,QAAO;AACvB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,OAAO,GAAG;MACnB;AACA,gBAAU,SAAS;IACrB,CAAC;AAED,SAAK,GAAG,SAAS,CAAC,QAAO;AACvB,aAAO;AACP,iBAAW,UAAU,WAAW;AAC9B,eAAO,OAAO,GAAG;MACnB;AACA,gBAAU,SAAS;IACrB,CAAC;AAED,WAAO;MACL,MAAM,YAAwD;AAC5D,YAAI,CAAC,UAAU,QAAQ;AACrB,cAAI,MAAM;AACR,mBAAO,EAAE,OAAO,QAAW,MAAM,KAAI;UACvC;AACA,iBAAO,IAAI,QAAwC,CAAC,SAAS,WAC3D,UAAU,KAAK,EAAE,SAAS,OAAM,CAAE,CAAC,EACnC,KAAK,CAACG,WAAWA,SAAQ,EAAE,OAAOA,QAAO,MAAM,MAAK,IAAK,EAAE,OAAO,QAAW,MAAM,KAAI,CAAG;QAC9F;AACA,cAAM,QAAQ,UAAU,MAAK;AAC7B,eAAO,EAAE,OAAO,OAAO,MAAM,MAAK;MACpC;MACA,QAAQ,YAAW;AACjB,aAAK,MAAK;AACV,eAAO,EAAE,OAAO,QAAW,MAAM,KAAI;MACvC;;EAEJ;EAEA,mBAAgB;AACd,UAAM,SAAS,IAAI,OAAO,KAAK,OAAO,aAAa,EAAE,KAAK,IAAI,GAAG,KAAK,UAAU;AAChF,WAAO,OAAO,iBAAgB;EAChC;;AAIF,SAASH,YAAW,GAAQ;AAAG;;;AC9pBzB,IAAOI,WAAP,cAAuB,YAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EA6BtC,OAAO,MAAyB,SAAwB;AACtD,WAAO,KAAK,QAAQ,KAAK,wBAAwB,EAAE,MAAM,GAAG,QAAO,CAAE;EACvE;;;;;;;;;;;;;;;;EAiBA,SAAS,gBAAwB,SAAwB;AACvD,WAAO,KAAK,QAAQ,IAAI,4BAA4B,cAAc,IAAI,OAAO;EAC/E;;;;;;;;;;;;;;;;EAiBA,KACE,QAA4C,CAAA,GAC5C,SAAwB;AAExB,WAAO,KAAK,QAAQ,WAAW,wBAAwB,MAAoB,EAAE,OAAO,GAAG,QAAO,CAAE;EAClG;;;;;;;;;;;;;;;;EAiBA,OAAO,gBAAwB,SAAwB;AACrD,WAAO,KAAK,QAAQ,OAAO,4BAA4B,cAAc,IAAI,OAAO;EAClF;;;;;;;;;;;;;;;;;;;;;;EAuBA,OAAO,gBAAwB,SAAwB;AACrD,WAAO,KAAK,QAAQ,KAAK,4BAA4B,cAAc,WAAW,OAAO;EACvF;;;;;;;;;;;;;;;;;EAkBA,MAAM,QACJ,gBACA,SAAwB;AAExB,UAAM,QAAQ,MAAM,KAAK,SAAS,cAAc;AAChD,QAAI,CAAC,MAAM,aAAa;AACtB,YAAM,IAAI,eACR,yDAAyD,MAAM,iBAAiB,MAAM,MAAM,EAAE,EAAE;IAEpG;AAEA,WAAO,KAAK,QACT,IAAI,MAAM,aAAa;MACtB,GAAG;MACH,SAAS,aAAa,CAAC,EAAE,QAAQ,qBAAoB,GAAI,mCAAS,OAAO,CAAC;MAC1E,QAAQ;MACR,kBAAkB;KACnB,EACA,YAAY,CAAC,GAAG,UAAU,aAAa,aAAa,MAAM,UAAU,MAAM,UAAU,CAAC;EAG1F;;;;AC7II,IAAOC,YAAP,cAAwB,YAAW;EAAzC,cAAA;;AACE,SAAA,UAA8B,IAAeC,SAAQ,KAAK,OAAO;EAiFnE;EApDE,OACE,MACA,SAAwB;AAExB,QAAI,KAAK,SAASC,oBAAmB;AACnC,cAAQ,KACN,cAAc,KAAK,KAAK,iDACtBA,mBAAkB,KAAK,KAAK,CAC9B;6HAAgI;IAEpI;AACA,QAAI,UAAW,KAAK,QAAgB,SAAS;AAC7C,QAAI,CAAC,KAAK,UAAU,WAAW,MAAM;AACnC,YAAM,wBAAwB,0BAA0B,KAAK,KAAK,KAAK;AACvE,gBAAU,KAAK,QAAQ,6BAA6B,KAAK,YAAY,qBAAqB;IAC5F;AACA,WAAO,KAAK,QAAQ,KAAK,gBAAgB;MACvC;MACA,SAAS,WAAW;MACpB,GAAG;MACH,QAAQ,KAAK,UAAU;KACxB;EACH;;;;EAKA,OAAO,MAA2B,SAAwB;AACxD,WAAO,cAAc,cAAc,MAAM,MAAM,OAAO;EACxD;;;;;;;;;;;;;;;;;;;EAoBA,YAAY,MAAgC,SAAwB;AAClE,WAAO,KAAK,QAAQ,KAAK,6BAA6B,EAAE,MAAM,GAAG,QAAO,CAAE;EAC5E;;AA8YF,IAAMA,qBAEF;EACF,cAAc;EACd,mBAAmB;EACnB,sBAAsB;EACtB,2BAA2B;EAC3B,sBAAsB;EACtB,4BAA4B;EAC5B,cAAc;EACd,cAAc;;AAmoChBF,UAAS,UAAUC;;;AC7nDb,IAAOE,UAAP,cAAsB,YAAW;;;;;;;EAOrC,SACE,SACA,SAAiD,CAAA,GACjD,SAAwB;AAExB,UAAM,EAAE,MAAK,IAAK,UAAU,CAAA;AAC5B,WAAO,KAAK,QAAQ,IAAI,kBAAkB,OAAO,IAAI;MACnD,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,IAAI,+BAAO,eAAc,OAAO,EAAE,kBAAkB,+BAAO,WAAU,IAAK,OAAU;QACtF,mCAAS;OACV;KACF;EACH;;;;;;;EAQA,KACE,SAA6C,CAAA,GAC7C,SAAwB;AAExB,UAAM,EAAE,OAAO,GAAG,MAAK,IAAK,UAAU,CAAA;AACtC,WAAO,KAAK,QAAQ,WAAW,cAAc,MAAiB;MAC5D;MACA,GAAG;MACH,SAAS,aAAa;QACpB,EAAE,IAAI,+BAAO,eAAc,OAAO,EAAE,kBAAkB,+BAAO,WAAU,IAAK,OAAU;QACtF,mCAAS;OACV;KACF;EACH;;;;AC1CK,IAAM,UAAU,CAAC,QAAmC;AAT3D,MAAAC,KAAA;AAUE,MAAI,OAAQ,WAAmB,YAAY,aAAa;AACtD,aAAQ,MAAAA,MAAA,WAAmB,QAAQ,QAA3B,gBAAAA,IAAiC,SAAjC,mBAAuC,WAAU;EAC3D;AACA,MAAI,OAAQ,WAAmB,SAAS,aAAa;AACnD,YAAQ,4BAAmB,KAAK,QAAxB,mBAA6B,QAA7B,4BAAmC,SAAnC,mBAAyC;EACnD;AACA,SAAO;AACT;;;;;ACoNM,IAAO,gBAAP,MAAoB;;;;;;;;;;;;;;;EA8BxB,YAAY,EACV,UAAU,QAAQ,oBAAoB,GACtC,SAAS,QAAQ,mBAAmB,KAAK,MACzC,YAAY,QAAQ,sBAAsB,KAAK,MAC/C,GAAG,KAAI,IACU,CAAA,GAAE;AAvBrB,2BAAA,IAAA,MAAA,MAAA;AAwBE,UAAM,UAAyB;MAC7B;MACA;MACA,GAAG;MACH,SAAS,WAAW;;AAGtB,QAAI,CAAC,QAAQ,2BAA2B,mBAAkB,GAAI;AAC5D,YAAM,IAAW,eACf,sWAAsW;IAE1W;AAEA,SAAK,UAAU,QAAQ;AACvB,SAAK,UAAU,QAAQ,WAAW,UAAU;AAC5C,SAAK,SAAS,QAAQ,UAAU;AAChC,UAAM,kBAAkB;AAExB,SAAK,WAAW;AAChB,SAAK,WACH,cAAc,QAAQ,UAAU,0BAA0B,IAAI,KAC9D,cAAc,QAAQ,eAAe,GAAG,gCAAgC,IAAI,KAC5E;AACF,SAAK,eAAe,QAAQ;AAC5B,SAAK,aAAa,QAAQ,cAAc;AACxC,SAAK,QAAQ,QAAQ,SAAe,gBAAe;AACnD,2BAAA,MAAI,wBAAiB,iBAAe,GAAA;AAEpC,SAAK,WAAW;AAEhB,SAAK,SAAS;AACd,SAAK,YAAY;EACnB;;;;EAKA,YAAY,SAA+B;AACzC,WAAO,IAAK,KAAK,YAAiE;MAChF,GAAG,KAAK;MACR,SAAS,KAAK;MACd,YAAY,KAAK;MACjB,SAAS,KAAK;MACd,QAAQ,KAAK;MACb,UAAU,KAAK;MACf,cAAc,KAAK;MACnB,QAAQ,KAAK;MACb,WAAW,KAAK;MAChB,GAAG;KACJ;EACH;EAEU,eAAY;AACpB,WAAO,KAAK,SAAS;EACvB;EAEU,gBAAgB,EAAE,QAAQ,MAAK,GAAmB;AAC1D,QAAI,KAAK,UAAU,OAAO,IAAI,WAAW,GAAG;AAC1C;IACF;AACA,QAAI,MAAM,IAAI,WAAW,GAAG;AAC1B;IACF;AAEA,QAAI,KAAK,aAAa,OAAO,IAAI,eAAe,GAAG;AACjD;IACF;AACA,QAAI,MAAM,IAAI,eAAe,GAAG;AAC9B;IACF;AAEA,UAAM,IAAI,MACR,2KAA2K;EAE/K;EAEU,YAAY,MAAyB;AAC7C,WAAO,aAAa,CAAC,KAAK,WAAW,IAAI,GAAG,KAAK,WAAW,IAAI,CAAC,CAAC;EACpE;EAEU,WAAW,MAAyB;AAC5C,QAAI,KAAK,UAAU,MAAM;AACvB,aAAO;IACT;AACA,WAAO,aAAa,CAAC,EAAE,aAAa,KAAK,OAAM,CAAE,CAAC;EACpD;EAEU,WAAW,MAAyB;AAC5C,QAAI,KAAK,aAAa,MAAM;AAC1B,aAAO;IACT;AACA,WAAO,aAAa,CAAC,EAAE,eAAe,UAAU,KAAK,SAAS,GAAE,CAAE,CAAC;EACrE;;;;EAKU,eAAe,OAA8B;AACrD,WAAO,OAAO,QAAQ,KAAK,EACxB,OAAO,CAAC,CAAC,GAAG,KAAK,MAAM,OAAO,UAAU,WAAW,EACnD,IAAI,CAAC,CAAC,KAAK,KAAK,MAAK;AACpB,UAAI,OAAO,UAAU,YAAY,OAAO,UAAU,YAAY,OAAO,UAAU,WAAW;AACxF,eAAO,GAAG,mBAAmB,GAAG,CAAC,IAAI,mBAAmB,KAAK,CAAC;MAChE;AACA,UAAI,UAAU,MAAM;AAClB,eAAO,GAAG,mBAAmB,GAAG,CAAC;MACnC;AACA,YAAM,IAAW,eACf,yBAAyB,OAAO,KAAK,mQAAmQ;IAE5S,CAAC,EACA,KAAK,GAAG;EACb;EAEQ,eAAY;AAClB,WAAO,GAAG,KAAK,YAAY,IAAI,OAAO,OAAO;EAC/C;EAEU,wBAAqB;AAC7B,WAAO,wBAAwB,MAAK,CAAE;EACxC;EAEU,gBACR,QACA,OACA,SACA,SAAgB;AAEhB,WAAc,SAAS,SAAS,QAAQ,OAAO,SAAS,OAAO;EACjE;EAEA,SAASC,OAAc,OAAiD;AACtE,UAAM,MACJ,cAAcA,KAAI,IAChB,IAAI,IAAIA,KAAI,IACZ,IAAI,IAAI,KAAK,WAAW,KAAK,QAAQ,SAAS,GAAG,KAAKA,MAAK,WAAW,GAAG,IAAIA,MAAK,MAAM,CAAC,IAAIA,MAAK;AAEtG,UAAM,eAAe,KAAK,aAAY;AACtC,QAAI,CAAC,WAAW,YAAY,GAAG;AAC7B,cAAQ,EAAE,GAAG,cAAc,GAAG,MAAK;IACrC;AAEA,QAAI,OAAO,UAAU,YAAY,SAAS,CAAC,MAAM,QAAQ,KAAK,GAAG;AAC/D,UAAI,SAAS,KAAK,eAAe,KAAgC;IACnE;AAEA,WAAO,IAAI,SAAQ;EACrB;EAEA,8BAA8B,WAAiB;AAC7C,UAAM,iBAAiB,KAAK;AAC5B,UAAM,kBAAmB,KAAK,KAAK,YAAa;AAChD,QAAI,kBAAkB,gBAAgB;AACpC,YAAM,IAAW,eACf,oLAC+F;IAEnG;AACA,WAAO,iBAAiB;EAC1B;;;;EAKU,MAAM,eAAe,SAA4B;EAAkB;;;;;;;EAQnE,MAAM,eACd,SACA,EAAE,KAAK,QAAO,GAAiD;EAC/C;EAElB,IAASA,OAAc,MAAqC;AAC1D,WAAO,KAAK,cAAc,OAAOA,OAAM,IAAI;EAC7C;EAEA,KAAUA,OAAc,MAAqC;AAC3D,WAAO,KAAK,cAAc,QAAQA,OAAM,IAAI;EAC9C;EAEA,MAAWA,OAAc,MAAqC;AAC5D,WAAO,KAAK,cAAc,SAASA,OAAM,IAAI;EAC/C;EAEA,IAASA,OAAc,MAAqC;AAC1D,WAAO,KAAK,cAAc,OAAOA,OAAM,IAAI;EAC7C;EAEA,OAAYA,OAAc,MAAqC;AAC7D,WAAO,KAAK,cAAc,UAAUA,OAAM,IAAI;EAChD;EAEQ,cACN,QACAA,OACA,MAAqC;AAErC,WAAO,KAAK,QACV,QAAQ,QAAQ,IAAI,EAAE,KAAK,CAACC,UAAQ;AAClC,aAAO,EAAE,QAAQ,MAAAD,OAAM,GAAGC,MAAI;IAChC,CAAC,CAAC;EAEN;EAEA,QACE,SACA,mBAAkC,MAAI;AAEtC,WAAO,IAAI,WAAW,MAAM,KAAK,YAAY,SAAS,kBAAkB,MAAS,CAAC;EACpF;EAEQ,MAAM,YACZ,cACA,kBACA,qBAAuC;AApe3C,QAAAC,KAAA;AAseI,UAAM,UAAU,MAAM;AACtB,UAAM,aAAa,QAAQ,cAAc,KAAK;AAC9C,QAAI,oBAAoB,MAAM;AAC5B,yBAAmB;IACrB;AAEA,UAAM,KAAK,eAAe,OAAO;AAEjC,UAAM,EAAE,KAAK,KAAK,QAAO,IAAK,KAAK,aAAa,SAAS,EAAE,YAAY,aAAa,iBAAgB,CAAE;AAEtG,UAAM,KAAK,eAAe,KAAK,EAAE,KAAK,QAAO,CAAE;AAG/C,UAAM,eAAe,UAAW,KAAK,OAAM,KAAM,KAAK,MAAO,GAAG,SAAS,EAAE,EAAE,SAAS,GAAG,GAAG;AAC5F,UAAM,cAAc,wBAAwB,SAAY,KAAK,cAAc,mBAAmB;AAC9F,UAAM,YAAY,KAAK,IAAG;AAE1B,cAAU,IAAI,EAAE,MACd,IAAI,YAAY,qBAChB,qBAAqB;MACnB;MACA,QAAQ,QAAQ;MAChB;MACA;MACA,SAAS,IAAI;KACd,CAAC;AAGJ,SAAIA,MAAA,QAAQ,WAAR,gBAAAA,IAAgB,SAAS;AAC3B,YAAM,IAAW,kBAAiB;IACpC;AAEA,UAAM,aAAa,IAAI,gBAAe;AACtC,UAAM,WAAW,MAAM,KAAK,iBAAiB,KAAK,KAAK,SAAS,UAAU,EAAE,MAAM,WAAW;AAC7F,UAAM,cAAc,KAAK,IAAG;AAE5B,QAAI,oBAAoB,OAAO;AAC7B,YAAM,eAAe,aAAa,gBAAgB;AAClD,WAAI,aAAQ,WAAR,mBAAgB,SAAS;AAC3B,cAAM,IAAW,kBAAiB;MACpC;AAKA,YAAM,YACJ,aAAa,QAAQ,KACrB,eAAe,KAAK,OAAO,QAAQ,KAAK,WAAW,WAAW,OAAO,SAAS,KAAK,IAAI,GAAG;AAC5F,UAAI,kBAAkB;AACpB,kBAAU,IAAI,EAAE,KACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,MAAM,YAAY,EAAE;AAExF,kBAAU,IAAI,EAAE,MACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,KAAK,YAAY,KACnF,qBAAqB;UACnB;UACA;UACA,YAAY,cAAc;UAC1B,SAAS,SAAS;SACnB,CAAC;AAEJ,eAAO,KAAK,aAAa,SAAS,kBAAkB,uBAAuB,YAAY;MACzF;AACA,gBAAU,IAAI,EAAE,KACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,gCAAgC;AAEpG,gBAAU,IAAI,EAAE,MACd,IAAI,YAAY,gBAAgB,YAAY,cAAc,QAAQ,kCAClE,qBAAqB;QACnB;QACA;QACA,YAAY,cAAc;QAC1B,SAAS,SAAS;OACnB,CAAC;AAEJ,UAAI,WAAW;AACb,cAAM,IAAW,0BAAyB;MAC5C;AACA,YAAM,IAAW,mBAAmB,EAAE,OAAO,SAAQ,CAAE;IACzD;AAEA,UAAM,iBAAiB,CAAC,GAAG,SAAS,QAAQ,QAAO,CAAE,EAClD,OAAO,CAAC,CAAC,IAAI,MAAM,SAAS,YAAY,EACxC,IAAI,CAAC,CAAC,MAAM,KAAK,MAAM,OAAO,OAAO,OAAO,KAAK,UAAU,KAAK,CAAC,EACjE,KAAK,EAAE;AACV,UAAM,eAAe,IAAI,YAAY,GAAG,WAAW,GAAG,cAAc,KAAK,IAAI,MAAM,IAAI,GAAG,IACxF,SAAS,KAAK,cAAc,QAC9B,gBAAgB,SAAS,MAAM,OAAO,cAAc,SAAS;AAE7D,QAAI,CAAC,SAAS,IAAI;AAChB,YAAM,cAAc,KAAK,YAAY,QAAQ;AAC7C,UAAI,oBAAoB,aAAa;AACnC,cAAMC,gBAAe,aAAa,gBAAgB;AAGlD,cAAY,qBAAqB,SAAS,IAAI;AAC9C,kBAAU,IAAI,EAAE,KAAK,GAAG,YAAY,MAAMA,aAAY,EAAE;AACxD,kBAAU,IAAI,EAAE,MACd,IAAI,YAAY,qBAAqBA,aAAY,KACjD,qBAAqB;UACnB;UACA,KAAK,SAAS;UACd,QAAQ,SAAS;UACjB,SAAS,SAAS;UAClB,YAAY,cAAc;SAC3B,CAAC;AAEJ,eAAO,KAAK,aACV,SACA,kBACA,uBAAuB,cACvB,SAAS,OAAO;MAEpB;AAEA,YAAM,eAAe,cAAc,gCAAgC;AAEnE,gBAAU,IAAI,EAAE,KAAK,GAAG,YAAY,MAAM,YAAY,EAAE;AAExD,YAAM,UAAU,MAAM,SAAS,KAAI,EAAG,MAAM,CAACC,SAAa,YAAYA,IAAG,EAAE,OAAO;AAClF,YAAM,UAAU,SAAS,OAAO;AAChC,YAAM,aAAa,UAAU,SAAY;AAEzC,gBAAU,IAAI,EAAE,MACd,IAAI,YAAY,qBAAqB,YAAY,KACjD,qBAAqB;QACnB;QACA,KAAK,SAAS;QACd,QAAQ,SAAS;QACjB,SAAS,SAAS;QAClB,SAAS;QACT,YAAY,KAAK,IAAG,IAAK;OAC1B,CAAC;AAGJ,YAAM,MAAM,KAAK,gBAAgB,SAAS,QAAQ,SAAS,YAAY,SAAS,OAAO;AACvF,YAAM;IACR;AAEA,cAAU,IAAI,EAAE,KAAK,YAAY;AACjC,cAAU,IAAI,EAAE,MACd,IAAI,YAAY,oBAChB,qBAAqB;MACnB;MACA,KAAK,SAAS;MACd,QAAQ,SAAS;MACjB,SAAS,SAAS;MAClB,YAAY,cAAc;KAC3B,CAAC;AAGJ,WAAO,EAAE,UAAU,SAAS,YAAY,cAAc,qBAAqB,UAAS;EACtF;EAEA,WACEJ,OACAK,OACA,MAAqB;AAErB,WAAO,KAAK,eAAeA,OAAM,EAAE,QAAQ,OAAO,MAAAL,OAAM,GAAG,KAAI,CAAE;EACnE;EAEA,eAIEK,OACA,SAA4B;AAE5B,UAAM,UAAU,KAAK,YAAY,SAAS,MAAM,MAAS;AACzD,WAAO,IAAe,YAA6B,MAA0B,SAASA,KAAI;EAC5F;EAEA,MAAM,iBACJ,KACA,MACA,IACA,YAA2B;AAE3B,UAAM,EAAE,QAAQ,QAAQ,GAAG,QAAO,IAAK,QAAQ,CAAA;AAC/C,QAAI;AAAQ,aAAO,iBAAiB,SAAS,MAAM,WAAW,MAAK,CAAE;AAErE,UAAM,UAAU,WAAW,MAAM,WAAW,MAAK,GAAI,EAAE;AAEvD,UAAM,iBACF,WAAmB,kBAAkB,QAAQ,gBAAiB,WAAmB,kBAClF,OAAO,QAAQ,SAAS,YAAY,QAAQ,SAAS,QAAQ,OAAO,iBAAiB,QAAQ;AAEhG,UAAM,eAA4B;MAChC,QAAQ,WAAW;MACnB,GAAI,iBAAiB,EAAE,QAAQ,OAAM,IAAK,CAAA;MAC1C,QAAQ;MACR,GAAG;;AAEL,QAAI,QAAQ;AAGV,mBAAa,SAAS,OAAO,YAAW;IAC1C;AAEA,QAAI;AAEF,aAAO,MAAM,KAAK,MAAM,KAAK,QAAW,KAAK,YAAY;IAC3D;AACE,mBAAa,OAAO;IACtB;EACF;EAEQ,YAAY,UAAkB;AAEpC,UAAM,oBAAoB,SAAS,QAAQ,IAAI,gBAAgB;AAG/D,QAAI,sBAAsB;AAAQ,aAAO;AACzC,QAAI,sBAAsB;AAAS,aAAO;AAG1C,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,WAAW;AAAK,aAAO;AAGpC,QAAI,SAAS,UAAU;AAAK,aAAO;AAEnC,WAAO;EACT;EAEQ,MAAM,aACZ,SACA,kBACA,cACA,iBAAqC;AAErC,QAAI;AAGJ,UAAM,yBAAyB,mDAAiB,IAAI;AACpD,QAAI,wBAAwB;AAC1B,YAAM,YAAY,WAAW,sBAAsB;AACnD,UAAI,CAAC,OAAO,MAAM,SAAS,GAAG;AAC5B,wBAAgB;MAClB;IACF;AAGA,UAAM,mBAAmB,mDAAiB,IAAI;AAC9C,QAAI,oBAAoB,CAAC,eAAe;AACtC,YAAM,iBAAiB,WAAW,gBAAgB;AAClD,UAAI,CAAC,OAAO,MAAM,cAAc,GAAG;AACjC,wBAAgB,iBAAiB;MACnC,OAAO;AACL,wBAAgB,KAAK,MAAM,gBAAgB,IAAI,KAAK,IAAG;MACzD;IACF;AAIA,QAAI,EAAE,iBAAiB,KAAK,iBAAiB,gBAAgB,KAAK,MAAO;AACvE,YAAM,aAAa,QAAQ,cAAc,KAAK;AAC9C,sBAAgB,KAAK,mCAAmC,kBAAkB,UAAU;IACtF;AACA,UAAM,MAAM,aAAa;AAEzB,WAAO,KAAK,YAAY,SAAS,mBAAmB,GAAG,YAAY;EACrE;EAEQ,mCAAmC,kBAA0B,YAAkB;AACrF,UAAM,oBAAoB;AAC1B,UAAM,gBAAgB;AAEtB,UAAM,aAAa,aAAa;AAGhC,UAAM,eAAe,KAAK,IAAI,oBAAoB,KAAK,IAAI,GAAG,UAAU,GAAG,aAAa;AAGxF,UAAM,SAAS,IAAI,KAAK,OAAM,IAAK;AAEnC,WAAO,eAAe,SAAS;EACjC;EAEO,6BAA6B,WAAmB,uBAA8B;AACnF,UAAM,UAAU,KAAK,KAAK;AAC1B,UAAM,cAAc,KAAK,KAAK;AAE9B,UAAM,eAAgB,UAAU,YAAa;AAC7C,QAAI,eAAe,eAAgB,yBAAyB,QAAQ,YAAY,uBAAwB;AACtG,YAAM,IAAW,eACf,mLAAmL;IAEvL;AAEA,WAAO;EACT;EAEA,aACE,cACA,EAAE,aAAa,EAAC,IAA8B,CAAA,GAAE;AAEhD,UAAM,UAAU,EAAE,GAAG,aAAY;AACjC,UAAM,EAAE,QAAQ,MAAAL,OAAM,MAAK,IAAK;AAEhC,UAAM,MAAM,KAAK,SAASA,OAAO,KAAgC;AACjE,QAAI,aAAa;AAAS,8BAAwB,WAAW,QAAQ,OAAO;AAC5E,YAAQ,UAAU,QAAQ,WAAW,KAAK;AAC1C,UAAM,EAAE,aAAa,KAAI,IAAK,KAAK,UAAU,EAAE,QAAO,CAAE;AACxD,UAAM,aAAa,KAAK,aAAa,EAAE,SAAS,cAAc,QAAQ,aAAa,WAAU,CAAE;AAE/F,UAAM,MAA4B;MAChC;MACA,SAAS;MACT,GAAI,QAAQ,UAAU,EAAE,QAAQ,QAAQ,OAAM;MAC9C,GAAK,WAAmB,kBACtB,gBAAiB,WAAmB,kBAAkB,EAAE,QAAQ,OAAM;MACxE,GAAI,QAAQ,EAAE,KAAI;MAClB,GAAK,KAAK,gBAAwB,CAAA;MAClC,GAAK,QAAQ,gBAAwB,CAAA;;AAGvC,WAAO,EAAE,KAAK,KAAK,SAAS,QAAQ,QAAO;EAC7C;EAEQ,aAAa,EACnB,SACA,QACA,aACA,WAAU,GAMX;AACC,QAAI,qBAAkC,CAAA;AACtC,QAAI,KAAK,qBAAqB,WAAW,OAAO;AAC9C,UAAI,CAAC,QAAQ;AAAgB,gBAAQ,iBAAiB,KAAK,sBAAqB;AAChF,yBAAmB,KAAK,iBAAiB,IAAI,QAAQ;IACvD;AAEA,UAAM,UAAU,aAAa;MAC3B;MACA;QACE,QAAQ;QACR,cAAc,KAAK,aAAY;QAC/B,2BAA2B,OAAO,UAAU;QAC5C,GAAI,QAAQ,UAAU,EAAE,uBAAuB,OAAO,KAAK,MAAM,QAAQ,UAAU,GAAI,CAAC,EAAC,IAAK,CAAA;QAC9F,GAAG,mBAAkB;QACrB,GAAI,KAAK,SAAS,0BAChB,EAAE,6CAA6C,OAAM,IACrD;QACF,qBAAqB;;MAEvB,KAAK,YAAY,OAAO;MACxB,KAAK,SAAS;MACd;MACA,QAAQ;KACT;AAED,SAAK,gBAAgB,OAAO;AAE5B,WAAO,QAAQ;EACjB;EAEQ,UAAU,EAAE,SAAS,EAAE,MAAM,SAAS,WAAU,EAAE,GAAoC;AAI5F,QAAI,CAAC,MAAM;AACT,aAAO,EAAE,aAAa,QAAW,MAAM,OAAS;IAClD;AACA,UAAM,UAAU,aAAa,CAAC,UAAU,CAAC;AACzC;;MAEE,YAAY,OAAO,IAAI,KACvB,gBAAgB,eAChB,gBAAgB,YACf,OAAO,SAAS;MAEf,QAAQ,OAAO,IAAI,cAAc;MAEnC,gBAAgB;MAEhB,gBAAgB;MAEhB,gBAAgB;MAEd,WAAmB,kBAAkB,gBAAiB,WAAmB;MAC3E;AACA,aAAO,EAAE,aAAa,QAAW,KAAsB;IACzD,WACE,OAAO,SAAS,aACf,OAAO,iBAAiB,QACtB,OAAO,YAAY,QAAQ,UAAU,QAAQ,OAAO,KAAK,SAAS,aACrE;AACA,aAAO,EAAE,aAAa,QAAW,MAAY,mBAAmB,IAAiC,EAAC;IACpG,OAAO;AACL,aAAO,uBAAA,MAAI,wBAAA,GAAA,EAAS,KAAb,MAAc,EAAE,MAAM,QAAO,CAAE;IACxC;EACF;;;AAEO,cAAA,YAAY;AACZ,cAAA,eAAe;AACf,cAAA,YAAY;AACZ,cAAA,kBAAkB;AAElB,cAAA,iBAAwB;AACxB,cAAA,WAAkB;AAClB,cAAA,qBAA4B;AAC5B,cAAA,4BAAmC;AACnC,cAAA,oBAA2B;AAC3B,cAAA,gBAAuB;AACvB,cAAA,gBAAuB;AACvB,cAAA,iBAAwB;AACxB,cAAA,kBAAyB;AACzB,cAAA,sBAA6B;AAC7B,cAAA,sBAA6B;AAC7B,cAAA,wBAA+B;AAC/B,cAAA,2BAAkC;AAElC,cAAA,SAAiB;AAMpB,IAAO,YAAP,cAAyB,cAAa;EAA5C,cAAA;;AACE,SAAA,cAA+B,IAAQ,YAAY,IAAI;AACvD,SAAA,WAAyB,IAAQM,UAAS,IAAI;AAC9C,SAAA,SAAqB,IAAQC,QAAO,IAAI;AACxC,SAAA,OAAiB,IAAQ,KAAK,IAAI;EACpC;;AACA,UAAU,cAAc;AACxB,UAAU,WAAWD;AACrB,UAAU,SAASC;AACnB,UAAU,OAAO;AAiJV,IAAM,EAAE,cAAc,UAAS,IAAK;", "names": ["_a", "_a", "_a", "_a", "Page", "client", "_a", "_a", "fetch", "_a", "path", "_a", "_BetaMessageStream_getFinalMessage", "_BetaMessageStream_getFinalText", "_BetaMessageStream_beginRequest", "_BetaMessageStream_addStreamEvent", "_BetaMessageStream_endRequest", "_BetaMessageStream_accumulateMessage", "chunk", "JSON_BUF_PROPERTY", "_a", "_MessageStream_getFinalMessage", "_MessageStream_getFinalText", "_MessageStream_beginRequest", "_MessageStream_addStreamEvent", "checkNever", "_MessageStream_endRequest", "_MessageStream_accumulateMessage", "chunk", "Batches", "Messages", "Batches", "DEPRECATED_MODELS", "Models", "_a", "path", "opts", "_a", "retryMessage", "err", "Page", "Messages", "Models"]}