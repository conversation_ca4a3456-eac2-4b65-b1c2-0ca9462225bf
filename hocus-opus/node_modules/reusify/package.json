{"name": "reusify", "version": "1.1.0", "description": "Reuse objects and functions with style", "main": "reusify.js", "types": "reusify.d.ts", "scripts": {"lint": "eslint", "test": "tape test.js", "test:coverage": "c8 --100 tape test.js", "test:typescript": "tsc"}, "pre-commit": ["lint", "test", "test:typescript"], "repository": {"type": "git", "url": "git+https://github.com/mcollina/reusify.git"}, "keywords": ["reuse", "object", "performance", "function", "fast"], "author": "<PERSON> <<EMAIL>>", "license": "MIT", "bugs": {"url": "https://github.com/mcollina/reusify/issues"}, "homepage": "https://github.com/mcollina/reusify#readme", "engines": {"node": ">=0.10.0", "iojs": ">=1.0.0"}, "devDependencies": {"@types/node": "^22.9.0", "eslint": "^9.13.0", "neostandard": "^0.12.0", "pre-commit": "^1.2.2", "tape": "^5.0.0", "c8": "^10.1.2", "typescript": "^5.2.2"}, "dependencies": {}}