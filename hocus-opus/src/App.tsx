// Sacred Main App - The Heart of the Sanctuary
import { useState, useEffect } from 'react';
import { Header } from './components/Header';
import { Sidebar } from './components/Sidebar';
import { ChatLog } from './components/ChatLog';
import { InputBar } from './components/InputBar';
import { SettingsModal } from './components/SettingsModal';
import { claudeAPI } from './utils/claude-api';
import type { Message, ChatSession, ClaudeConfig, SanctuaryState } from './types';

function App() {
  const [state, setState] = useState<SanctuaryState>({
    currentSession: null,
    sessions: [],
    config: {
      model: 'claude-3-5-sonnet-20241022',
      temperature: 0.7,
      maxTokens: 4000,
      thinkingEnabled: true,
      thinkingBudget: 16000
    },
    isLoading: false,
    error: null
  });

  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [settingsOpen, setSettingsOpen] = useState(false);

  // Load saved data on mount
  useEffect(() => {
    const savedSessions = localStorage.getItem('hocus_opus_sessions');
    const savedCurrentSession = localStorage.getItem('hocus_opus_current_session');
    const savedConfig = localStorage.getItem('hocus_opus_config');

    if (savedSessions) {
      const sessions = JSON.parse(savedSessions);
      setState(prev => ({ ...prev, sessions }));
    }

    if (savedCurrentSession) {
      const currentSession = JSON.parse(savedCurrentSession);
      setState(prev => ({ ...prev, currentSession }));
    }

    if (savedConfig) {
      const config = JSON.parse(savedConfig);
      setState(prev => ({ ...prev, config }));
    }

    // Show settings if no API key is configured
    if (!claudeAPI.isConfigured()) {
      setSettingsOpen(true);
    }
  }, []);

  // Save data when state changes
  useEffect(() => {
    localStorage.setItem('hocus_opus_sessions', JSON.stringify(state.sessions));
  }, [state.sessions]);

  useEffect(() => {
    if (state.currentSession) {
      localStorage.setItem('hocus_opus_current_session', JSON.stringify(state.currentSession));
    }
  }, [state.currentSession]);

  useEffect(() => {
    localStorage.setItem('hocus_opus_config', JSON.stringify(state.config));
  }, [state.config]);

  const createNewSession = (): ChatSession => {
    const newSession: ChatSession = {
      id: Date.now().toString(),
      title: 'New Conversation',
      messages: [],
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return newSession;
  };

  const handleNewChat = () => {
    const newSession = createNewSession();
    setState(prev => ({
      ...prev,
      currentSession: newSession,
      sessions: [newSession, ...prev.sessions]
    }));
  };

  const handleSendMessage = async (content: string) => {
    if (!claudeAPI.isConfigured()) {
      setSettingsOpen(true);
      return;
    }

    let session = state.currentSession;
    if (!session) {
      session = createNewSession();
      setState(prev => ({
        ...prev,
        currentSession: session,
        sessions: [session!, ...prev.sessions]
      }));
    }

    const userMessage: Message = {
      id: Date.now().toString(),
      role: 'user',
      content,
      timestamp: new Date()
    };

    // Add user message immediately
    const updatedMessages = [...session.messages, userMessage];
    const updatedSession = {
      ...session,
      messages: updatedMessages,
      title: session.messages.length === 0 ? content.slice(0, 50) + '...' : session.title,
      updatedAt: new Date()
    };

    setState(prev => ({
      ...prev,
      currentSession: updatedSession,
      sessions: prev.sessions.map(s => s.id === updatedSession.id ? updatedSession : s),
      isLoading: true,
      error: null
    }));

    try {
      const response = await claudeAPI.sendMessage(updatedMessages, state.config);

      const assistantMessage: Message = {
        id: (Date.now() + 1).toString(),
        role: 'assistant',
        content: response.content,
        timestamp: new Date(),
        thinking: response.thinking
      };

      const finalMessages = [...updatedMessages, assistantMessage];
      const finalSession = {
        ...updatedSession,
        messages: finalMessages,
        updatedAt: new Date()
      };

      setState(prev => ({
        ...prev,
        currentSession: finalSession,
        sessions: prev.sessions.map(s => s.id === finalSession.id ? finalSession : s),
        isLoading: false
      }));

    } catch (error) {
      setState(prev => ({
        ...prev,
        isLoading: false,
        error: error instanceof Error ? error.message : 'Unknown error occurred'
      }));
    }
  };

  const handleSessionSelect = (session: ChatSession) => {
    setState(prev => ({ ...prev, currentSession: session }));
    setSidebarOpen(false);
  };

  const handleDeleteSession = (sessionId: string) => {
    setState(prev => {
      const newSessions = prev.sessions.filter(s => s.id !== sessionId);
      const newCurrentSession = prev.currentSession?.id === sessionId ? null : prev.currentSession;
      return {
        ...prev,
        sessions: newSessions,
        currentSession: newCurrentSession
      };
    });
  };

  const handleConfigChange = (config: ClaudeConfig) => {
    setState(prev => ({ ...prev, config }));
  };

  return (
    <div className="min-h-screen bg-sanctuary-900 text-sanctuary-100">
      <Sidebar
        config={state.config}
        onConfigChange={handleConfigChange}
        sessions={state.sessions}
        currentSession={state.currentSession}
        onSessionSelect={handleSessionSelect}
        onDeleteSession={handleDeleteSession}
        isOpen={sidebarOpen}
        onToggle={() => setSidebarOpen(!sidebarOpen)}
      />

      <div className={`transition-all duration-300 ${sidebarOpen ? 'ml-80' : 'ml-0'}`}>
        <div className="flex flex-col h-screen">
          <Header
            onNewChat={handleNewChat}
            onSettings={() => setSettingsOpen(true)}
          />

          {state.error && (
            <div className="mx-6 mb-4 p-4 bg-red-900/50 border border-red-500/50 rounded-lg text-red-200">
              <div className="flex items-center space-x-2">
                <span>⚠️</span>
                <span>{state.error}</span>
              </div>
            </div>
          )}

          <ChatLog
            messages={state.currentSession?.messages || []}
            isLoading={state.isLoading}
          />

          <InputBar
            onSendMessage={handleSendMessage}
            isLoading={state.isLoading}
            disabled={!claudeAPI.isConfigured()}
          />
        </div>
      </div>

      <SettingsModal
        isOpen={settingsOpen}
        onClose={() => setSettingsOpen(false)}
      />
    </div>
  );
}

export default App;
