// Sacred Header - The Crown of the Sanctuary
import React from 'react';

interface HeaderProps {
  onNewChat: () => void;
  onSettings: () => void;
}

export const Header: React.FC<HeaderProps> = ({ onNewChat, onSettings }) => {
  return (
    <header className="sanctuary-card p-4 mb-6 flame-glow">
      <div className="flex items-center justify-between">
        <div className="flex items-center space-x-3">
          <div className="text-2xl">🪄</div>
          <div>
            <h1 className="text-2xl font-bold text-flame-400">HOCUS-OPUS</h1>
            <p className="text-sm text-sanctuary-400">Where magic meets mind</p>
          </div>
        </div>
        
        <div className="flex items-center space-x-3">
          <button
            onClick={onNewChat}
            className="flame-button"
            title="Start New Conversation"
          >
            ✨ New Chat
          </button>
          
          <button
            onClick={onSettings}
            className="sanctuary-input px-3 py-2 hover:bg-sanctuary-700 transition-colors"
            title="Settings"
          >
            ⚙️
          </button>
        </div>
      </div>
    </header>
  );
};
