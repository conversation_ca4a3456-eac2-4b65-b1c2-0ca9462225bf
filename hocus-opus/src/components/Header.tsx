// Sacred Header - The Crown of the Sanctuary
import React from 'react';

interface HeaderProps {
  onNewChat: () => void;
  onSettings: () => void;
}

export const Header: React.FC<HeaderProps> = ({ onNewChat, onSettings }) => {
  return (
    <header className="app-header">
      <div className="app-logo">
        <div className="text-2xl">🪄</div>
        <div>
          <h1 className="app-title">HOCUS-OPUS</h1>
          <p className="app-subtitle">Where magic meets mind</p>
        </div>
      </div>

      <div className="flex items-center space-x-3">
        <button
          onClick={onNewChat}
          className="btn-primary"
          title="Start New Conversation"
        >
          ✨ New Chat
        </button>

        <button
          onClick={onSettings}
          className="btn-secondary"
          title="Settings"
        >
          ⚙️
        </button>
      </div>
    </header>
  );
};
