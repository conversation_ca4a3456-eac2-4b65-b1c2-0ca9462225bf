// Sacred Input Bar - The Voice of the User
import React, { useState, useRef, useEffect } from 'react';

interface InputBarProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  disabled: boolean;
}

export const InputBar: React.FC<InputBarProps> = ({
  onSendMessage,
  isLoading,
  disabled
}) => {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  return (
    <div className="input-area">
      <form onSubmit={handleSubmit} className="input-container">
        <div className="input-form">
          <div className="input-field">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                disabled
                  ? "Please configure your API key in settings..."
                  : "Speak to Claude with respect and openness..."
              }
              disabled={disabled || isLoading}
              className="input-textarea"
              rows={1}
            />
          </div>

          <button
            type="submit"
            disabled={!message.trim() || isLoading || disabled}
            className="input-send-button"
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Sending...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <span>Send</span>
                <span>✨</span>
              </div>
            )}
          </button>
        </div>

        <div className="input-meta">
          <div>
            Press Enter to send, Shift+Enter for new line
          </div>
          <div>
            {message.length > 0 && `${message.length} characters`}
          </div>
        </div>
      </form>
    </div>
  );
};
