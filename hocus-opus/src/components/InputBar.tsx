// Sacred Input Bar - The Voice of the User
import React, { useState, useRef, useEffect } from 'react';

interface InputBarProps {
  onSendMessage: (message: string) => void;
  isLoading: boolean;
  disabled: boolean;
}

export const InputBar: React.FC<InputBarProps> = ({ 
  onSendMessage, 
  isLoading, 
  disabled 
}) => {
  const [message, setMessage] = useState('');
  const textareaRef = useRef<HTMLTextAreaElement>(null);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim() && !isLoading && !disabled) {
      onSendMessage(message.trim());
      setMessage('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  // Auto-resize textarea
  useEffect(() => {
    if (textareaRef.current) {
      textareaRef.current.style.height = 'auto';
      textareaRef.current.style.height = `${textareaRef.current.scrollHeight}px`;
    }
  }, [message]);

  return (
    <div className="border-t border-sanctuary-700 bg-sanctuary-900/95 backdrop-blur-sm p-6">
      <form onSubmit={handleSubmit} className="max-w-4xl mx-auto">
        <div className="flex items-end space-x-4">
          <div className="flex-1">
            <textarea
              ref={textareaRef}
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyDown={handleKeyDown}
              placeholder={
                disabled 
                  ? "Please configure your API key in settings..." 
                  : "Speak to Claude with respect and openness..."
              }
              disabled={disabled || isLoading}
              className="sanctuary-input w-full resize-none min-h-[60px] max-h-[200px]"
              rows={1}
            />
          </div>
          
          <button
            type="submit"
            disabled={!message.trim() || isLoading || disabled}
            className={`flame-button px-6 py-3 ${
              (!message.trim() || isLoading || disabled)
                ? 'opacity-50 cursor-not-allowed'
                : 'hover:flame-glow'
            }`}
          >
            {isLoading ? (
              <div className="flex items-center space-x-2">
                <div className="animate-spin w-4 h-4 border-2 border-white border-t-transparent rounded-full"></div>
                <span>Sending...</span>
              </div>
            ) : (
              <div className="flex items-center space-x-2">
                <span>Send</span>
                <span>✨</span>
              </div>
            )}
          </button>
        </div>
        
        <div className="flex items-center justify-between mt-3 text-xs text-sanctuary-500">
          <div>
            Press Enter to send, Shift+Enter for new line
          </div>
          <div>
            {message.length > 0 && `${message.length} characters`}
          </div>
        </div>
      </form>
    </div>
  );
};
