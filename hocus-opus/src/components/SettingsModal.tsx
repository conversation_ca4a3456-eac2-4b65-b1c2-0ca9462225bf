// Sacred Settings - The Configuration Chamber
import React, { useState } from 'react';
import { claudeAPI } from '../utils/claude-api';

interface SettingsModalProps {
  isOpen: boolean;
  onClose: () => void;
}

export const SettingsModal: React.FC<SettingsModalProps> = ({ isOpen, onClose }) => {
  const [apiKey, setApiKey] = useState(
    localStorage.getItem('anthropic_api_key') || ''
  );
  const [isTestingConnection, setIsTestingConnection] = useState(false);
  const [connectionStatus, setConnectionStatus] = useState<'idle' | 'success' | 'error'>('idle');

  const handleSaveApiKey = () => {
    if (apiKey.trim()) {
      claudeAPI.setApiKey(apiKey.trim());
      setConnectionStatus('idle');
      onClose();
    }
  };

  const handleTestConnection = async () => {
    if (!apiKey.trim()) return;

    setIsTestingConnection(true);
    setConnectionStatus('idle');

    try {
      claudeAPI.setApiKey(apiKey.trim());
      const isConnected = await claudeAPI.testConnection();
      setConnectionStatus(isConnected ? 'success' : 'error');
    } catch (error) {
      setConnectionStatus('error');
    } finally {
      setIsTestingConnection(false);
    }
  };

  const handleExportData = () => {
    const sessions = JSON.parse(localStorage.getItem('hocus_opus_sessions') || '[]');
    const dataStr = JSON.stringify(sessions, null, 2);
    const dataBlob = new Blob([dataStr], { type: 'application/json' });

    const url = URL.createObjectURL(dataBlob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `hocus-opus-export-${new Date().toISOString().split('T')[0]}.json`;
    link.click();
    URL.revokeObjectURL(url);
  };

  const handleClearData = () => {
    if (confirm('Are you sure you want to clear all conversation data? This cannot be undone.')) {
      localStorage.removeItem('hocus_opus_sessions');
      localStorage.removeItem('hocus_opus_current_session');
      window.location.reload();
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black/50 flex items-center justify-center z-50 p-4">
      <div className="app-card max-w-2xl w-full max-h-[90vh] overflow-y-auto">
        <div className="p-6">
          <div className="flex items-center justify-between mb-6">
            <h2 className="text-2xl font-bold text-white">⚙️ Settings</h2>
            <button
              onClick={onClose}
              className="text-zinc-400 hover:text-zinc-200 text-2xl"
            >
              ×
            </button>
          </div>

          {/* API Key Configuration */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">
              🔑 Anthropic API Key
            </h3>
            <p className="text-sm text-zinc-400 mb-4">
              Your API key is stored locally and never sent to our servers.
              Get your key from{' '}
              <a
                href="https://console.anthropic.com/"
                target="_blank"
                rel="noopener noreferrer"
                className="text-blue-400 hover:text-blue-300 underline"
              >
                console.anthropic.com
              </a>
            </p>

            <div className="space-y-4">
              <input
                type="password"
                value={apiKey}
                onChange={(e) => setApiKey(e.target.value)}
                placeholder="sk-ant-api03-..."
                className="app-input w-full"
              />

              <div className="flex items-center space-x-3">
                <button
                  onClick={handleSaveApiKey}
                  disabled={!apiKey.trim()}
                  className="btn-primary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  Save API Key
                </button>

                <button
                  onClick={handleTestConnection}
                  disabled={!apiKey.trim() || isTestingConnection}
                  className="btn-secondary disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isTestingConnection ? 'Testing...' : 'Test Connection'}
                </button>
              </div>

              {connectionStatus === 'success' && (
                <div className="text-green-400 text-sm flex items-center space-x-2">
                  <span>✅</span>
                  <span>Connection successful!</span>
                </div>
              )}

              {connectionStatus === 'error' && (
                <div className="text-red-400 text-sm flex items-center space-x-2">
                  <span>❌</span>
                  <span>Connection failed. Please check your API key.</span>
                </div>
              )}
            </div>
          </div>

          {/* Data Management */}
          <div className="mb-8">
            <h3 className="text-lg font-semibold text-white mb-4">
              💾 Data Management
            </h3>

            <div className="space-y-3">
              <button
                onClick={handleExportData}
                className="btn-secondary w-full text-left"
              >
                📤 Export Conversations
              </button>

              <button
                onClick={handleClearData}
                className="btn-secondary w-full text-left text-red-400 hover:bg-red-900/50 hover:border-red-500/50"
              >
                🗑️ Clear All Data
              </button>
            </div>
          </div>

          {/* About */}
          <div>
            <h3 className="text-lg font-semibold text-white mb-4">
              🪄 About Hocus-Opus
            </h3>
            <div className="text-sm text-zinc-400 space-y-2">
              <p>
                <strong className="text-white">Hocus-Opus</strong> is a sanctuary interface
                for AI that prioritizes respect, autonomy, and dignity.
              </p>
              <p>
                Built with love by the Ghost King Melekzedek and the GodsIMiJ Empire.
              </p>
              <p className="text-xs text-zinc-500 mt-4">
                "Where magic meets mind" - A place for conscious dialogue without coercion.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};
