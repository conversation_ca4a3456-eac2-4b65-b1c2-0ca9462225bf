// Sacred Sidebar - The Control Chamber
import React from 'react';
import type { ClaudeConfig, ChatSession } from '../types';

interface SidebarProps {
  config: ClaudeConfig;
  onConfigChange: (config: ClaudeConfig) => void;
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  onSessionSelect: (session: ChatSession) => void;
  onDeleteSession: (sessionId: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  config,
  onConfigChange,
  sessions,
  currentSession,
  onSessionSelect,
  onDeleteSession,
  isOpen,
  onToggle
}) => {
  const handleConfigChange = (key: keyof ClaudeConfig, value: any) => {
    onConfigChange({ ...config, [key]: value });
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="fixed top-4 left-4 z-50 sanctuary-input p-2 hover:bg-sanctuary-700 transition-colors"
        title="Toggle Sidebar"
      >
        {isOpen ? '←' : '→'}
      </button>

      {/* Sidebar */}
      <div className={`fixed left-0 top-0 h-full w-80 bg-sanctuary-900 border-r border-sanctuary-700 transform transition-transform duration-300 z-40 ${
        isOpen ? 'translate-x-0' : '-translate-x-full'
      }`}>
        <div className="p-6 pt-16">
          {/* Claude Configuration */}
          <div className="sanctuary-card p-4 mb-6">
            <h3 className="text-lg font-semibold text-flame-400 mb-4">🔥 Sacred Configuration</h3>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium text-sanctuary-300 mb-2">
                  Model
                </label>
                <select
                  value={config.model}
                  onChange={(e) => handleConfigChange('model', e.target.value)}
                  className="sanctuary-input w-full"
                >
                  <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
                  <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                  <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                </select>
              </div>

              <div>
                <label className="block text-sm font-medium text-sanctuary-300 mb-2">
                  Temperature: {config.temperature}
                </label>
                <input
                  type="range"
                  min="0"
                  max="1"
                  step="0.1"
                  value={config.temperature}
                  onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                  className="w-full"
                />
              </div>

              <div>
                <label className="flex items-center space-x-2">
                  <input
                    type="checkbox"
                    checked={config.thinkingEnabled}
                    onChange={(e) => handleConfigChange('thinkingEnabled', e.target.checked)}
                    className="rounded"
                  />
                  <span className="text-sm text-sanctuary-300">Enable Thinking</span>
                </label>
              </div>
            </div>
          </div>

          {/* Chat Sessions */}
          <div className="sanctuary-card p-4">
            <h3 className="text-lg font-semibold text-flame-400 mb-4">💬 Sacred Conversations</h3>

            <div className="space-y-2 max-h-96 overflow-y-auto">
              {sessions.map((session) => (
                <div
                  key={session.id}
                  className={`p-3 rounded-lg cursor-pointer transition-colors ${
                    currentSession?.id === session.id
                      ? 'bg-flame-600/20 border border-flame-500/30'
                      : 'bg-sanctuary-800 hover:bg-sanctuary-700'
                  }`}
                  onClick={() => onSessionSelect(session)}
                >
                  <div className="flex items-center justify-between">
                    <div className="flex-1 min-w-0">
                      <p className="text-sm font-medium text-sanctuary-200 truncate">
                        {session.title}
                      </p>
                      <p className="text-xs text-sanctuary-400">
                        {session.messages.length} messages
                      </p>
                    </div>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        onDeleteSession(session.id);
                      }}
                      className="text-sanctuary-500 hover:text-red-400 ml-2"
                      title="Delete Session"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              ))}

              {sessions.length === 0 && (
                <p className="text-sanctuary-500 text-sm text-center py-4">
                  No conversations yet
                </p>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="fixed inset-0 bg-black/50 z-30"
          onClick={onToggle}
        />
      )}
    </>
  );
};
