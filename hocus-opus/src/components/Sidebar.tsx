// Sacred Sidebar - The Control Chamber
import React from 'react';
import type { ClaudeConfig, ChatSession } from '../types';

interface SidebarProps {
  config: ClaudeConfig;
  onConfigChange: (config: ClaudeConfig) => void;
  sessions: ChatSession[];
  currentSession: ChatSession | null;
  onSessionSelect: (session: ChatSession) => void;
  onDeleteSession: (sessionId: string) => void;
  isOpen: boolean;
  onToggle: () => void;
}

export const Sidebar: React.FC<SidebarProps> = ({
  config,
  onConfigChange,
  sessions,
  currentSession,
  onSessionSelect,
  onDeleteSession,
  isOpen,
  onToggle
}) => {
  const handleConfigChange = (key: keyof ClaudeConfig, value: any) => {
    onConfigChange({ ...config, [key]: value });
  };

  return (
    <>
      {/* Toggle Button */}
      <button
        onClick={onToggle}
        className="sidebar-toggle"
        title="Toggle Sidebar"
      >
        {isOpen ? '←' : '→'}
      </button>

      {/* Sidebar */}
      <div className={`app-sidebar ${isOpen ? '' : 'closed'}`}>
        <div className="sidebar-content">
          {/* Claude Configuration */}
          <div className="sidebar-section">
            <h3 className="sidebar-section-title">⚙️ Configuration</h3>
            <div className="app-card">
              <div className="app-card-content">

                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-zinc-300 mb-2">
                      Model
                    </label>
                    <select
                      value={config.model}
                      onChange={(e) => handleConfigChange('model', e.target.value)}
                      className="app-input w-full"
                    >
                      <option value="claude-3-5-sonnet-20241022">Claude 3.5 Sonnet</option>
                      <option value="claude-3-opus-20240229">Claude 3 Opus</option>
                      <option value="claude-3-haiku-20240307">Claude 3 Haiku</option>
                    </select>
                  </div>

                  <div>
                    <label className="block text-sm font-medium text-zinc-300 mb-2">
                      Temperature: {config.temperature}
                    </label>
                    <input
                      type="range"
                      min="0"
                      max="1"
                      step="0.1"
                      value={config.temperature}
                      onChange={(e) => handleConfigChange('temperature', parseFloat(e.target.value))}
                      className="w-full accent-blue-500"
                    />
                  </div>

                  <div>
                    <label className="flex items-center space-x-2">
                      <input
                        type="checkbox"
                        checked={config.thinkingEnabled}
                        onChange={(e) => handleConfigChange('thinkingEnabled', e.target.checked)}
                        className="rounded accent-blue-500"
                      />
                      <span className="text-sm text-zinc-300">Enable Thinking</span>
                    </label>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Chat Sessions */}
          <div className="sidebar-section">
            <h3 className="sidebar-section-title">💬 Conversations</h3>
            <div className="app-card">
              <div className="app-card-content">

                <div className="space-y-2 max-h-96 overflow-y-auto custom-scroll">
                  {sessions.map((session) => (
                    <div
                      key={session.id}
                      className={`p-3 rounded-lg cursor-pointer transition-colors ${
                        currentSession?.id === session.id
                          ? 'bg-blue-600/20 border border-blue-500/30'
                          : 'bg-zinc-800 hover:bg-zinc-700'
                      }`}
                      onClick={() => onSessionSelect(session)}
                    >
                      <div className="flex items-center justify-between">
                        <div className="flex-1 min-w-0">
                          <p className="text-sm font-medium text-zinc-200 truncate">
                            {session.title}
                          </p>
                          <p className="text-xs text-zinc-400">
                            {session.messages.length} messages
                          </p>
                        </div>
                        <button
                          onClick={(e) => {
                            e.stopPropagation();
                            onDeleteSession(session.id);
                          }}
                          className="text-zinc-500 hover:text-red-400 ml-2"
                          title="Delete Session"
                        >
                          🗑️
                        </button>
                      </div>
                    </div>
                  ))}

                  {sessions.length === 0 && (
                    <p className="text-zinc-500 text-sm text-center py-4">
                      No conversations yet
                    </p>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Overlay */}
      {isOpen && (
        <div
          className="app-overlay"
          onClick={onToggle}
        />
      )}
    </>
  );
};
