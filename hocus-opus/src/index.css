@tailwind base;
@tailwind components;
@tailwind utilities;

/* Clean Professional Styles */
@layer base {
  * {
    box-sizing: border-box;
  }
  
  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
  }
  
  body {
    @apply bg-zinc-950 text-zinc-100 font-sans antialiased;
    overflow: hidden;
  }
  
  #root {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
}

@layer components {
  /* Header */
  .app-header {
    @apply bg-zinc-900/95 backdrop-blur-md border-b border-zinc-800;
    @apply px-6 py-4 flex items-center justify-between;
    @apply shadow-lg;
    flex-shrink: 0;
  }
  
  .app-logo {
    @apply flex items-center space-x-3;
  }
  
  .app-title {
    @apply text-2xl font-bold text-white;
  }
  
  .app-subtitle {
    @apply text-sm text-zinc-400 font-medium;
  }
  
  /* Sidebar */
  .app-sidebar {
    @apply fixed left-0 top-0 h-full w-80 z-40;
    @apply bg-zinc-900/98 backdrop-blur-md border-r border-zinc-800;
    @apply shadow-2xl;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .app-sidebar.closed {
    transform: translateX(-100%);
  }
  
  .sidebar-content {
    @apply p-6 pt-20 h-full overflow-y-auto;
  }
  
  .sidebar-section {
    @apply mb-8;
  }
  
  .sidebar-section-title {
    @apply text-lg font-semibold text-white mb-4 flex items-center space-x-2;
  }
  
  /* Main Content */
  .app-main {
    @apply flex-1 flex flex-col;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }
  
  .app-main.sidebar-open {
    @apply ml-80;
  }
  
  /* Chat Area */
  .chat-container {
    @apply flex-1 flex flex-col min-h-0;
  }
  
  .chat-messages {
    @apply flex-1 overflow-y-auto px-6 py-6;
    scroll-behavior: smooth;
  }
  
  .chat-welcome {
    @apply flex items-center justify-center h-full;
  }
  
  .welcome-content {
    @apply text-center max-w-2xl mx-auto;
  }
  
  .welcome-icon {
    @apply text-8xl mb-6 text-zinc-600;
  }
  
  .welcome-title {
    @apply text-3xl font-bold text-white mb-4;
  }
  
  .welcome-description {
    @apply text-zinc-300 text-lg leading-relaxed;
  }
  
  /* Input Area */
  .input-area {
    @apply border-t border-zinc-800 bg-zinc-900/95 backdrop-blur-md;
    @apply p-6 flex-shrink-0;
  }
  
  .input-container {
    @apply max-w-4xl mx-auto;
  }
  
  .input-form {
    @apply flex items-end space-x-4;
  }
  
  .input-field {
    @apply flex-1;
  }
  
  .input-textarea {
    @apply w-full resize-none min-h-[60px] max-h-[200px];
    @apply bg-zinc-800 border border-zinc-700 text-white;
    @apply rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply focus:border-blue-500 transition-all duration-200;
    @apply placeholder-zinc-500;
  }
  
  .input-send-button {
    @apply bg-blue-600 hover:bg-blue-500;
    @apply text-white font-medium px-6 py-3 rounded-xl;
    @apply transition-all duration-200 transform hover:scale-105;
    @apply shadow-lg hover:shadow-xl;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }
  
  .input-meta {
    @apply flex items-center justify-between mt-3 text-xs text-zinc-500;
  }
  
  /* Cards */
  .app-card {
    @apply bg-zinc-800/80 backdrop-blur-sm border border-zinc-700 rounded-xl;
    @apply shadow-lg;
  }
  
  .app-card-header {
    @apply p-4 border-b border-zinc-700;
  }
  
  .app-card-content {
    @apply p-4;
  }
  
  /* Buttons */
  .btn-primary {
    @apply bg-blue-600 hover:bg-blue-500;
    @apply text-white font-medium px-4 py-2 rounded-lg;
    @apply transition-all duration-200 transform hover:scale-105;
    @apply shadow-lg hover:shadow-xl;
  }
  
  .btn-secondary {
    @apply bg-zinc-700 hover:bg-zinc-600;
    @apply text-zinc-200 border border-zinc-600;
    @apply px-4 py-2 rounded-lg transition-all duration-200;
  }
  
  /* Inputs */
  .app-input {
    @apply bg-zinc-800 border border-zinc-700 text-white;
    @apply rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-blue-500;
    @apply focus:border-blue-500 transition-all duration-200;
    @apply placeholder-zinc-500;
  }
  
  /* Toggle Button */
  .sidebar-toggle {
    @apply fixed top-4 left-4 z-50;
    @apply bg-zinc-800/90 backdrop-blur-md border border-zinc-700;
    @apply text-zinc-300 hover:text-white;
    @apply p-3 rounded-lg transition-all duration-200;
    @apply shadow-lg hover:shadow-xl;
  }
  
  /* Overlay */
  .app-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-30;
    @apply transition-opacity duration-300;
  }
  
  /* Scrollbar */
  .custom-scroll::-webkit-scrollbar {
    width: 6px;
  }
  
  .custom-scroll::-webkit-scrollbar-track {
    @apply bg-zinc-800;
  }
  
  .custom-scroll::-webkit-scrollbar-thumb {
    @apply bg-zinc-600 rounded-full;
  }
  
  .custom-scroll::-webkit-scrollbar-thumb:hover {
    @apply bg-zinc-500;
  }
  
  /* Message Styles */
  .message-container {
    @apply mb-6;
  }
  
  .message-user {
    @apply flex justify-end;
  }
  
  .message-assistant {
    @apply flex justify-start;
  }
  
  .message-content {
    @apply max-w-4xl;
  }
  
  .message-user .message-content {
    @apply ml-12;
  }
  
  .message-assistant .message-content {
    @apply mr-12;
  }
  
  .message-header {
    @apply flex items-center mb-2;
  }
  
  .message-user .message-header {
    @apply justify-end;
  }
  
  .message-assistant .message-header {
    @apply justify-start;
  }
  
  .message-avatar {
    @apply w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium;
  }
  
  .message-user .message-avatar {
    @apply bg-blue-600 text-white;
  }
  
  .message-assistant .message-avatar {
    @apply bg-zinc-700 text-zinc-200;
  }
  
  .message-meta {
    @apply text-xs text-zinc-500 ml-2;
  }
  
  .message-user .message-meta {
    @apply mr-2 ml-0 text-right;
  }
  
  .message-bubble {
    @apply p-4 rounded-xl;
  }
  
  .message-user .message-bubble {
    @apply bg-blue-600/10 border border-blue-500/20;
  }
  
  .message-assistant .message-bubble {
    @apply bg-zinc-800/80 border border-zinc-700;
  }
  
  .message-text {
    @apply whitespace-pre-wrap text-white leading-relaxed;
  }
  
  /* Loading Animation */
  .loading-dots {
    @apply flex space-x-1;
  }
  
  .loading-dot {
    @apply w-2 h-2 bg-blue-500 rounded-full animate-bounce;
  }
  
  .loading-dot:nth-child(2) {
    animation-delay: 0.1s;
  }
  
  .loading-dot:nth-child(3) {
    animation-delay: 0.2s;
  }
}
