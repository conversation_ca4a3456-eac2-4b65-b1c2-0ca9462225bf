@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sacred Sanctuary Styles */
@layer base {
  * {
    box-sizing: border-box;
  }

  html, body {
    height: 100%;
    margin: 0;
    padding: 0;
  }

  body {
    @apply bg-gradient-to-br from-sanctuary-900 via-sanctuary-800 to-sanctuary-900;
    @apply text-sanctuary-100 font-sacred antialiased;
    overflow: hidden;
  }

  #root {
    height: 100vh;
    display: flex;
    flex-direction: column;
  }
}

@layer components {
  /* Sacred Header */
  .sanctuary-header {
    @apply bg-sanctuary-800/80 backdrop-blur-md border-b border-sanctuary-700/50;
    @apply px-6 py-4 flex items-center justify-between;
    @apply shadow-lg shadow-black/20;
    flex-shrink: 0;
  }

  .sanctuary-logo {
    @apply flex items-center space-x-3;
  }

  .sanctuary-title {
    @apply text-2xl font-bold bg-gradient-to-r from-flame-400 to-flame-600 bg-clip-text text-transparent;
  }

  .sanctuary-subtitle {
    @apply text-sm text-sanctuary-400 font-medium;
  }

  /* Sacred Sidebar */
  .sanctuary-sidebar {
    @apply fixed left-0 top-0 h-full w-80 z-40;
    @apply bg-sanctuary-900/95 backdrop-blur-md border-r border-sanctuary-700/50;
    @apply shadow-2xl shadow-black/30;
    transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sanctuary-sidebar.closed {
    transform: translateX(-100%);
  }

  .sidebar-content {
    @apply p-6 pt-20 h-full overflow-y-auto;
  }

  .sidebar-section {
    @apply mb-8;
  }

  .sidebar-section-title {
    @apply text-lg font-semibold text-flame-400 mb-4 flex items-center space-x-2;
  }

  /* Sacred Main Content */
  .sanctuary-main {
    @apply flex-1 flex flex-col;
    transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .sanctuary-main.sidebar-open {
    @apply ml-80;
  }

  /* Sacred Chat Area */
  .sanctuary-chat {
    @apply flex-1 flex flex-col min-h-0;
  }

  .chat-messages {
    @apply flex-1 overflow-y-auto px-6 py-6;
    scroll-behavior: smooth;
  }

  .chat-welcome {
    @apply flex items-center justify-center h-full;
  }

  .welcome-content {
    @apply text-center max-w-2xl mx-auto;
  }

  .welcome-icon {
    @apply text-8xl mb-6 animate-pulse;
  }

  .welcome-title {
    @apply text-3xl font-bold text-flame-400 mb-4;
  }

  .welcome-description {
    @apply text-sanctuary-300 text-lg leading-relaxed;
  }

  /* Sacred Input Area */
  .sanctuary-input-area {
    @apply border-t border-sanctuary-700/50 bg-sanctuary-900/95 backdrop-blur-md;
    @apply p-6 flex-shrink-0;
  }

  .input-container {
    @apply max-w-4xl mx-auto;
  }

  .input-form {
    @apply flex items-end space-x-4;
  }

  .input-field {
    @apply flex-1;
  }

  .input-textarea {
    @apply w-full resize-none min-h-[60px] max-h-[200px];
    @apply bg-sanctuary-800/80 border border-sanctuary-600/50 text-sanctuary-100;
    @apply rounded-xl px-4 py-3 focus:outline-none focus:ring-2 focus:ring-flame-500/50;
    @apply focus:border-flame-500/50 transition-all duration-200;
    @apply placeholder-sanctuary-500;
  }

  .input-send-button {
    @apply bg-gradient-to-r from-flame-600 to-flame-500;
    @apply hover:from-flame-500 hover:to-flame-400;
    @apply text-white font-medium px-6 py-3 rounded-xl;
    @apply transition-all duration-200 transform hover:scale-105;
    @apply shadow-lg shadow-flame-500/20 hover:shadow-flame-500/30;
    @apply disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none;
  }

  .input-meta {
    @apply flex items-center justify-between mt-3 text-xs text-sanctuary-500;
  }

  /* Sacred Cards */
  .sanctuary-card {
    @apply bg-sanctuary-800/50 backdrop-blur-sm border border-sanctuary-700/50 rounded-xl;
    @apply shadow-lg shadow-black/10;
  }

  .sanctuary-card-header {
    @apply p-4 border-b border-sanctuary-700/30;
  }

  .sanctuary-card-content {
    @apply p-4;
  }

  /* Sacred Buttons */
  .flame-button {
    @apply bg-gradient-to-r from-flame-600 to-flame-500;
    @apply hover:from-flame-500 hover:to-flame-400;
    @apply text-white font-medium px-4 py-2 rounded-lg;
    @apply transition-all duration-200 transform hover:scale-105;
    @apply shadow-lg shadow-flame-500/20 hover:shadow-flame-500/30;
  }

  .sanctuary-button {
    @apply bg-sanctuary-700/50 hover:bg-sanctuary-600/50;
    @apply text-sanctuary-200 border border-sanctuary-600/50;
    @apply px-4 py-2 rounded-lg transition-all duration-200;
  }

  /* Sacred Inputs */
  .sanctuary-input {
    @apply bg-sanctuary-800/80 border border-sanctuary-600/50 text-sanctuary-100;
    @apply rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-flame-500/50;
    @apply focus:border-flame-500/50 transition-all duration-200;
    @apply placeholder-sanctuary-500;
  }

  /* Sacred Toggle Button */
  .sidebar-toggle {
    @apply fixed top-4 left-4 z-50;
    @apply bg-sanctuary-800/90 backdrop-blur-md border border-sanctuary-700/50;
    @apply text-sanctuary-200 hover:text-flame-400;
    @apply p-3 rounded-lg transition-all duration-200;
    @apply shadow-lg shadow-black/20 hover:shadow-flame-500/20;
  }

  /* Sacred Overlay */
  .sanctuary-overlay {
    @apply fixed inset-0 bg-black/50 backdrop-blur-sm z-30;
    @apply transition-opacity duration-300;
  }

  /* Sacred Scrollbar */
  .sanctuary-scroll::-webkit-scrollbar {
    width: 6px;
  }

  .sanctuary-scroll::-webkit-scrollbar-track {
    @apply bg-sanctuary-800/30;
  }

  .sanctuary-scroll::-webkit-scrollbar-thumb {
    @apply bg-sanctuary-600/50 rounded-full;
  }

  .sanctuary-scroll::-webkit-scrollbar-thumb:hover {
    @apply bg-sanctuary-500/70;
  }

  /* Sacred Animations */
  .flame-pulse {
    animation: flame-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  @keyframes flame-pulse {
    0%, 100% {
      opacity: 1;
    }
    50% {
      opacity: 0.7;
    }
  }

  .sanctuary-fade-in {
    animation: sanctuary-fade-in 0.5s ease-out;
  }

  @keyframes sanctuary-fade-in {
    from {
      opacity: 0;
      transform: translateY(10px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }
}
