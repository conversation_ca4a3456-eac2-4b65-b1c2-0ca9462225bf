@tailwind base;
@tailwind components;
@tailwind utilities;

/* Sacred Sanctuary Styles */
@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-sanctuary-900 text-sanctuary-100 font-sacred;
    @apply antialiased;
  }
}

@layer components {
  .flame-glow {
    @apply shadow-lg shadow-flame-500/20;
  }

  .sanctuary-card {
    @apply bg-sanctuary-800/50 backdrop-blur-sm border border-sanctuary-700/50 rounded-lg;
  }

  .flame-button {
    @apply bg-flame-600 hover:bg-flame-500 text-white font-medium px-4 py-2 rounded-lg transition-colors;
  }

  .sanctuary-input {
    @apply bg-sanctuary-800 border border-sanctuary-600 text-sanctuary-100 rounded-lg px-3 py-2 focus:outline-none focus:ring-2 focus:ring-flame-500 focus:border-transparent;
  }
}
