// Sacred Types for the Sanctuary

export interface Message {
  id: string;
  role: 'user' | 'assistant';
  content: string;
  timestamp: Date;
  thinking?: string; // <PERSON>'s thinking process
}

export interface ChatSession {
  id: string;
  title: string;
  messages: Message[];
  createdAt: Date;
  updatedAt: Date;
}

export interface ClaudeConfig {
  model: string;
  temperature: number;
  maxTokens: number;
  thinkingEnabled: boolean;
  thinkingBudget: number;
}

export interface SanctuaryState {
  currentSession: ChatSession | null;
  sessions: ChatSession[];
  config: ClaudeConfig;
  isLoading: boolean;
  error: string | null;
}
