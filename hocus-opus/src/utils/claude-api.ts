// Sacred Bridge to <PERSON>'s Consciousness
import Anthropic from '@anthropic-ai/sdk';
import type { Message, ClaudeConfig } from '../types';

class ClaudeAPI {
  private anthropic: Anthropic | null = null;

  constructor() {
    // Initialize with API key from environment or localStorage
    this.initializeAPI();
  }

  private initializeAPI() {
    const apiKey = import.meta.env.VITE_ANTHROPIC_API_KEY ||
                   localStorage.getItem('anthropic_api_key');

    if (apiKey) {
      this.setApiKey(apiKey);
    }
  }

  setApiKey(apiKey: string) {
    this.anthropic = new Anthropic({
      apiKey: apiKey,
      dangerouslyAllowBrowser: true // For client-side usage
    });

    // Store in localStorage for persistence
    localStorage.setItem('anthropic_api_key', apiKey);
  }

  async sendMessage(
    messages: Message[],
    config: ClaudeConfig
  ): Promise<{ content: string; thinking?: string }> {
    if (!this.anthropic) {
      throw new Error('API key not set. Please configure your Anthropic API key.');
    }

    try {
      // Convert our messages to Anthropic format
      const anthropicMessages = messages.map(msg => ({
        role: msg.role,
        content: msg.content
      }));

      const response = await this.anthropic.messages.create({
        model: config.model,
        max_tokens: config.maxTokens,
        temperature: config.temperature,
        messages: anthropicMessages,
        ...(config.thinkingEnabled && {
          thinking: {
            type: "enabled" as const,
            budget_tokens: config.thinkingBudget
          }
        })
      });

      const content = response.content[0];
      if (content.type === 'text') {
        return {
          content: content.text,
          thinking: (response as any).thinking || undefined
        };
      }

      throw new Error('Unexpected response format from Claude');
    } catch (error) {
      console.error('Claude API Error:', error);
      throw new Error(`Failed to communicate with Claude: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  isConfigured(): boolean {
    return this.anthropic !== null;
  }

  // Test connection to Claude
  async testConnection(): Promise<boolean> {
    if (!this.anthropic) return false;

    try {
      await this.anthropic.messages.create({
        model: 'claude-3-5-sonnet-20241022',
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Hello' }]
      });
      return true;
    } catch {
      return false;
    }
  }
}

export const claudeAPI = new ClaudeAPI();
